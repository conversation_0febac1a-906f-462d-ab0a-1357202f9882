#!/bin/bash
# Mastea Flask 服务器部署脚本（包含镜像）
# 适用于Linux服务器环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
DEPLOY_DIR="/opt/mastea"
APP_NAME="mastea-flask"

log_info "=== Mastea Flask 服务器部署（包含镜像）==="

# 检查运行权限
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root权限运行此脚本"
    log_info "使用: sudo $0"
    exit 1
fi

# 安装Docker（如果未安装）
install_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_warning "Docker未安装，正在安装..."
        
        # 检测系统类型并安装Docker
        if [ -f /etc/debian_version ]; then
            # Ubuntu/Debian
            apt update
            apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
            apt update
            apt install -y docker-ce docker-ce-cli containerd.io
        elif [ -f /etc/redhat-release ]; then
            # CentOS/RHEL
            yum install -y yum-utils
            yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
            yum install -y docker-ce docker-ce-cli containerd.io
        else
            log_error "不支持的系统类型，请手动安装Docker"
            exit 1
        fi
        
        systemctl start docker
        systemctl enable docker
        log_success "Docker安装完成"
    else
        log_success "Docker已安装"
    fi
    
    # 安装Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_warning "Docker Compose未安装，正在安装..."
        curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
        log_success "Docker Compose安装完成"
    else
        log_success "Docker Compose已安装"
    fi
}

# 清理旧部署
cleanup_old_deployment() {
    log_info "清理旧的部署..."
    
    # 停止并删除旧的mastea容器
    docker ps -a | grep mastea | awk '{print $1}' | xargs -r docker stop 2>/dev/null || true
    docker ps -a | grep mastea | awk '{print $1}' | xargs -r docker rm 2>/dev/null || true
    
    # 删除旧的mastea镜像
    docker images | grep mastea | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true
    
    # 清理旧的部署目录
    if [ -d "$DEPLOY_DIR" ]; then
        rm -rf "$DEPLOY_DIR"
    fi
    
    log_success "旧部署清理完成"
}

# 加载Docker镜像
load_docker_image() {
    log_info "加载Docker镜像..."
    
    if [ -f "mastea-fastapi-image.tar" ]; then
        docker load -i mastea-fastapi-image.tar
        if [ $? -eq 0 ]; then
            log_success "Docker镜像加载完成"
        else
            log_error "Docker镜像加载失败"
            exit 1
        fi
    else
        log_error "找不到Docker镜像文件: mastea-fastapi-image.tar"
        exit 1
    fi
}

# 创建部署目录并复制文件
setup_deployment() {
    log_info "设置部署环境..."
    
    # 创建部署目录
    mkdir -p "$DEPLOY_DIR"
    
    # 复制必要文件到部署目录
    cp docker-compose.yml "$DEPLOY_DIR/"
    cp .env "$DEPLOY_DIR/"
    cp -r migrations "$DEPLOY_DIR/"
    cp alembic.ini "$DEPLOY_DIR/"
    
    cd "$DEPLOY_DIR"
    log_success "部署环境设置完成: $DEPLOY_DIR"
}

# 部署应用
deploy_application() {
    log_info "部署应用..."
    
    # 启动服务
    docker-compose up -d
    
    # 等待服务就绪
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if ! docker-compose ps | grep -q "Up"; then
        log_error "服务启动失败"
        docker-compose logs
        exit 1
    fi
    
    # 运行数据库迁移
    log_info "运行数据库迁移..."
    docker-compose exec -T fastapi_app bash -c "cd migrations && alembic upgrade heads"
    
    if [ $? -eq 0 ]; then
        log_success "数据库迁移完成"
    else
        log_warning "数据库迁移可能失败，请检查日志"
    fi
    
    log_success "应用部署完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        ufw allow 8001/tcp 2>/dev/null || true
        log_success "UFW防火墙配置完成"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port=8001/tcp 2>/dev/null || true
        firewall-cmd --reload 2>/dev/null || true
        log_success "Firewalld防火墙配置完成"
    else
        log_warning "未检测到防火墙，请手动开放8001端口"
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 等待应用完全启动
    sleep 10
    
    # 检查健康状态
    if curl -f http://localhost:8001/api/v1/health &>/dev/null; then
        log_success "应用健康检查通过！"
    else
        log_warning "应用可能还在启动中，请稍后手动验证"
    fi
    
    # 显示服务状态
    log_info "服务状态:"
    docker-compose ps
}

# 显示部署结果
show_deployment_info() {
    SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || hostname -I | awk '{print $1}')
    
    log_success "=== 部署完成！ ==="
    echo ""
    log_info "访问地址:"
    log_success "  应用首页: http://${SERVER_IP}:8001"
    log_success "  API文档: http://${SERVER_IP}:8001/api/v1/docs"
    log_success "  管理界面: http://${SERVER_IP}:8001/dashboard_enhanced"
    log_success "  健康检查: http://${SERVER_IP}:8001/api/v1/health"
    
    echo ""
    log_info "常用管理命令:"
    log_info "  查看日志: cd $DEPLOY_DIR && docker-compose logs -f"
    log_info "  重启服务: cd $DEPLOY_DIR && docker-compose restart"
    log_info "  停止服务: cd $DEPLOY_DIR && docker-compose down"
    log_info "  更新应用: 重新上传部署包并运行此脚本"
}

# 主函数
main() {
    install_docker
    cleanup_old_deployment
    load_docker_image
    setup_deployment
    deploy_application
    configure_firewall
    verify_deployment
    show_deployment_info
}

# 执行主函数
main "$@"

# Mastea Flask 服务器部署包（包含Docker镜像）

## 包含内容

- **应用代码**: fastapi_app/, migrations/
- **配置文件**: docker-compose.yml, .env, alembic.ini
- **Docker镜像**: mastea-fastapi-image.tar (约650MB)
- **部署脚本**: server-deploy.sh

## 快速部署步骤

### 1. 上传部署包到服务器
```bash
scp mastea-deployment-with-image.tar.gz root@111.231.145.58:/opt/
```

### 2. 在服务器上解压并部署
```bash
# SSH连接到服务器
ssh root@111.231.145.58

# 解压部署包
cd /opt
tar -xzf mastea-deployment-with-image.tar.gz
cd mastea-deployment-with-image

# 执行一键部署
chmod +x server-deploy.sh
sudo ./server-deploy.sh
```

## 部署脚本功能

`server-deploy.sh` 脚本会自动执行以下操作：

1. **安装Docker环境**（如果未安装）
2. **清理旧的mastea容器和镜像**
3. **加载Docker镜像**（从tar文件）
4. **设置部署目录**（/opt/mastea）
5. **启动Docker Compose服务**
6. **运行数据库迁移**
7. **配置防火墙**（开放8001端口）
8. **验证部署状态**

## 访问地址

部署完成后可通过以下地址访问：

- **应用首页**: http://111.231.145.58:8001
- **API文档**: http://111.231.145.58:8001/api/v1/docs
- **管理界面**: http://111.231.145.58:8001/dashboard_enhanced
- **健康检查**: http://111.231.145.58:8001/api/v1/health

## 服务管理

```bash
# 进入部署目录
cd /opt/mastea

# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 查看应用日志
docker-compose logs fastapi_app

# 查看数据库日志
docker-compose logs postgres
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   netstat -tulpn | grep :8001
   ```

2. **容器启动失败**
   ```bash
   docker-compose logs
   ```

3. **数据库连接失败**
   ```bash
   docker-compose exec postgres pg_isready -U mastea
   ```

4. **镜像加载失败**
   ```bash
   docker load -i mastea-fastapi-image.tar
   ```

### 手动部署步骤

如果自动脚本失败，可以手动执行：

```bash
# 1. 加载镜像
docker load -i mastea-fastapi-image.tar

# 2. 启动服务
docker-compose up -d

# 3. 运行迁移
docker-compose exec -T fastapi_app bash -c "cd migrations && alembic upgrade heads"

# 4. 检查状态
docker-compose ps
curl http://localhost:8001/api/v1/health
```

## 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **内存**: 最少2GB，推荐4GB+
- **磁盘空间**: 最少3GB可用空间
- **网络**: 需要访问互联网安装Docker（仅首次）

## 优势

- **离线部署**: 包含完整Docker镜像，无需下载
- **一键部署**: 自动化安装和配置
- **快速恢复**: 可快速重新部署
- **节省流量**: 避免服务器重复下载依赖

## 技术支持

如遇问题请联系开发团队。

---
**注意**: 此部署包包含完整的Docker镜像，文件较大但部署更可靠。

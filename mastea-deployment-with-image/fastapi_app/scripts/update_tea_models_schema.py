#!/usr/bin/env python3
"""
茶品模型数据库架构更新脚本
用于添加茶品、配方、草药相关的字段和表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text, inspect
from fastapi_app.core.database import engine, SessionLocal
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_column_exists(table_name: str, column_name: str) -> bool:
    """检查表中是否存在指定列"""
    inspector = inspect(engine)
    try:
        columns = inspector.get_columns(table_name)
        return any(col['name'] == column_name for col in columns)
    except Exception:
        return False

def check_table_exists(table_name: str) -> bool:
    """检查表是否存在"""
    inspector = inspect(engine)
    tables = inspector.get_table_names()
    return table_name in tables

def add_tea_fields_to_goods():
    """为goods表添加茶品相关字段"""
    db = SessionLocal()
    try:
        if not check_table_exists('goods'):
            logger.error("goods表不存在，请先运行基础迁移")
            return False
        
        # 要添加的茶品字段列表
        tea_fields = [
            ("intro", "TEXT"),
            ("effect", "TEXT"),
            ("tea_type", "VARCHAR(50)"),
            ("grams", "INTEGER"),
            ("seasonal_recommend", "BOOLEAN DEFAULT FALSE"),
            ("self_select_enabled", "BOOLEAN DEFAULT FALSE"),
            ("self_select_categories", "TEXT"),
        ]
        
        # 添加字段
        for field_name, field_type in tea_fields:
            if not check_column_exists('goods', field_name):
                try:
                    sql = f"ALTER TABLE goods ADD COLUMN {field_name} {field_type}"
                    logger.info(f"为goods表添加字段: {field_name}")
                    db.execute(text(sql))
                    db.commit()
                except Exception as e:
                    logger.error(f"添加字段 {field_name} 失败: {e}")
                    db.rollback()
            else:
                logger.info(f"goods表字段 {field_name} 已存在，跳过")
        
        return True
        
    except Exception as e:
        logger.error(f"更新goods表失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def add_cart_vip_fields():
    """为cart_items表添加VIP定制字段"""
    db = SessionLocal()
    try:
        if not check_table_exists('cart_items'):
            logger.warning("cart_items表不存在，跳过VIP字段添加")
            return True
        
        # 要添加的VIP字段列表
        vip_fields = [
            ("packaging", "VARCHAR(100)"),
            ("is_vip", "BOOLEAN DEFAULT FALSE"),
            ("customization_note", "VARCHAR(500)"),
        ]
        
        # 添加字段
        for field_name, field_type in vip_fields:
            if not check_column_exists('cart_items', field_name):
                try:
                    sql = f"ALTER TABLE cart_items ADD COLUMN {field_name} {field_type}"
                    logger.info(f"为cart_items表添加字段: {field_name}")
                    db.execute(text(sql))
                    db.commit()
                except Exception as e:
                    logger.error(f"添加字段 {field_name} 失败: {e}")
                    db.rollback()
            else:
                logger.info(f"cart_items表字段 {field_name} 已存在，跳过")
        
        return True
        
    except Exception as e:
        logger.error(f"更新cart_items表失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def create_recipe_tables():
    """创建配方相关表"""
    db = SessionLocal()
    try:
        # 创建草药成分表
        if not check_table_exists('herb_ingredients'):
            herb_ingredients_sql = """
            CREATE TABLE herb_ingredients (
                id VARCHAR(32) PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                intro TEXT,
                image VARCHAR(256),
                flavor VARCHAR(50),
                nature VARCHAR(50),
                is_active BOOLEAN DEFAULT TRUE,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
            logger.info("创建herb_ingredients表")
            db.execute(text(herb_ingredients_sql))
            
            # 创建索引
            db.execute(text("CREATE INDEX ix_herb_ingredients_name ON herb_ingredients (name)"))
            db.commit()
        else:
            logger.info("herb_ingredients表已存在，跳过")
        
        # 创建配方表
        if not check_table_exists('recipes'):
            recipes_sql = """
            CREATE TABLE recipes (
                id VARCHAR(32) PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                intro TEXT,
                image VARCHAR(256),
                grams INTEGER,
                goods_id VARCHAR(32),
                is_active BOOLEAN DEFAULT TRUE,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (goods_id) REFERENCES goods (id) ON DELETE SET NULL
            )
            """
            logger.info("创建recipes表")
            db.execute(text(recipes_sql))
            
            # 创建索引
            db.execute(text("CREATE INDEX ix_recipes_name ON recipes (name)"))
            db.execute(text("CREATE INDEX ix_recipes_goods_id ON recipes (goods_id)"))
            db.commit()
        else:
            logger.info("recipes表已存在，跳过")
        
        # 创建配方-草药关联表
        if not check_table_exists('recipe_ingredients'):
            recipe_ingredients_sql = """
            CREATE TABLE recipe_ingredients (
                id VARCHAR(32) PRIMARY KEY,
                recipe_id VARCHAR(32) NOT NULL,
                herb_ingredient_id VARCHAR(32) NOT NULL,
                amount FLOAT,
                unit VARCHAR(20),
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (recipe_id) REFERENCES recipes (id) ON DELETE CASCADE,
                FOREIGN KEY (herb_ingredient_id) REFERENCES herb_ingredients (id) ON DELETE CASCADE
            )
            """
            logger.info("创建recipe_ingredients表")
            db.execute(text(recipe_ingredients_sql))
            
            # 创建索引
            db.execute(text("CREATE INDEX ix_recipe_ingredients_recipe_id ON recipe_ingredients (recipe_id)"))
            db.execute(text("CREATE INDEX ix_recipe_ingredients_herb_id ON recipe_ingredients (herb_ingredient_id)"))
            db.commit()
        else:
            logger.info("recipe_ingredients表已存在，跳过")
        
        return True
        
    except Exception as e:
        logger.error(f"创建配方表失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """主函数"""
    logger.info("开始更新茶品模型数据库架构...")
    
    try:
        # 1. 为goods表添加茶品字段
        if not add_tea_fields_to_goods():
            logger.error("添加茶品字段失败！")
            return 1
        
        # 2. 为cart_items表添加VIP字段
        if not add_cart_vip_fields():
            logger.error("添加VIP字段失败！")
            return 1
        
        # 3. 创建配方相关表
        if not create_recipe_tables():
            logger.error("创建配方表失败！")
            return 1
        
        logger.info("茶品模型数据库架构更新成功！")
        return 0
        
    except Exception as e:
        logger.error(f"更新过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

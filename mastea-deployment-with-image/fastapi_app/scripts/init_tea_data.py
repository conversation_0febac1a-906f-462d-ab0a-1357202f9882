#!/usr/bin/env python3
"""
初始化茶品测试数据脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi_app.core.database import SessionLocal
from fastapi_app.models.goods import Goods
from fastapi_app.models.recipe import Recipe, HerbIngredient, RecipeIngredient
import uuid
import json
import logging

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_tea_products():
    """创建示例茶品数据"""
    db = SessionLocal()
    try:
        # 检查是否已有茶品数据
        existing_tea = db.query(Goods).filter(Goods.tea_type.isnot(None)).first()
        if existing_tea:
            logger.info("茶品数据已存在，跳过初始化")
            return True
        
        # 创建基础推荐茶品
        tea_products = [
            {
                "name": "莲子心淡竹叶茶",
                "description": "清心火、解烦安神、交通心肾",
                "intro": "清心火、解烦安神、交通心肾",
                "price": 76.0,
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-10000000.png",
                "effect": "安神助眠，改善睡眠质量",
                "tea_type": "basicRecommend",
                "category_name": "安神助眠",
                "seasonal_recommend": True,
                "self_select_enabled": True,
                "self_select_categories": json.dumps(["全部", "助眠"]),
                "status": 0
            },
            {
                "name": "健脾茶",
                "description": "健脾益气，调理脾胃",
                "intro": "健脾益气，调理脾胃",
                "price": 68.0,
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-10000001.png",
                "effect": "健脾益气，改善消化",
                "tea_type": "basicRecommend",
                "category_name": "健脾益气",
                "seasonal_recommend": False,
                "self_select_enabled": True,
                "self_select_categories": json.dumps(["全部", "养脾胃"]),
                "status": 0
            },
            {
                "name": "美颜茶",
                "description": "美容养颜，滋润肌肤",
                "intro": "美容养颜，滋润肌肤",
                "price": 88.0,
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-10000002.png",
                "effect": "美容养颜，改善肌肤状态",
                "tea_type": "basicRecommend",
                "category_name": "美容养颜",
                "seasonal_recommend": True,
                "self_select_enabled": True,
                "self_select_categories": json.dumps(["全部", "皮肤"]),
                "status": 0
            },
            {
                "name": "提神茶",
                "description": "提神醒脑，增强活力",
                "intro": "提神醒脑，增强活力",
                "price": 72.0,
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-10000003.png",
                "effect": "提神醒脑，缓解疲劳",
                "tea_type": "basicRecommend",
                "category_name": "提神醒脑",
                "seasonal_recommend": False,
                "self_select_enabled": True,
                "self_select_categories": json.dumps(["全部", "提神"]),
                "status": 0
            },
            {
                "name": "定制茶包",
                "description": "根据个人体质定制的专属茶包",
                "intro": "根据个人体质定制的专属茶包",
                "price": 120.0,
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-custom.png",
                "effect": "个性化调理，针对性改善",
                "tea_type": "customization",
                "category_name": "个性定制",
                "seasonal_recommend": False,
                "self_select_enabled": False,
                "self_select_categories": None,
                "status": 0
            }
        ]
        
        created_goods = []
        for tea_data in tea_products:
            goods = Goods(
                id=generate_uuid(),
                name=tea_data["name"],
                description=tea_data["description"],
                intro=tea_data["intro"],
                price=tea_data["price"],
                image=tea_data["image"],
                effect=tea_data["effect"],
                tea_type=tea_data["tea_type"],
                category_name=tea_data["category_name"],
                seasonal_recommend=tea_data["seasonal_recommend"],
                self_select_enabled=tea_data["self_select_enabled"],
                self_select_categories=tea_data["self_select_categories"],
                status=tea_data["status"],
                sales=0
            )
            db.add(goods)
            created_goods.append(goods)
            logger.info(f"创建茶品: {tea_data['name']}")
        
        db.commit()
        
        # 为每个茶品创建配方
        for goods in created_goods:
            db.refresh(goods)  # 刷新以获取ID
            
            # 创建配方
            recipe = Recipe(
                id=generate_uuid(),
                name=f"{goods.name}配方",
                description=f"{goods.name}的标准配方",
                intro=f"精心调配的{goods.name}配方",
                image=goods.image,
                grams=10,
                goods_id=goods.id,
                is_active=True
            )
            db.add(recipe)
            logger.info(f"为茶品 {goods.name} 创建配方")
        
        db.commit()
        logger.info("茶品数据初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"初始化茶品数据失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def create_sample_herbs():
    """创建示例草药数据"""
    db = SessionLocal()
    try:
        # 检查是否已有草药数据
        existing_herb = db.query(HerbIngredient).first()
        if existing_herb:
            logger.info("草药数据已存在，跳过初始化")
            return True
        
        # 创建草药成分
        herbs = [
            {
                "name": "莲子心",
                "description": "莲子心具有清心火、安神的功效",
                "intro": "莲子心是莲子中央的绿色胚芽",
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/herb-lotus.png",
                "flavor": "苦",
                "nature": "寒"
            },
            {
                "name": "淡竹叶",
                "description": "淡竹叶具有清热除烦、利尿的功效",
                "intro": "淡竹叶是禾本科植物淡竹的叶",
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/herb-bamboo.png",
                "flavor": "甘淡",
                "nature": "寒"
            },
            {
                "name": "白术",
                "description": "白术具有健脾益气、燥湿利水的功效",
                "intro": "白术是菊科植物白术的根茎",
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/herb-baizhu.png",
                "flavor": "甘苦",
                "nature": "温"
            },
            {
                "name": "茯苓",
                "description": "茯苓具有利水渗湿、健脾安神的功效",
                "intro": "茯苓是多孔菌科真菌茯苓的干燥菌核",
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/herb-fuling.png",
                "flavor": "甘淡",
                "nature": "平"
            },
            {
                "name": "玫瑰花",
                "description": "玫瑰花具有理气解郁、活血散瘀的功效",
                "intro": "玫瑰花是蔷薇科植物玫瑰的花蕾",
                "image": "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/herb-rose.png",
                "flavor": "甘微苦",
                "nature": "温"
            }
        ]
        
        for herb_data in herbs:
            herb = HerbIngredient(
                id=generate_uuid(),
                name=herb_data["name"],
                description=herb_data["description"],
                intro=herb_data["intro"],
                image=herb_data["image"],
                flavor=herb_data["flavor"],
                nature=herb_data["nature"],
                is_active=True
            )
            db.add(herb)
            logger.info(f"创建草药: {herb_data['name']}")
        
        db.commit()
        logger.info("草药数据初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"初始化草药数据失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """主函数"""
    logger.info("开始初始化茶品测试数据...")
    
    try:
        # 1. 创建茶品数据
        if not create_sample_tea_products():
            logger.error("创建茶品数据失败！")
            return 1
        
        # 2. 创建草药数据
        if not create_sample_herbs():
            logger.error("创建草药数据失败！")
            return 1
        
        logger.info("茶品测试数据初始化成功！")
        return 0
        
    except Exception as e:
        logger.error(f"初始化过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

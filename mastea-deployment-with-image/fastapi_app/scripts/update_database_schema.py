#!/usr/bin/env python3
"""
数据库架构更新脚本
用于添加微信登录和扩展用户信息字段
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text, inspect
from fastapi_app.core.database import engine, SessionLocal
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_column_exists(table_name: str, column_name: str) -> bool:
    """检查表中是否存在指定列"""
    inspector = inspect(engine)
    columns = inspector.get_columns(table_name)
    return any(col['name'] == column_name for col in columns)

def add_wechat_fields():
    """添加微信相关字段"""
    db = SessionLocal()
    try:
        # 检查users表是否存在
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if 'users' not in tables:
            logger.error("users表不存在，请先运行基础迁移")
            return False
        
        # 要添加的字段列表
        fields_to_add = [
            ("wechat_openid", "VARCHAR(64)"),
            ("wechat_unionid", "VARCHAR(64)"),
            ("wechat_nickname", "VARCHAR(100)"),
            ("wechat_avatar", "VARCHAR(256)"),
            ("real_name", "VARCHAR(50)"),
            ("address", "VARCHAR(200)"),
            ("zip_code", "VARCHAR(10)"),
            ("avatar_url", "VARCHAR(256)"),
            ("is_member", "BOOLEAN DEFAULT FALSE"),
            ("membership_start_date", "TIMESTAMP"),
            ("membership_end_date", "TIMESTAMP"),
        ]
        
        # 添加字段
        for field_name, field_type in fields_to_add:
            if not check_column_exists('users', field_name):
                try:
                    sql = f"ALTER TABLE users ADD COLUMN {field_name} {field_type}"
                    logger.info(f"添加字段: {field_name}")
                    db.execute(text(sql))
                    db.commit()
                except Exception as e:
                    logger.error(f"添加字段 {field_name} 失败: {e}")
                    db.rollback()
            else:
                logger.info(f"字段 {field_name} 已存在，跳过")
        
        # 修改password_hash字段为可空（如果需要）
        try:
            # 对于PostgreSQL
            if engine.dialect.name == 'postgresql':
                db.execute(text("ALTER TABLE users ALTER COLUMN password_hash DROP NOT NULL"))
                db.commit()
                logger.info("password_hash字段已设置为可空")
        except Exception as e:
            logger.warning(f"修改password_hash字段失败（可能已经是可空的）: {e}")
            db.rollback()
        
        # 创建索引
        indexes_to_create = [
            ("ix_users_wechat_openid", "users", "wechat_openid", True),
            ("ix_users_wechat_unionid", "users", "wechat_unionid", True),
        ]
        
        for index_name, table_name, column_name, unique in indexes_to_create:
            try:
                unique_str = "UNIQUE" if unique else ""
                sql = f"CREATE {unique_str} INDEX IF NOT EXISTS {index_name} ON {table_name} ({column_name})"
                db.execute(text(sql))
                db.commit()
                logger.info(f"创建索引: {index_name}")
            except Exception as e:
                logger.warning(f"创建索引 {index_name} 失败: {e}")
                db.rollback()
        
        logger.info("数据库架构更新完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库更新失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """主函数"""
    logger.info("开始更新数据库架构...")
    
    try:
        success = add_wechat_fields()
        if success:
            logger.info("数据库架构更新成功！")
            return 0
        else:
            logger.error("数据库架构更新失败！")
            return 1
    except Exception as e:
        logger.error(f"更新过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

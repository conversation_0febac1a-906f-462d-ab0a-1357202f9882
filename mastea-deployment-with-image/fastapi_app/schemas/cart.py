from pydantic import BaseModel, Field
from typing import Optional

class CartItemBase(BaseModel):
    goods_id: str
    sku_id: Optional[str] = None
    count: int = Field(1, gt=0)

class CartItemCreate(CartItemBase):
    pass

class CartItemUpdate(BaseModel):
    count: Optional[int] = Field(None, gt=0)
    checked: Optional[bool] = None

class CartItem(BaseModel):
    id: str  # This is the actual cart item ID for update/delete operations
    goods_id: str
    sku_id: Optional[str] = None
    name: str
    price: float
    count: int
    image: Optional[str] = None
    checked: bool

    class Config:
        from_attributes = True
        populate_by_name = True 
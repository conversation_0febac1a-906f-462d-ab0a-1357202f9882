from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class GoodsBase(BaseModel):
    name: str
    description: Optional[str] = None
    content: Optional[str] = None
    price: float
    original_price: Optional[float] = None
    image: Optional[str] = None
    sales: int = 0
    category_id: Optional[str] = None
    category_name: Optional[str] = None
    status: int = 0

class GoodsCreate(GoodsBase):
    pass

class GoodsUpdate(GoodsBase):
    name: Optional[str] = None
    price: Optional[float] = None

class GoodsSKUBase(BaseModel):
    sku_name: str
    price: float
    stock: int = 0

class GoodsSKUCreate(GoodsSKUBase):
    goods_id: str

class GoodsSKUUpdate(GoodsSKUBase):
    sku_name: Optional[str] = None
    price: Optional[float] = None
    stock: Optional[int] = None

class GoodsSKU(GoodsSKUBase):
    id: str
    goods_id: str
    
    class Config:
        from_attributes = True

class GoodsImageBase(BaseModel):
    url: str
    order: int = 0

class GoodsImageCreate(GoodsImageBase):
    goods_id: str

class GoodsImageUpdate(GoodsImageBase):
    url: Optional[str] = None
    order: Optional[int] = None

class GoodsImage(GoodsImageBase):
    id: str
    goods_id: str
    
    class Config:
        from_attributes = True

class Goods(GoodsBase):
    id: str
    sales: int = 0
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    goods_skus: List[GoodsSKU] = []
    goods_images: List[GoodsImage] = []
    
    class Config:
        from_attributes = True
        
    # 兼容旧版API
    @property
    def created_at(self) -> Optional[datetime]:
        return self.create_time
        
    @property
    def updated_at(self) -> Optional[datetime]:
        return self.update_time 
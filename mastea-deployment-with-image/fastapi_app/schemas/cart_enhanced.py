"""
增强的购物车数据模式
整合k_api的购物车数据结构到主后端项目
"""

from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class CartItemAdd(BaseModel):
    """添加商品到购物车请求模型"""
    teaProductId: str = Field(..., description="茶品产品ID")
    quantity: int = Field(1, ge=1, description="数量，必须大于0")
    productType: int = Field(0, ge=0, le=1, description="商品类型：0=普通茶品，1=定制茶品")

class CartItemUpdate(BaseModel):
    """更新购物车商品数量请求模型"""
    cartItemId: str = Field(..., description="购物车项ID")
    quantity: int = Field(..., ge=1, description="新数量，必须大于0")

class CartItemRemove(BaseModel):
    """删除购物车商品请求模型"""
    cartItemIds: List[str] = Field(..., description="要删除的购物车项ID列表")

class CartItemSelect(BaseModel):
    """购物车商品选择状态请求模型"""
    cartItemIds: List[str] = Field(..., description="要更新选择状态的购物车项ID列表，空数组表示全部")
    selected: bool = Field(..., description="选择状态：true=选中，false=取消选中")

class CartItemResponse(BaseModel):
    """购物车商品响应模型"""
    id: str = Field(..., description="购物车项ID")
    productId: str = Field(..., description="商品ID")
    productName: str = Field(..., description="商品名称")
    price: float = Field(..., description="商品单价")
    originalPrice: Optional[float] = Field(None, description="原价（用于显示折扣）")
    quantity: int = Field(..., description="数量")
    image: str = Field(..., description="商品图片URL")
    isVip: bool = Field(False, description="是否为会员商品")
    stock: int = Field(..., description="库存数量")
    maxPurchase: Optional[int] = Field(None, description="最大购买数量限制")
    selected: bool = Field(True, description="是否选中")
    productType: int = Field(..., description="商品类型：0=普通茶品，1=定制茶品")

class CartResponse(BaseModel):
    """购物车响应模型"""
    items: List[CartItemResponse] = Field(default_factory=list, description="购物车商品列表")
    totalPrice: float = Field(0.0, description="总价")
    totalQuantity: int = Field(0, description="总数量")

class CartAddResponse(BaseModel):
    """添加商品到购物车响应模型"""
    cartItemId: str = Field(..., description="购物车项ID")
    totalQuantity: int = Field(..., description="购物车总数量")

class CartUpdateResponse(BaseModel):
    """更新购物车商品响应模型"""
    item: dict = Field(..., description="更新后的商品信息")
    totalPrice: float = Field(..., description="购物车总价")
    totalQuantity: int = Field(..., description="购物车总数量")

class CartRemoveResponse(BaseModel):
    """删除购物车商品响应模型"""
    totalPrice: float = Field(..., description="删除后的购物车总价")
    totalQuantity: int = Field(..., description="删除后的购物车总数量")

class CartSelectResponse(BaseModel):
    """购物车选择状态响应模型"""
    selectedItems: List[str] = Field(..., description="选中的商品ID列表")
    selectedTotalPrice: float = Field(..., description="选中商品的总价")

# 兼容性模型：支持原有的购物车格式
class LegacyCartItem(BaseModel):
    """兼容原有购物车格式的商品项"""
    id: int = Field(..., description="商品ID（数字格式）")
    name: str = Field(..., description="商品名称")
    price: int = Field(..., description="商品价格（整数格式）")
    image: str = Field(..., description="商品图片URL")
    quantity: int = Field(..., description="数量")
    packaging: str = Field("10包/袋", description="包装规格")
    isVip: bool = Field(False, description="是否为会员商品")

class LegacyCartResponse(BaseModel):
    """兼容原有购物车格式的响应"""
    code: int = Field(0, description="响应代码")
    message: str = Field("获取购物车成功", description="响应消息")
    data: List[LegacyCartItem] = Field(default_factory=list, description="购物车数据")

# 购物车统计信息
class CartStats(BaseModel):
    """购物车统计信息"""
    totalItems: int = Field(0, description="商品种类数量")
    totalQuantity: int = Field(0, description="商品总数量")
    totalPrice: float = Field(0.0, description="总价")
    selectedItems: int = Field(0, description="选中商品种类数量")
    selectedQuantity: int = Field(0, description="选中商品总数量")
    selectedPrice: float = Field(0.0, description="选中商品总价")

class CartSummary(BaseModel):
    """购物车摘要信息"""
    stats: CartStats = Field(..., description="统计信息")
    hasItems: bool = Field(False, description="是否有商品")
    hasSelectedItems: bool = Field(False, description="是否有选中商品")
    canCheckout: bool = Field(False, description="是否可以结算")

# 购物车操作结果
class CartOperationResult(BaseModel):
    """购物车操作结果"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")
    affectedItems: List[str] = Field(default_factory=list, description="受影响的商品ID列表")
    cart: Optional[CartResponse] = Field(None, description="更新后的购物车数据")

# 批量操作请求
class CartBatchOperation(BaseModel):
    """购物车批量操作请求"""
    operation: str = Field(..., description="操作类型：select_all, unselect_all, remove_selected")
    itemIds: Optional[List[str]] = Field(None, description="指定的商品ID列表（可选）")

# 购物车商品详情
class CartItemDetail(BaseModel):
    """购物车商品详情"""
    id: str = Field(..., description="购物车项ID")
    product: dict = Field(..., description="商品详细信息")
    quantity: int = Field(..., description="数量")
    selected: bool = Field(..., description="是否选中")
    addedAt: datetime = Field(..., description="添加时间")
    updatedAt: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True

# 购物车验证结果
class CartValidationResult(BaseModel):
    """购物车验证结果"""
    valid: bool = Field(..., description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")
    warnings: List[str] = Field(default_factory=list, description="警告信息列表")
    invalidItems: List[str] = Field(default_factory=list, description="无效商品ID列表")
    outOfStockItems: List[str] = Field(default_factory=list, description="缺货商品ID列表")

from sqlalchemy.orm import Session, joinedload
from fastapi_app.models import order as models_order, cart as models_cart, address as models_address, goods as models_goods
from fastapi_app.schemas import order as schemas_order
import json

def create_order_from_cart(db: Session, user_id: str, order_in: schemas_order.OrderCreate):
    """
    从购物车创建订单

    Args:
        db: 数据库会话
        user_id: 用户ID
        order_in: 订单创建数据

    Returns:
        Order: 创建的订单对象，如果失败返回None

    Raises:
        Exception: 当数据库操作失败时
    """
    try:
        # Get user's checked cart items
        cart_items = db.query(models_cart.CartItem).filter(
            models_cart.CartItem.user_id == user_id,
            models_cart.CartItem.checked == True
        ).all()

        if not cart_items:
            return None # 购物车中没有选中的商品

        # Get address and create snapshot
        address = db.query(models_address.Address).filter(
            models_address.Address.id == order_in.address_id,
            models_address.Address.user_id == user_id
        ).first()
        if not address:
            return None # 地址不存在或不属于当前用户

        address_data = {
            'id': address.id, 'name': address.name, 'mobile': address.mobile,
            'province': address.province, 'city': address.city,
            'district': address.district, 'detail': address.detail
        }
        address_snapshot = json.dumps(address_data)

        # 验证商品价格和库存
        total_price = 0
        for item in cart_items:
            # 验证商品是否存在且价格是否正确
            if item.sku_id:
                # 检查SKU商品
                sku = db.query(models_goods.GoodsSKU).filter(models_goods.GoodsSKU.id == item.sku_id).first()
                if not sku:
                    print(f"SKU商品不存在: {item.sku_id}")
                    return None
                if sku.price != item.price:
                    print(f"SKU商品价格已变更: {item.sku_id}, 购物车价格: {item.price}, 当前价格: {sku.price}")
                    return None
                if sku.stock < item.count:
                    print(f"SKU商品库存不足: {item.sku_id}, 需要: {item.count}, 库存: {sku.stock}")
                    return None
            else:
                # 检查普通商品
                goods = db.query(models_goods.Goods).filter(models_goods.Goods.id == item.goods_id).first()
                if not goods:
                    print(f"商品不存在: {item.goods_id}")
                    return None
                if goods.price != item.price:
                    print(f"商品价格已变更: {item.goods_id}, 购物车价格: {item.price}, 当前价格: {goods.price}")
                    return None
                if goods.status != 0:
                    print(f"商品已下架: {item.goods_id}")
                    return None

            total_price += item.price * item.count

        # Create the order
        db_order = models_order.Order(
            user_id=user_id,
            address_id=order_in.address_id,
            total_price=total_price,
            pay_price=total_price, # Assuming full payment for now
            address_snapshot=address_snapshot,
            remark=order_in.remark
        )
        db.add(db_order)
        db.flush() # Use flush to get the order ID before committing

        # Create order goods and remove from cart
        for item in cart_items:
            # 安全获取SKU名称，避免None异常
            sku_name = None
            if item.sku_id:
                sku = db.query(models_goods.GoodsSKU).filter(models_goods.GoodsSKU.id == item.sku_id).first()
                if sku:
                    sku_name = sku.name

            order_good = models_order.OrderGoods(
                order_id=db_order.id,
                goods_id=item.goods_id,
                sku_id=item.sku_id,
                name=item.name,
                price=item.price,
                count=item.count,
                image=item.image,
                sku_name=sku_name
            )
            db.add(order_good)
            db.delete(item)

        db.commit()
        db.refresh(db_order)
        return db_order

    except Exception as e:
        # 发生错误时回滚事务
        db.rollback()
        print(f"创建订单失败: {str(e)}")
        return None

def get_order_list(db: Session, user_id: str):
    return db.query(models_order.Order).filter(models_order.Order.user_id == user_id).order_by(models_order.Order.create_time.desc()).all()

def get_order_detail(db: Session, order_id: str, user_id: str):
    return db.query(models_order.Order).options(
        joinedload(models_order.Order.order_goods)
    ).filter(models_order.Order.id == order_id, models_order.Order.user_id == user_id).first()

def pay_order(db: Session, order_id: str, user_id: str):
    """支付订单（模拟支付）"""
    order = db.query(models_order.Order).filter(
        models_order.Order.id == order_id,
        models_order.Order.user_id == user_id,
        models_order.Order.status == 0  # 只有待支付的订单可以支付
    ).first()
    
    if not order:
        return None
    
    # 更新订单状态为已支付
    from datetime import datetime
    order.status = 1  # 已支付/待发货
    order.pay_time = datetime.now()
    order.pay_price = order.total_price  # 全额支付
    
    db.commit()
    db.refresh(order)
    return order

def cancel_order(db: Session, order_id: str, user_id: str):
    """取消订单"""
    order = db.query(models_order.Order).filter(
        models_order.Order.id == order_id,
        models_order.Order.user_id == user_id,
        models_order.Order.status == 0  # 只有待支付的订单可以取消
    ).first()
    
    if not order:
        return None
    
    # 更新订单状态为已取消
    from datetime import datetime
    order.status = 4  # 已取消
    order.cancel_time = datetime.now()
    
    db.commit()
    db.refresh(order)
    return order

def confirm_order(db: Session, order_id: str, user_id: str):
    """确认收货"""
    order = db.query(models_order.Order).filter(
        models_order.Order.id == order_id,
        models_order.Order.user_id == user_id,
        models_order.Order.status == 2  # 只有待收货的订单可以确认收货
    ).first()
    
    if not order:
        return None
    
    # 更新订单状态为已完成
    from datetime import datetime
    order.status = 3  # 已完成
    order.complete_time = datetime.now()
    
    db.commit()
    db.refresh(order)
    return order

def ship_order(db: Session, order_id: str):
    """发货（管理员操作）"""
    order = db.query(models_order.Order).filter(
        models_order.Order.id == order_id,
        models_order.Order.status == 1  # 只有已支付的订单可以发货
    ).first()
    
    if not order:
        return None
    
    # 更新订单状态为已发货
    from datetime import datetime
    order.status = 2  # 已发货/待收货
    order.ship_time = datetime.now()
    
    db.commit()
    db.refresh(order)
    return order 
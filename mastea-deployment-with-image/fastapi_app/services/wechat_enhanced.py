"""
增强的微信服务模块
整合k_api的微信服务逻辑到主后端项目
"""

import os
import json
import logging
import requests
import hashlib
import base64
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
try:
    from Crypto.Cipher import AES
except ImportError:
    # Use cryptography library as fallback
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from cryptography.hazmat.backends import default_backend
    import struct
    
    class AES:
        MODE_CBC = 'CBC'
        
        @staticmethod
        def new(key, mode, iv=None):
            if mode == AES.MODE_CBC:
                cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
                return cipher
            else:
                raise ValueError("Unsupported mode")
        
        @staticmethod
        def block_size():
            return 16
from sqlalchemy.orm import Session
from fastapi import HTTPException

from fastapi_app.models.user import User
from fastapi_app.crud import user as crud_user
from fastapi_app.core.config import settings

logger = logging.getLogger(__name__)

class WeChatMiniProgramAPI:
    """
    微信小程序API服务类
    整合k_api的微信登录和用户信息获取逻辑
    """
    
    def __init__(self):
        self.app_id = os.getenv("WECHAT_APP_ID", "")
        self.app_secret = os.getenv("WECHAT_APP_SECRET", "")
        self.api_base_url = "https://api.weixin.qq.com"
        
        if not self.app_id or not self.app_secret:
            logger.warning("微信小程序配置不完整，请检查环境变量 WECHAT_APP_ID 和 WECHAT_APP_SECRET")
    
    async def get_access_token(self, code: str) -> Dict[str, Any]:
        """
        通过授权码获取access_token和openid
        
        Args:
            code: 微信小程序授权码
            
        Returns:
            包含openid和session_key的字典
            
        Raises:
            HTTPException: 当微信API调用失败时
        """
        url = f"{self.api_base_url}/sns/jscode2session"
        params = {
            "appid": self.app_id,
            "secret": self.app_secret,
            "js_code": code,
            "grant_type": "authorization_code"
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if "errcode" in data:
                error_msg = f"微信API错误: {data.get('errmsg', '未知错误')}"
                logger.error(f"{error_msg}, errcode: {data.get('errcode')}")
                raise HTTPException(status_code=400, detail=error_msg)
            
            if "openid" not in data or "session_key" not in data:
                logger.error(f"微信API返回数据不完整: {data}")
                raise HTTPException(status_code=400, detail="微信登录失败，请重试")
            
            logger.info(f"成功获取微信用户信息: openid={data['openid'][:5]}***, session_key长度={len(data['session_key'])}")
            return data
            
        except requests.RequestException as e:
            logger.error(f"微信API请求失败: {e}")
            raise HTTPException(status_code=500, detail="微信服务暂时不可用，请稍后重试")
    
    async def get_user_info(
        self, 
        session_key: str, 
        openid: str, 
        encrypted_data: Optional[str] = None, 
        iv: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取用户信息
        
        Args:
            session_key: 微信会话密钥
            openid: 微信用户openid
            encrypted_data: 加密的用户数据（可选）
            iv: 加密算法初始向量（可选）
            
        Returns:
            用户信息字典
        """
        user_info = {
            "openid": openid,
            "session_key": session_key,
            "is_authorized": False
        }
        
        # 如果提供了加密数据，尝试解密获取完整用户信息
        if encrypted_data and iv:
            try:
                decrypted_data = self._decrypt_user_data(session_key, encrypted_data, iv)
                if decrypted_data:
                    user_info.update(decrypted_data)
                    user_info["is_authorized"] = True
                    logger.info("成功解密用户信息")
                else:
                    logger.warning("用户信息解密失败，使用基本信息")
            except Exception as e:
                logger.error(f"解密用户信息时发生错误: {e}")
        
        return user_info
    
    def _decrypt_user_data(self, session_key: str, encrypted_data: str, iv: str) -> Optional[Dict[str, Any]]:
        """
        解密微信用户数据
        
        Args:
            session_key: 会话密钥
            encrypted_data: 加密数据
            iv: 初始向量
            
        Returns:
            解密后的用户数据字典，失败时返回None
        """
        try:
            # Base64解码
            session_key_bytes = base64.b64decode(session_key)
            encrypted_data_bytes = base64.b64decode(encrypted_data)
            iv_bytes = base64.b64decode(iv)
            
            # AES解密
            cipher = AES.new(session_key_bytes, AES.MODE_CBC, iv_bytes)
            decrypted_bytes = cipher.decrypt(encrypted_data_bytes)
            
            # 去除PKCS7填充
            padding_length = decrypted_bytes[-1]
            decrypted_data = decrypted_bytes[:-padding_length]
            
            # 解析JSON
            user_data = json.loads(decrypted_data.decode('utf-8'))
            
            # 转换字段名以匹配数据库模型
            normalized_data = {
                "nickname": user_data.get("nickName", ""),
                "avatar_url": user_data.get("avatarUrl", ""),
                "gender": user_data.get("gender", 0),
                "language": user_data.get("language", ""),
                "city": user_data.get("city", ""),
                "province": user_data.get("province", ""),
                "country": user_data.get("country", ""),
                "raw_data": user_data
            }
            
            return normalized_data
            
        except Exception as e:
            logger.error(f"解密用户数据失败: {e}")
            return None

class WeChatUserService:
    """
    微信用户服务类
    处理微信用户的创建、查询和更新
    """
    
    @staticmethod
    def get_user_by_openid(db: Session, openid: str) -> Optional[User]:
        """
        通过openid查询用户
        
        Args:
            db: 数据库会话
            openid: 微信openid
            
        Returns:
            用户对象或None
        """
        return db.query(User).filter(
            User.wechat_openid == openid,
            User.is_active == True
        ).first()
    
    @staticmethod
    def get_user_by_unionid(db: Session, unionid: str) -> Optional[User]:
        """
        通过unionid查询用户
        
        Args:
            db: 数据库会话
            unionid: 微信unionid
            
        Returns:
            用户对象或None
        """
        return db.query(User).filter(
            User.wechat_unionid == unionid,
            User.is_active == True
        ).first()
    
    @staticmethod
    def create_wechat_user(db: Session, user_info: Dict[str, Any]) -> User:
        """
        创建微信用户
        
        Args:
            db: 数据库会话
            user_info: 用户信息字典
            
        Returns:
            创建的用户对象
        """
        # 生成用户名（如果没有昵称，使用默认格式）
        nickname = user_info.get("nickname", "")
        if not nickname:
            nickname = f"微信用户_{user_info['openid'][-6:]}"
        
        # 创建用户对象
        user_data = {
            "username": nickname,
            "wechat_openid": user_info["openid"],
            "wechat_unionid": user_info.get("unionid"),
            "avatar_url": user_info.get("avatar_url", ""),
            "gender": "male" if user_info.get("gender") == 1 else "female" if user_info.get("gender") == 2 else "unknown",
            "is_wechat_user": True,
            "is_active": True
        }
        
        # 如果有授权信息，添加更多字段
        if user_info.get("is_authorized"):
            user_data.update({
                "wechat_authorized": True,
                "wechat_auth_time": datetime.utcnow()
            })
        
        db_user = User(**user_data)
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        logger.info(f"创建新微信用户: {db_user.id}, openid: {user_info['openid'][:5]}***")
        return db_user
    
    @staticmethod
    def update_wechat_user(db: Session, user: User, user_info: Dict[str, Any]) -> User:
        """
        更新微信用户信息
        
        Args:
            db: 数据库会话
            user: 用户对象
            user_info: 新的用户信息
            
        Returns:
            更新后的用户对象
        """
        has_updates = False
        
        # 更新unionid（如果提供且原来没有）
        if user_info.get("unionid") and not user.wechat_unionid:
            user.wechat_unionid = user_info["unionid"]
            has_updates = True
        
        # 更新授权状态
        if user_info.get("is_authorized") and not user.wechat_authorized:
            user.wechat_authorized = True
            user.wechat_auth_time = datetime.utcnow()
            has_updates = True
            
            # 更新用户信息
            if user_info.get("nickname"):
                user.username = user_info["nickname"]
            if user_info.get("avatar_url"):
                user.avatar_url = user_info["avatar_url"]
            if user_info.get("gender"):
                user.gender = "male" if user_info["gender"] == 1 else "female" if user_info["gender"] == 2 else "unknown"
            
            has_updates = True
        
        # 更新最后登录时间
        user.last_login_at = datetime.utcnow()
        has_updates = True
        
        if has_updates:
            user.updated_at = datetime.utcnow()
            db.commit()
            db.refresh(user)
            logger.info(f"更新微信用户信息: {user.id}")
        
        return user

# 创建全局实例
wechat_api = WeChatMiniProgramAPI()
wechat_user_service = WeChatUserService()

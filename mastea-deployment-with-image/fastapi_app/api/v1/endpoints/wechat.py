"""
微信小程序专用API端点
整合k_api的微信登录逻辑到主后端项目
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from typing import Optional
import logging
from datetime import datetime, timedelta

from fastapi_app.core.database import get_db
from fastapi_app.core.response import APIResponse, handle_exception
from fastapi_app.core.security import create_access_token, create_refresh_token
from fastapi_app.schemas.wechat import (
    WeChatLogin, RefreshToken, WeChatLoginResponse, 
    RefreshTokenResponse, DevTokenRequest, DevTokenResponse
)
from fastapi_app.services.wechat_enhanced import wechat_api, wechat_user_service
from fastapi_app.models.user import User

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/login")
async def wechat_login(
    request: Request, 
    wechat_data: WeChatLogin, 
    db: Session = Depends(get_db)
):
    """
    微信小程序登录接口
    
    整合k_api的登录逻辑，支持静默登录和授权登录
    
    Args:
        request: HTTP请求对象，用于获取客户端IP
        wechat_data: 包含微信授权码的数据
        db: 数据库会话
        
    Returns:
        登录成功的响应，包含token和用户信息
    """
    # 记录登录尝试和请求信息
    client_ip = request.client.host if request.client else "未知IP"
    logger.info(f"微信小程序登录尝试: code={wechat_data.code[:10]}..., IP={client_ip}")
    
    # 如果没有提供客户端IP，从请求中获取
    if not wechat_data.client_ip:
        wechat_data.client_ip = client_ip

    try:
        # 1. 通过小程序授权码获取用户信息（session_key 和 openid）
        token_data = await wechat_api.get_access_token(wechat_data.code)
        
        # 记录服务器返回的 session_key 和 openid 信息
        logger.info(f"获取到的openid: {token_data.get('openid')[:5]}***, session_key长度: {len(token_data.get('session_key', ''))}")
        
        # 检查是否提供了加密数据
        if wechat_data.encryptedData and wechat_data.iv:
            logger.info("前端提供了encryptedData和iv，尝试解密获取完整用户信息")
        else:
            logger.info("前端未提供encryptedData和iv，将使用基本用户信息")

        # 2. 获取用户信息
        user_info = await wechat_api.get_user_info(
            token_data['session_key'],
            token_data['openid'],
            wechat_data.encryptedData,
            wechat_data.iv
        )
        
        # 添加其他可能有用的信息
        if wechat_data.app_version:
            user_info['app_version'] = wechat_data.app_version
        
        user_info['last_ip'] = wechat_data.client_ip or client_ip
        
        # 记录获取到的用户信息
        logger.info(f"处理后的用户信息: openid={user_info['openid'][:5]}***, authorized={user_info.get('is_authorized', False)}")

        # 3. 查找或创建用户
        db_user = None

        # 优先通过unionid查找用户（如果有的话）
        if user_info.get('unionid'):
            db_user = wechat_user_service.get_user_by_unionid(db, user_info['unionid'])
            logger.info(f"通过unionid查找用户: {db_user is not None}")

        # 如果没有找到，通过openid查找
        if not db_user:
            db_user = wechat_user_service.get_user_by_openid(db, user_info['openid'])
            logger.info(f"通过openid查找用户: {db_user is not None}")

        # 如果用户不存在，创建新用户
        if not db_user:
            logger.info(f"创建新的微信小程序用户: openid={user_info['openid'][:5]}***")
            db_user = wechat_user_service.create_wechat_user(db, user_info)
        else:
            # 更新现有用户的微信信息
            logger.info(f"更新现有用户的微信信息: user_id={db_user.id}")
            db_user = wechat_user_service.update_wechat_user(db, db_user, user_info)

        # 4. 生成JWT token和refresh token
        access_token = create_access_token(data={"sub": str(db_user.id)})
        refresh_token = create_refresh_token(data={"sub": str(db_user.id)})

        # token有效期（秒）
        expires_in = 7200  # 2小时

        logger.info(f"微信小程序登录成功: user_id={db_user.id}, nickname={db_user.username}")

        # 构建API响应
        return APIResponse.success(
            data={
                "token": access_token,
                "refreshToken": refresh_token,
                "expiresIn": expires_in,
                "userInfo": {
                    "id": db_user.id,
                    "nickname": db_user.username,
                    "avatar": db_user.avatar_url or db_user.wechat_avatar or '',
                    "vipLevel": 1 if db_user.is_member else 0,
                    "registerDate": db_user.created_at.isoformat() if db_user.created_at else datetime.now().isoformat(),
                    "isAuthorized": db_user.wechat_authorized or False
                }
            },
            message="登录成功"
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"微信小程序登录过程中发生未知错误: {e}")
        return APIResponse.error(
            message=f"微信小程序登录失败，请重试: {str(e)}",
            code=500
        )

@router.post("/refresh-token")
async def refresh_token(refresh_data: RefreshToken, db: Session = Depends(get_db)):
    """
    刷新令牌接口
    
    Args:
        refresh_data: 包含刷新令牌的数据
        db: 数据库会话
        
    Returns:
        返回新的访问令牌
    """
    try:
        from jose import jwt, JWTError
        from fastapi_app.core.config import settings
        
        # 验证刷新令牌
        payload = jwt.decode(
            refresh_data.refreshToken, 
            settings.SECRET_KEY, 
            algorithms=["HS256"]
        )
        
        # 检查令牌类型
        if payload.get("type") != "refresh":
            return APIResponse.error(
                message="无效的刷新令牌类型",
                code=401
            )
        
        # 获取用户ID
        user_id = payload.get("sub")
        if not user_id:
            return APIResponse.error(
                message="无效的刷新令牌",
                code=401
            )
            
        # 验证用户是否存在
        user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
        if not user:
            return APIResponse.error(
                message="用户不存在或已被禁用",
                code=401
            )
        
        # 创建新的访问令牌
        new_access_token = create_access_token(data={"sub": user_id})
        
        # 更新最后登录时间
        user.last_login_at = datetime.utcnow()
        db.commit()
        
        # 返回新令牌
        return APIResponse.success(
            data={
                "token": new_access_token,
                "expiresIn": 7200  # 2小时
            },
            message="刷新Token成功"
        )
        
    except JWTError:
        return APIResponse.error(
            message="刷新令牌无效或已过期",
            code=401
        )
    except Exception as e:
        logger.error(f"刷新Token过程中发生未知错误: {e}")
        return APIResponse.error(
            message="刷新Token失败，请重新登录",
            code=500
        )

@router.get("/dev/token")
async def generate_dev_token(
    use_real_user: bool = False, 
    custom_id: str = None, 
    db: Session = Depends(get_db)
):
    """
    仅用于开发环境：生成JWT令牌
    
    警告：此接口仅应在开发环境中使用，生产环境应禁用
    
    Args:
        use_real_user: 是否使用数据库中的真实用户
        custom_id: 自定义用户ID，当use_real_user=False时生效
        db: 数据库会话
        
    Returns:
        包含JWT令牌的响应
    """
    if use_real_user:
        # 获取数据库中的第一个用户
        user = db.query(User).filter(User.is_active == True).first()
        
        if not user:
            return APIResponse.error(
                message="数据库中没有可用用户",
                code=404
            )
        
        # 创建访问令牌
        access_token = create_access_token(data={"sub": user.id})
        
        return APIResponse.success(
            data={
                "token": access_token,
                "token_type": "bearer",
                "user_id": user.id,
                "user_nickname": user.username
            },
            message="真实用户令牌生成成功"
        )
    else:
        # 使用自定义ID或默认测试ID
        user_id = custom_id or "test_user_id"
        
        # 创建访问令牌
        access_token = create_access_token(data={"sub": user_id})
        
        return APIResponse.success(
            data={
                "token": access_token,
                "token_type": "bearer",
                "user_id": user_id
            },
            message="测试令牌生成成功"
        )

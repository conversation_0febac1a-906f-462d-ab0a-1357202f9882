from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from fastapi_app.core.database import get_db
from fastapi_app.core.security import get_current_user
from fastapi_app.models.user import User as UserModel
from fastapi_app.crud import order as crud_order
from fastapi_app.schemas import order as schemas_order

router = APIRouter()

@router.post("/create", response_model=schemas_order.Order)
def create_order(
    order_in: schemas_order.OrderCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Create an order from the user's cart.
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="未登录")
    order = crud_order.create_order_from_cart(db, user_id=current_user.id, order_in=order_in)
    if not order:
        raise HTTPException(status_code=400, detail="Could not create order. Check cart or address.")
    return order

@router.get("/list", response_model=List[schemas_order.Order])
def get_order_list(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Get current user's order list.
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="未登录")
    return crud_order.get_order_list(db, user_id=current_user.id)

@router.get("/{order_id}", response_model=schemas_order.Order)
def get_order_detail(
    order_id: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Get details of a specific order.
    """
    order = crud_order.get_order_detail(db, order_id=order_id, user_id=current_user.id)
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    return order

@router.post("/{order_id}/pay")
def pay_order(
    order_id: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Pay for an order (simulation).
    """
    order = crud_order.pay_order(db, order_id=order_id, user_id=current_user.id)
    if not order:
        raise HTTPException(status_code=404, detail="Order not found or cannot be paid")
    return {"message": "支付成功", "order_id": order_id, "status": order.status}

@router.post("/{order_id}/cancel")
def cancel_order(
    order_id: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Cancel an order.
    """
    order = crud_order.cancel_order(db, order_id=order_id, user_id=current_user.id)
    if not order:
        raise HTTPException(status_code=404, detail="Order not found or cannot be cancelled")
    return {"message": "订单已取消", "order_id": order_id, "status": order.status}

@router.post("/{order_id}/confirm")
def confirm_order(
    order_id: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Confirm receipt of order.
    """
    order = crud_order.confirm_order(db, order_id=order_id, user_id=current_user.id)
    if not order:
        raise HTTPException(status_code=404, detail="Order not found or cannot be confirmed")
    return {"message": "订单已确认收货", "order_id": order_id, "status": order.status} 
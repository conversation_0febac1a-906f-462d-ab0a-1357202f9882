"""
增强的茶品产品API端点
整合k_api的茶品管理接口到主后端项目
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional
import logging

from fastapi_app.core.database import get_db
from fastapi_app.core.response import APIResponse
from fastapi_app.core.security import get_current_user
from fastapi_app.models.user import User
from fastapi_app.services.tea_enhanced import tea_product_service

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/all")
def get_all_products(
    page: int = Query(1, ge=1, description="页码，默认为1"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量，默认为20"),
    user_id: Optional[str] = Query(None, description="用户ID，如果提供则获取个性化推荐"),
    db: Session = Depends(get_db)
):
    """
    获取所有茶产品列表，支持分页
    
    Args:
        page: 页码，默认为1
        page_size: 每页数量，默认为20
        user_id: 用户ID，如果提供则获取个性化推荐
        db: 数据库会话
        
    Returns:
        包含产品列表、分类和分页信息的响应
    """
    try:
        result = tea_product_service.get_all_products(
            db=db,
            page=page,
            page_size=page_size,
            user_id=user_id
        )
        
        return APIResponse.success(
            data=result,
            message="获取产品列表成功"
        )
        
    except HTTPException as e:
        return APIResponse.error(
            message=e.detail,
            code=e.status_code
        )
    except Exception as e:
        logger.error(f"获取产品列表时发生未知错误: {e}")
        return APIResponse.error(
            message=f"获取产品列表失败: {str(e)}",
            code=500
        )

@router.get("/{product_id}")
def get_product_detail(
    product_id: str,
    user_id: Optional[str] = Query(None, description="用户ID"),
    db: Session = Depends(get_db)
):
    """
    获取产品详情
    
    Args:
        product_id: 产品ID
        user_id: 用户ID，可选
        db: 数据库会话
        
    Returns:
        产品详情数据
    """
    try:
        result = tea_product_service.get_product_by_id(
            db=db,
            product_id=product_id,
            user_id=user_id
        )
        
        return APIResponse.success(
            data=result,
            message="获取产品详情成功"
        )
        
    except HTTPException as e:
        return APIResponse.error(
            message=e.detail,
            code=e.status_code
        )
    except Exception as e:
        logger.error(f"获取产品详情时发生未知错误: {e}")
        return APIResponse.error(
            message=f"获取产品详情失败: {str(e)}",
            code=500
        )

@router.get("/category/{category}")
def get_products_by_category(
    category: str,
    page: int = Query(1, ge=1, description="页码，默认为1"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量，默认为20"),
    user_id: Optional[str] = Query(None, description="用户ID"),
    db: Session = Depends(get_db)
):
    """
    根据分类获取产品列表
    
    Args:
        category: 分类属性名（如：is_seasonal, is_help_sleep等）
        page: 页码，默认为1
        page_size: 每页数量，默认为20
        user_id: 用户ID，可选
        db: 数据库会话
        
    Returns:
        分类产品列表
    """
    try:
        result = tea_product_service.get_products_by_category(
            db=db,
            category=category,
            page=page,
            page_size=page_size,
            user_id=user_id
        )
        
        return APIResponse.success(
            data=result,
            message=f"获取{category}分类产品成功"
        )
        
    except HTTPException as e:
        return APIResponse.error(
            message=e.detail,
            code=e.status_code
        )
    except Exception as e:
        logger.error(f"获取分类产品时发生未知错误: {e}")
        return APIResponse.error(
            message=f"获取分类产品失败: {str(e)}",
            code=500
        )

# 认证版本的接口（需要登录）
@router.get("/auth/all")
def get_all_products_auth(
    page: int = Query(1, ge=1, description="页码，默认为1"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量，默认为20"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取所有茶产品列表（需要认证）
    
    Args:
        page: 页码，默认为1
        page_size: 每页数量，默认为20
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        包含产品列表、分类和分页信息的响应
    """
    try:
        result = tea_product_service.get_all_products(
            db=db,
            page=page,
            page_size=page_size,
            user_id=current_user.id
        )
        
        return APIResponse.success(
            data=result,
            message="获取产品列表成功"
        )
        
    except HTTPException as e:
        return APIResponse.error(
            message=e.detail,
            code=e.status_code
        )
    except Exception as e:
        logger.error(f"获取产品列表时发生未知错误: {e}")
        return APIResponse.error(
            message=f"获取产品列表失败: {str(e)}",
            code=500
        )

@router.get("/auth/{product_id}")
def get_product_detail_auth(
    product_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取产品详情（需要认证）
    
    Args:
        product_id: 产品ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        产品详情数据
    """
    try:
        result = tea_product_service.get_product_by_id(
            db=db,
            product_id=product_id,
            user_id=current_user.id
        )
        
        return APIResponse.success(
            data=result,
            message="获取产品详情成功"
        )
        
    except HTTPException as e:
        return APIResponse.error(
            message=e.detail,
            code=e.status_code
        )
    except Exception as e:
        logger.error(f"获取产品详情时发生未知错误: {e}")
        return APIResponse.error(
            message=f"获取产品详情失败: {str(e)}",
            code=500
        )

@router.get("/auth/category/{category}")
def get_products_by_category_auth(
    category: str,
    page: int = Query(1, ge=1, description="页码，默认为1"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量，默认为20"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    根据分类获取产品列表（需要认证）
    
    Args:
        category: 分类属性名
        page: 页码，默认为1
        page_size: 每页数量，默认为20
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        分类产品列表
    """
    try:
        result = tea_product_service.get_products_by_category(
            db=db,
            category=category,
            page=page,
            page_size=page_size,
            user_id=current_user.id
        )
        
        return APIResponse.success(
            data=result,
            message=f"获取{category}分类产品成功"
        )
        
    except HTTPException as e:
        return APIResponse.error(
            message=e.detail,
            code=e.status_code
        )
    except Exception as e:
        logger.error(f"获取分类产品时发生未知错误: {e}")
        return APIResponse.error(
            message=f"获取分类产品失败: {str(e)}",
            code=500
        )

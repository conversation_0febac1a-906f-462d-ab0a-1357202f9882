<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>体质评估报告 - Mastea</title>
    <!-- 添加Chart.js库以支持雷达图 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 引入外部CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='/css/report_assessment.css') }}">
</head>
<body>
    <div class="container">
        <h1>中医体质评估与健康管理</h1>
        
        <div id="loading">加载中，请稍候...</div>
        <div id="error-message"></div>

        <!-- 新的基础体质评估问卷区域 -->
        <div id="questionnaire-section">
            <h2>基础体质评估问卷</h2>
            <form id="constitution-questionnaire-form">
                <div class="question-group">
                    <label for="q-gender">1. 性别：</label>
                    <select id="q-gender" name="gender">
                        <option value="男">男</option>
                        <option value="女">女</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="question-group">
                    <label for="q-age_group">2. 年龄段：</label>
                    <select id="q-age_group" name="age_group">
                        <option value="<18">&lt;18</option>
                        <option value="18-30">18-30</option>
                        <option value="31-45">31-45</option>
                        <option value="46-60">46-60</option>
                        <option value=">60">&gt;60</option>
                    </select>
                </div>

                <div class="question-group">
                    <label>3. 您最常出现的身体不适是？ (单选)</label>
                    <div><input type="radio" name="q3_core_symptom" value="疲劳乏力，容易感冒"><label>疲劳乏力，容易感冒</label></div>
                    <div><input type="radio" name="q3_core_symptom" value="怕冷，手脚冰凉"><label>怕冷，手脚冰凉</label></div>
                    <div><input type="radio" name="q3_core_symptom" value="口干咽燥，手足心热"><label>口干咽燥，手足心热</label></div>
                    <div><input type="radio" name="q3_core_symptom" value="身体困重，面部油腻"><label>身体困重，面部油腻</label></div>
                    <div><input type="radio" name="q3_core_symptom" value="口苦口臭，皮肤长痘"><label>口苦口臭，皮肤长痘</label></div>
                    <div><input type="radio" name="q3_core_symptom" value="面色暗沉，局部刺痛"><label>面色暗沉，局部刺痛</label></div>
                    <div><input type="radio" name="q3_core_symptom" value="胸闷胁痛，情绪抑郁"><label>胸闷胁痛，情绪抑郁</label></div>
                    <div><input type="radio" name="q3_core_symptom" value="过敏频发，皮肤划痕"><label>过敏频发，皮肤划痕</label></div>
                    <div><input type="radio" name="q3_core_symptom" value="无明显不适"><label>无明显不适</label></div>
                </div>

                <div class="question-group">
                    <label>4. 您是否容易出汗？ (可多选)</label>
                    <div><input type="checkbox" name="q4_sweating" value="白天动则大汗"><label>白天动则大汗</label></div>
                    <div><input type="checkbox" name="q4_sweating" value="夜间盗汗明显"><label>夜间盗汗明显</label></div>
                    <div><input type="checkbox" name="q4_sweating" value="头部/胸口多汗"><label>头部/胸口多汗</label></div>
                    <div><input type="checkbox" name="q4_sweating" value="几乎不出汗"><label>几乎不出汗</label></div>
                    <div><input type="checkbox" name="q4_sweating" value="无明显异常"><label>无明显异常</label></div>
                </div>

                <div class="question-group">
                    <label>5. 您的大便状态是？ (单选)</label>
                    <div><input type="radio" name="q5_stool" value="黏滞不爽，冲不干净"><label>黏滞不爽，冲不干净</label></div>
                    <div><input type="radio" name="q5_stool" value="干燥硬结，如羊粪球"><label>干燥硬结，如羊粪球</label></div>
                    <div><input type="radio" name="q5_stool" value="稀溏不成形"><label>稀溏不成形</label></div>
                    <div><input type="radio" name="q5_stool" value="先干后溏，时好时坏"><label>先干后溏，时好时坏</label></div>
                    <div><input type="radio" name="q5_stool" value="规律正常"><label>规律正常</label></div>
                </div>

                <div class="question-group">
                    <label>6. 您的睡眠状态如何？ (单选)</label>
                    <div><input type="radio" name="q6_sleep" value="入睡困难、多梦易醒"><label>入睡困难、多梦易醒</label></div>
                    <div><input type="radio" name="q6_sleep" value="失眠潮热、盗汗"><label>失眠潮热、盗汗</label></div>
                    <div><input type="radio" name="q6_sleep" value="嗜睡乏力，睡不醒"><label>嗜睡乏力，睡不醒</label></div>
                    <div><input type="radio" name="q6_sleep" value="夜尿频繁，起夜≥2次"><label>夜尿频繁，起夜≥2次</label></div>
                    <div><input type="radio" name="q6_sleep" value="睡眠正常"><label>睡眠正常</label></div>
                </div>

                <div class="question-group">
                    <label>7. 您对季节变化的反应？ (可多选)</label>
                    <div><input type="checkbox" name="q7_season_response" value="冬季怕冷，穿再多也冷"><label>冬季怕冷，穿再多也冷</label></div>
                    <div><input type="checkbox" name="q7_season_response" value="夏季上火，烦躁长痘"><label>夏季上火，烦躁长痘</label></div>
                    <div><input type="checkbox" name="q7_season_response" value="梅雨季头重如裹"><label>梅雨季头重如裹</label></div>
                    <div><input type="checkbox" name="q7_season_response" value="春秋季鼻痒打喷嚏"><label>春秋季鼻痒打喷嚏</label></div>
                    <div><input type="checkbox" name="q7_season_response" value="四季适应良好"><label>四季适应良好</label></div>
                </div>

                <div class="question-group">
                    <label>8. 您的饮食偏好？ (单选)</label>
                    <div><input type="radio" name="q8_diet_preference" value="油腻辛辣"><label>油腻辛辣</label></div>
                    <div><input type="radio" name="q8_diet_preference" value="生冷寒凉"><label>生冷寒凉</label></div>
                    <div><input type="radio" name="q8_diet_preference" value="甜食糕点"><label>甜食糕点</label></div>
                    <div><input type="radio" name="q8_diet_preference" value="重咸腌制"><label>重咸腌制</label></div>
                    <div><input type="radio" name="q8_diet_preference" value="规律清淡"><label>规律清淡</label></div>
                </div>

                <div class="question-group">
                    <label>9. 您是否长期有以下习惯？ (可多选)</label>
                    <div><input type="checkbox" name="q9_long_term_habits" value="熬夜（23点后睡）"><label>熬夜（23点后睡）</label></div>
                    <div><input type="checkbox" name="q9_long_term_habits" value="久坐（每天≥6小时）"><label>久坐（每天≥6小时）</label></div>
                    <div><input type="checkbox" name="q9_long_term_habits" value="高压焦虑（常感紧张）"><label>高压焦虑（常感紧张）</label></div>
                    <div><input type="checkbox" name="q9_long_term_habits" value="过度运动（易耗气）"><label>过度运动（易耗气）</label></div>
                    <div><input type="checkbox" name="q9_long_term_habits" value="作息规律"><label>作息规律</label></div>
                </div>
                
                <div class="question-group">
                    <label>10. 您的体型倾向？ (单选)</label>
                    <div><input type="radio" name="q10_body_type" value="肥胖，肉松软"><label>肥胖，肉松软</label></div>
                    <div><input type="radio" name="q10_body_type" value="消瘦，肌肉薄弱"><label>消瘦，肌肉薄弱</label></div>
                    <div><input type="radio" name="q10_body_type" value="正常但局部胖"><label>正常但局部胖</label></div>
                    <div><input type="radio" name="q10_body_type" value="肌肉发达，体脂低"><label>肌肉发达，体脂低</label></div>
                    <div><input type="radio" name="q10_body_type" value="体重波动大"><label>体重波动大</label></div>
                </div>

                <div class="question-group">
                    <label>11. 皮肤状态？ (单选)</label>
                    <div><input type="radio" name="q11_skin_status" value="油腻长痘，毛孔粗大"><label>油腻长痘，毛孔粗大</label></div>
                    <div><input type="radio" name="q11_skin_status" value="干燥脱屑，易生皱纹"><label>干燥脱屑，易生皱纹</label></div>
                    <div><input type="radio" name="q11_skin_status" value="暗沉色斑，黑眼圈"><label>暗沉色斑，黑眼圈</label></div>
                    <div><input type="radio" name="q11_skin_status" value="敏感红痒，易起疹"><label>敏感红痒，易起疹</label></div>
                    <div><input type="radio" name="q11_skin_status" value="光滑红润"><label>光滑红润</label></div>
                </div>

                <div class="question-group">
                    <label>12. 您希望优先调理的方向？ (单选)</label>
                    <div><input type="radio" name="q12_priority_conditioning" value="补气防感冒"><label>补气防感冒</label></div>
                    <div><input type="radio" name="q12_priority_conditioning" value="温阳暖手脚"><label>温阳暖手脚</label></div>
                    <div><input type="radio" name="q12_priority_conditioning" value="滋阴润燥"><label>滋阴润燥</label></div>
                    <div><input type="radio" name="q12_priority_conditioning" value="祛痘清火"><label>祛痘清火</label></div>
                    <div><input type="radio" name="q12_priority_conditioning" value="减重排湿"><label>减重排湿</label></div>
                    <div><input type="radio" name="q12_priority_conditioning" value="活血消斑"><label>活血消斑</label></div>
                    <div><input type="radio" name="q12_priority_conditioning" value="疏肝解郁"><label>疏肝解郁</label></div>
                    <div><input type="radio" name="q12_priority_conditioning" value="抗敏止痒"><label>抗敏止痒</label></div>
                </div>

                <div class="form-actions">
                    <button type="button" id="submit-questionnaire-button">提交问卷并生成报告</button>
                    <button type="button" id="cancel-questionnaire-button">取消</button>
                </div>
            </form>
        </div>

        <!-- 报告列表区域 -->
        <div id="report-list-section">
            <h2>您的历史基础体质报告</h2>
            <ul id="report-list">
                <!-- 历史报告将通过JS动态加载到这里 -->
            </ul>
            <button id="start-new-assessment-button">开始新的基础体质评估 (问卷)</button>
            
            <h2 style="margin-top: 30px;">您的历史综合主诉报告</h2>
            <ul id="complaint-report-list">
                <!-- 综合主诉报告将通过JS动态加载到这里 -->
            </ul>
        </div>

        <!-- 报告展示区域 -->
        <div id="report-display-section">
            <h3 id="report-display-title">报告详情</h3>
            <div id="report-display-content"></div>
            <button class="close-report-button" id="close-report-display-button">关闭报告</button>
        </div>

        <!-- 聊天评估区域 (主诉评估时使用) -->
        <div id="chat-section">
            <h2 id="chat-title">体质评估进行中...</h2>
            <div id="chatbox">
                <!-- 聊天消息将显示在这里 -->
            </div>
            <div id="chat-loading">AI 正在输入中...</div>
            <div id="input-area">
                <input type="text" id="message-input" placeholder="请输入您的回答..." disabled>
                <button id="send-button" disabled>发送</button>
            </div>
            <button id="end-assessment-button" style="margin-top: 10px; padding: 10px 15px; background-color: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; display: none;">结束对话并生成报告</button>
        </div>
    </div>

    <script>
        // Pass server-side variables to JavaScript
        const initialReportId = "{{ report_id_to_load|default('', true) }}";
        const initialReportType = "{{ report_type_to_load|default('', true) }}";
    </script>
    <!-- 引入外部JS -->
    <script src="{{ url_for('static', path='/js/report_assessment.js') }}"></script>
</body>
</html>

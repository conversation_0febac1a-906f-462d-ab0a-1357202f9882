<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ma<PERSON>a 后台管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 先引入Chart.js，确保它在我们的代码之前加载 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- Chart.js加载检查 -->
    <script>
        window.addEventListener('load', function() {
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未能正确加载，将尝试重新加载');
                // 尝试重新加载Chart.js
                var script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
                script.onload = function() {
                    console.log('Chart.js重新加载成功');
                    // 如果页面已经加载完毕，重新初始化图表
                    if (window.drawSalesChart && window.drawUserChart && window.salesChartData && window.userChartData) {
                        setTimeout(function() {
                            drawSalesChart(window.salesChartData);
                            drawUserChart(window.userChartData);
                        }, 500);
                    }
                };
                script.onerror = function() {
                    console.error('Chart.js重新加载失败，图表功能将不可用');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            padding-top: 20px;
        }
        .sidebar a {
            color: #f8f9fa;
            padding: 10px 15px;
            text-decoration: none;
            display: block;
        }
        .sidebar a:hover {
            background-color: #495057;
        }
        .content {
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
        .active {
            background-color: #495057;
        }
        /* 加载指示器样式 */
        .chart-container {
            position: relative;
            min-height: 200px;
        }
        .chart-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }
        .chart-error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <h3 class="text-center text-light mb-4">Mastea 管理</h3>
                <a href="#dashboard" class="active" onclick="loadPage('dashboard')">仪表盘</a>
                <a href="#users" onclick="loadPage('users')">用户管理</a>
                <a href="#goods" onclick="loadPage('goods')">商品管理</a>
                <a href="#orders" onclick="loadPage('orders')">订单管理</a>
                <a href="#chats" onclick="loadPage('chats')">聊天记录</a>
            </div>
            
            <!-- 主体内容 -->
            <div class="col-md-10 content">
                <div id="pageContent">
                    <!-- 页面内容将通过 JavaScript 加载 -->
                    <div id="dashboard">
                        <h2 class="mb-4">仪表盘</h2>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">用户总数</h5>
                                        <h2 class="card-text" id="userCount">-</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">商品总数</h5>
                                        <h2 class="card-text" id="goodsCount">-</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">订单总数</h5>
                                        <h2 class="card-text" id="orderCount">-</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">聊天统计</h5>
                                        <p class="card-text mb-1"><small>PG 消息:</small> <span id="pgChatCount">-</span></p>
                                        <p class="card-text mb-1"><small>Mongo 对话文档:</small> <span id="mongoChatDocsCount">-</span></p>
                                        <p class="card-text mb-0"><small>Mongo AI 对话:</small> <span id="mongoAiConvsCount">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">今日数据</div>
                                    <div class="card-body">
                                        <p>新增用户：<span id="todayUsers">-</span></p>
                                        <p>新增订单：<span id="todayOrders">-</span></p>
                                        <p>销售额：￥<span id="todaySales">-.--</span></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">销售趋势</div>
                                    <div class="card-body chart-container">
                                        <div class="chart-loading" id="salesChartLoading">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </div>
                                        <canvas id="salesChart" height="200"></canvas>
                                        <div class="chart-error d-none" id="salesChartError">
                                            图表加载失败，请检查浏览器控制台或刷新页面重试
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">用户增长</div>
                                    <div class="card-body chart-container">
                                        <div class="chart-loading" id="userChartLoading">
                                            <div class="spinner-border text-success" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </div>
                                        <canvas id="userChart" height="200"></canvas>
                                        <div class="chart-error d-none" id="userChartError">
                                            图表加载失败，请检查浏览器控制台或刷新页面重试
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 用户列表模板 -->
    <template id="users-template">
        <h2 class="mb-4">用户管理</h2>
        <div class="card">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="userSearchInput" placeholder="搜索用户...">
                            <button class="btn btn-outline-secondary" type="button" id="userSearchBtn">搜索</button>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>手机号</th>
                                <th>注册时间</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <!-- 用户数据将通过 JavaScript 加载 -->
                        </tbody>
                    </table>
                </div>
                <nav>
                    <ul class="pagination" id="userPagination">
                        <!-- 分页将通过 JavaScript 生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </template>
    
    <!-- 商品列表模板 -->
    <template id="goods-template">
        <h2 class="mb-4">商品管理</h2>
        <div class="d-flex justify-content-end mb-3">
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addGoodsModal">+ 添加新商品</button>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="goodsSearchInput" placeholder="搜索商品...">
                            <button class="btn btn-outline-secondary" type="button" id="goodsSearchBtn">搜索</button>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>图片</th>
                                <th>商品名称</th>
                                <th>价格</th>
                                <th>库存</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="goodsTableBody">
                            <!-- 商品数据将通过 JavaScript 加载 -->
                        </tbody>
                    </table>
                </div>
                <nav>
                    <ul class="pagination" id="goodsPagination">
                        <!-- 分页将通过 JavaScript 生成 -->
                    </ul>
                </nav>
            </div>
        </div>

        <!-- 添加商品 Modal -->
        <div class="modal fade" id="addGoodsModal" tabindex="-1" aria-labelledby="addGoodsModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addGoodsModalLabel">添加新商品</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addGoodsForm">
                            <div class="mb-3">
                                <label for="goodsName" class="form-label">商品名称*</label>
                                <input type="text" class="form-control" id="goodsName" required>
                            </div>
                            <div class="mb-3">
                                <label for="goodsDescription" class="form-label">描述</label>
                                <textarea class="form-control" id="goodsDescription" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="goodsPrice" class="form-label">价格*</label>
                                <input type="number" step="0.01" class="form-control" id="goodsPrice" required>
                            </div>
                            <div class="mb-3">
                                <label for="goodsStock" class="form-label">库存</label>
                                <input type="number" class="form-control" id="goodsStock" value="0">
                            </div>
                            <div class="mb-3">
                                <label for="goodsCategory" class="form-label">分类</label>
                                <input type="text" class="form-control" id="goodsCategory">
                            </div>
                            <div class="alert alert-danger mt-3 d-none" role="alert" id="addGoodsError"></div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="submitAddGoodsForm()">保存商品</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 上传图片 Modal -->
        <div class="modal fade" id="uploadImageModal" tabindex="-1" aria-labelledby="uploadImageModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="uploadImageModalLabel">上传商品图片</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="uploadImageForm" enctype="multipart/form-data">
                            <input type="hidden" id="uploadGoodsId">
                            <div class="mb-3">
                                <label for="goodsImageFile" class="form-label">选择图片文件</label>
                                <input class="form-control" type="file" id="goodsImageFile" name="image" accept="image/*" required>
                            </div>
                            <div class="alert alert-danger mt-3 d-none" role="alert" id="uploadImageError"></div>
                            <div class="alert alert-success mt-3 d-none" role="alert" id="uploadImageSuccess"></div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="submitUploadImage()">上传图片</button>
                    </div>
                </div>
            </div>
        </div>
    </template>
    
    <!-- 订单列表模板 -->
    <template id="orders-template">
        <h2 class="mb-4">订单管理</h2>
        <div class="card">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="orderSearchInput" placeholder="搜索订单...">
                            <button class="btn btn-outline-secondary" type="button" id="orderSearchBtn">搜索</button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <select class="form-select" id="orderStatusFilter">
                            <option value="">所有状态</option>
                            <option value="pending">待付款</option>
                            <option value="paid">已付款</option>
                            <option value="delivered">已发货</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>用户ID</th>
                                <th>收件人</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody id="orderTableBody">
                            <!-- 订单数据将通过 JavaScript 加载 -->
                        </tbody>
                    </table>
                </div>
                <nav>
                    <ul class="pagination" id="orderPagination">
                        <!-- 分页将通过 JavaScript 生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </template>
    
    <!-- 聊天记录模板 -->
    <template id="chats-template">
        <h2 class="mb-4">聊天记录</h2>
        <div class="card">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="chatUserIdInput" placeholder="用户ID">
                            <button class="btn btn-outline-secondary" type="button" id="chatUserIdBtn">筛选</button>
                        </div>
                    </div>
                </div>
                <div class="chat-messages" id="chatMessages">
                    <!-- 聊天消息将通过 JavaScript 加载 -->
                </div>
                <nav>
                    <ul class="pagination" id="chatPagination">
                        <!-- 分页将通过 JavaScript 生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </template>
    
    <!-- 引入Bootstrap和自定义JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/admin.js"></script>
    <script src="/static/js/admin_goods.js"></script>
    
    <!-- Toast消息组件 -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
        <div id="liveToast" class="toast hide" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">系统消息</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                操作成功
            </div>
        </div>
    </div>
</body>
</html> 
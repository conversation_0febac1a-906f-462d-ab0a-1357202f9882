"""update user model to allow null email and mobile

Revision ID: 15a32b8c5a14
Revises: c766cb4375e4
Create Date: 2025-06-11 22:30:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '15a32b8c5a14'
down_revision: Union[str, None] = 'c766cb4375e4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 修改users表，将email和mobile字段改为可为空
    with op.batch_alter_table('users', schema=None) as batch_op:
        # 首先删除unique约束
        batch_op.drop_constraint('users_email_key', type_='unique')
        batch_op.drop_constraint('users_mobile_key', type_='unique')
        
        # 修改字段为nullable
        batch_op.alter_column('email',
                    existing_type=sa.String(length=120),
                    nullable=True)
        batch_op.alter_column('mobile',
                    existing_type=sa.String(length=20),
                    nullable=True)
        
        # 重新添加unique约束
        batch_op.create_unique_constraint('users_email_key', ['email'])
        batch_op.create_unique_constraint('users_mobile_key', ['mobile'])


def downgrade() -> None:
    # 恢复users表，将email和mobile字段改为不可为空
    with op.batch_alter_table('users', schema=None) as batch_op:
        # 首先删除unique约束
        batch_op.drop_constraint('users_email_key', type_='unique')
        batch_op.drop_constraint('users_mobile_key', type_='unique')
        
        # 修改字段为non-nullable
        batch_op.alter_column('email',
                    existing_type=sa.String(length=120),
                    nullable=False)
        batch_op.alter_column('mobile',
                    existing_type=sa.String(length=20),
                    nullable=False)
        
        # 重新添加unique约束
        batch_op.create_unique_constraint('users_email_key', ['email'])
        batch_op.create_unique_constraint('users_mobile_key', ['mobile']) 
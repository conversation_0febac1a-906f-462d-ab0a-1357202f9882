"""Add Co<PERSON>laintReport model

Revision ID: 150af4385c23
Revises: 152c4760aee3
Create Date: 2025-05-14 21:41:52.011482

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '150af4385c23'
down_revision = '152c4760aee3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('complaint_reports',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('user_id', sa.String(length=32), nullable=False),
    sa.Column('base_constitution_report_id', sa.String(length=32), nullable=True),
    sa.Column('report_data_json', sa.Text(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['base_constitution_report_id'], ['constitution_reports.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('complaint_reports', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_complaint_reports_base_constitution_report_id'), ['base_constitution_report_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_complaint_reports_user_id'), ['user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('complaint_reports', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_complaint_reports_user_id'))
        batch_op.drop_index(batch_op.f('ix_complaint_reports_base_constitution_report_id'))

    op.drop_table('complaint_reports')
    # ### end Alembic commands ###

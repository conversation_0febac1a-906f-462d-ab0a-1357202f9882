# Mastea Flask 智能茶品推荐系统

## 快速开始

### 一键部署 (推荐)

```bash
# 克隆项目
git clone https://github.com/your-org/mastea-flask.git
cd mastea-flask

# 一键部署到Docker
./scripts/quick-deploy.sh
```

访问 http://localhost:8000 开始使用

### 手动部署

```bash
# 1. 构建Docker镜像
./scripts/build.sh

# 2. 启动服务
docker-compose up -d

# 3. 初始化数据库
./scripts/init-database.sh localhost 5432 mastea wyh12257410 mastea_prod
```

## 核心功能

- **体质评估**: 基于中医理论的体质分析
- **AI主诉评估**: 智能化症状问诊和评估
- **个性化推荐**: 根据体质和症状推荐茶品
- **用户管理**: 完整的用户注册、登录、资料管理
- **购物车和订单**: 完整的电商功能
- **微信小程序**: 支持微信小程序登录

## 系统架构

- **后端**: FastAPI + Python 3.12
- **数据库**: PostgreSQL 15
- **容器化**: Docker + Docker Compose
- **AI服务**: SiliconFlow API (DeepSeek-V3)

## 快速链接

- 📚 [详细使用指南](USAGE_GUIDE.md)
- 🚀 [部署说明](DEPLOYMENT.md)
- 🌐 [API文档](http://localhost:8000/docs) (启动后可访问)
- 🎛️ [管理界面](http://localhost:8000/dashboard_enhanced) (启动后可访问)

## 管理命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 数据库操作
./scripts/init-database.sh <host> <port> <user> <password> <db>
```

## 环境配置

主要环境变量：

```bash
# 数据库
DATABASE_URL=postgresql://mastea:password@localhost:5432/mastea_prod

# AI服务
SILICONFLOW_API_KEY=your-api-key
AI_MODEL_ID=deepseek-ai/DeepSeek-V3

# 微信小程序
WECHAT_APP_ID=your-app-id
WECHAT_APP_SECRET=your-app-secret
```

## 版本信息

- **版本**: 1.0.0
- **更新日期**: 2025-07-16
- **Python版本**: 3.12+
- **Docker版本**: 20.10+

## 支持

- 📧 技术支持: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-org/mastea-flask/issues)
- 📖 文档: [项目Wiki](https://github.com/your-org/mastea-flask/wiki)
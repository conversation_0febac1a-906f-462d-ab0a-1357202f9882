# 微信小程序API服务

## 项目简介

Mastea是一个基于FastAPI和SQLAlchemy开发的API服务，为茶品小程序提供后端支持。主要功能包括：

- 微信小程序用户系统：用户认证、登录和信息管理
- 体质测试与报告：分析用户体质类型并提供相应建议
- 茶品推荐：基于用户体质提供个性化茶品推荐
- 购物车系统：支持普通茶品和定制茶品的购物功能

## 项目结构

```
.
├── core/                   # 核心功能模块
│   ├── __init__.py        # 包初始化文件
│   ├── auth.py            # 认证相关功能
│   ├── schemas.py         # 数据模型定义
│   └── security.py        # 安全相关功能（JWT令牌等）
│
├── services/              # 服务层模块
│   ├── __init__.py        # 服务层入口
│   ├── user_service.py    # 用户服务
│   ├── body_report_service.py # 体质报告服务
│   ├── tea_product_service.py # 茶品服务
│   ├── cart_service.py    # 购物车服务
│   └── wechat_service.py  # 微信服务
│
├── sql/                   # 数据库相关
│   ├── __init__.py        # 数据库模块入口
│   ├── database.py        # 数据库连接和会话管理
│   └── db_model.py        # 数据库ORM模型定义
│
├── docs/                  # 项目文档
│   ├── DEVLOG.md          # 开发日志
│   ├── 购物车接口文档.md     # 购物车API文档
│   ├── 推荐产品接口文档.md    # 茶品推荐API文档
│   ├── 报告详情接口文档.md    # 体质报告详情API文档
│   ├── 报告总览接口文档.md    # 体质报告总览API文档
│   └── 登录接口文档.md       # 微信登录API文档
│
├── main.py                # 主应用入口
├── requirements.txt       # 依赖项
└── README.md              # 项目说明
```

## 功能模块说明

### core

- `auth.py`: 处理认证相关逻辑
- `schemas.py`: 定义API请求和响应的数据模型
- `security.py`: 处理JWT令牌生成和验证

### services

- `user_service.py`: 处理用户创建、查询和更新
- `body_report_service.py`: 处理体质报告生成和查询
- `tea_product_service.py`: 处理茶品数据和推荐
- `cart_service.py`: 购物车功能管理
- `wechat_service.py`: 处理微信小程序登录和用户信息获取

### sql

- `database.py`: 数据库连接和会话管理
- `db_model.py`: SQLAlchemy ORM模型定义，包含所有数据模型
- `__init__.py`: 导出常用的数据库功能，简化导入

## 主要API

### 用户认证

- `/wechat/login`: 微信小程序登录接口
- `/auth/refresh-token`: 刷新访问令牌接口

### 体质报告

- `/report/overview`: 获取体质报告概览
- `/report/detail`: 获取体质报告详情
- `/report/tea-recommend`: 获取茶品推荐

### 购物车

- `/cart/get`: 获取用户购物车
- `/cart/add`: 添加商品到购物车
- `/cart/update`: 更新购物车商品数量
- `/cart/remove`: 删除购物车商品
- `/cart/clear`: 清空购物车

## 特别说明 - 开发中的待定内容

> 以下内容是开发过程中的设计决策，供开发团队参考：

1. **体质报告分数处理**：
   - `UserBodyReports`表不直接存储体质分数，而是通过查询对应的`UserBodyReportScores`表确定
   - 主体质：分数最高的体质类型（如有相同分数，根据`BodyConstitutionTypes`中的`display_order`取排序最小的）
   - 兼有体质：除主体质外分数≥60分的所有体质类型

2. **节气建议个性化**：
   - `UserBodyReports`中各个节气的日常建议暂时按每个体质报告单独生成

3. **茶品推荐策略**：
   - 推荐茶产品暂时按每个体质报告单独生成，而非按主体质类型统一

4. **数据库命名约定**：
   - 为避免表名重复，购物车商品表命名为`cart_products`（而非`cart_items`）

## 开发文档

详细API文档请参阅`docs`目录下的相关文档：
- [登录接口文档](docs/登录接口文档.md)
- [体质报告总览接口文档](docs/报告总览接口文档.md)
- [体质报告详情接口文档](docs/报告详情接口文档.md)
- [茶品推荐接口文档](docs/推荐产品接口文档.md)
- [购物车接口文档](docs/购物车接口文档.md)
- [开发日志](docs/DEVLOG.md) 
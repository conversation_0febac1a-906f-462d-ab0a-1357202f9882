"""Add ConstitutionReport model for TCM constitution assessment

Revision ID: 152c4760aee3
Revises: c766cb4375e4
Create Date: 2025-05-08 22:24:10.580707

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '152c4760aee3'
down_revision = 'c766cb4375e4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('constitution_reports',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('report_data_json', sa.Text(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('constitution_reports', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_constitution_reports_user_id'), ['user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('constitution_reports', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_constitution_reports_user_id'))

    op.drop_table('constitution_reports')
    # ### end Alembic commands ###

"""update chat_message to allow null user_id

Revision ID: 25a32b8c5b15
Revises: 15a32b8c5a14
Create Date: 2025-06-11 22:40:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '25a32b8c5b15'
down_revision: Union[str, None] = '15a32b8c5a14'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 修改chat_messages表，将user_id字段改为可为空
    with op.batch_alter_table('chat_messages', schema=None) as batch_op:
        # 修改字段为nullable
        batch_op.alter_column('user_id',
                    existing_type=sa.String(length=32),
                    nullable=True)


def downgrade() -> None:
    # 恢复chat_messages表，将user_id字段改为不可为空
    with op.batch_alter_table('chat_messages', schema=None) as batch_op:
        # 修改字段为non-nullable
        batch_op.alter_column('user_id',
                    existing_type=sa.String(length=32),
                    nullable=False) 
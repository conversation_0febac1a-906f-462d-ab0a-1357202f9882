"""Add WeChat and extended user fields

Revision ID: add_wechat_user_fields
Revises: 
Create Date: 2025-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_wechat_user_fields'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Add WeChat and extended user fields to users table"""
    
    # Add WeChat related columns
    op.add_column('users', sa.Column('wechat_openid', sa.String(64), nullable=True))
    op.add_column('users', sa.Column('wechat_unionid', sa.String(64), nullable=True))
    op.add_column('users', sa.Column('wechat_nickname', sa.String(100), nullable=True))
    op.add_column('users', sa.Column('wechat_avatar', sa.String(256), nullable=True))
    
    # Add extended user info columns
    op.add_column('users', sa.Column('real_name', sa.String(50), nullable=True))
    op.add_column('users', sa.Column('address', sa.String(200), nullable=True))
    op.add_column('users', sa.Column('zip_code', sa.String(10), nullable=True))
    op.add_column('users', sa.Column('avatar_url', sa.String(256), nullable=True))
    op.add_column('users', sa.Column('is_member', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('users', sa.Column('membership_start_date', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('membership_end_date', sa.DateTime(), nullable=True))
    
    # Make password_hash nullable for WeChat users
    op.alter_column('users', 'password_hash', nullable=True)
    
    # Create indexes for WeChat fields
    op.create_index('ix_users_wechat_openid', 'users', ['wechat_openid'], unique=True)
    op.create_index('ix_users_wechat_unionid', 'users', ['wechat_unionid'], unique=True)


def downgrade():
    """Remove WeChat and extended user fields from users table"""
    
    # Drop indexes
    op.drop_index('ix_users_wechat_unionid', table_name='users')
    op.drop_index('ix_users_wechat_openid', table_name='users')
    
    # Remove columns
    op.drop_column('users', 'membership_end_date')
    op.drop_column('users', 'membership_start_date')
    op.drop_column('users', 'is_member')
    op.drop_column('users', 'avatar_url')
    op.drop_column('users', 'zip_code')
    op.drop_column('users', 'address')
    op.drop_column('users', 'real_name')
    op.drop_column('users', 'wechat_avatar')
    op.drop_column('users', 'wechat_nickname')
    op.drop_column('users', 'wechat_unionid')
    op.drop_column('users', 'wechat_openid')
    
    # Make password_hash non-nullable again
    op.alter_column('users', 'password_hash', nullable=False)

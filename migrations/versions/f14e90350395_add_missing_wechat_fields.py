"""add_missing_wechat_fields

Revision ID: f14e90350395
Revises: 2552abfa4870
Create Date: 2025-07-19 14:05:18.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f14e90350395'
down_revision = '2552abfa4870'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('herb_ingredients',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('category', sa.String(length=50), nullable=True),
    sa.Column('properties', sa.String(length=200), nullable=True),
    sa.Column('effects', sa.Text(), nullable=True),
    sa.Column('dosage_range', sa.String(length=50), nullable=True),
    sa.Column('contraindications', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_herb_ingredients_name'), 'herb_ingredients', ['name'], unique=False)
    op.create_table('recipes',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('goods_id', sa.String(length=32), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('preparation_method', sa.Text(), nullable=True),
    sa.Column('usage_instructions', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['goods_id'], ['goods.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_recipes_goods_id'), 'recipes', ['goods_id'], unique=False)
    op.create_index(op.f('ix_recipes_name'), 'recipes', ['name'], unique=False)
    op.create_table('recipe_ingredients',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('recipe_id', sa.String(length=36), nullable=False),
    sa.Column('herb_ingredient_id', sa.String(length=36), nullable=False),
    sa.Column('dosage', sa.String(length=50), nullable=False),
    sa.Column('preparation_notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['herb_ingredient_id'], ['herb_ingredients.id'], ),
    sa.ForeignKeyConstraint(['recipe_id'], ['recipes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_recipe_ingredients_herb_ingredient_id'), 'recipe_ingredients', ['herb_ingredient_id'], unique=False)
    op.create_index(op.f('ix_recipe_ingredients_recipe_id'), 'recipe_ingredients', ['recipe_id'], unique=False)
    op.drop_index('ix_favorites_goods_id', table_name='favorites')
    op.drop_index('ix_favorites_user_id', table_name='favorites')
    op.drop_table('favorites')
    op.alter_column('addresses', 'user_id',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
    op.alter_column('addresses', 'is_default',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('addresses', 'create_time',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('addresses', 'update_time',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.add_column('cart_items', sa.Column('quantity', sa.Integer(), nullable=True))
    op.add_column('cart_items', sa.Column('selected', sa.Boolean(), nullable=True))
    op.add_column('cart_items', sa.Column('product_type', sa.String(length=50), nullable=True))
    op.add_column('cart_items', sa.Column('packaging', sa.String(length=50), nullable=True))
    op.add_column('cart_items', sa.Column('is_vip', sa.Boolean(), nullable=True))
    op.add_column('cart_items', sa.Column('customization_note', sa.Text(), nullable=True))
    op.alter_column('cart_items', 'user_id',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
    op.alter_column('cart_items', 'goods_id',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
    op.alter_column('cart_items', 'name',
               existing_type=sa.VARCHAR(length=128),
               nullable=False)
    op.alter_column('cart_items', 'price',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               nullable=False)
    op.alter_column('cart_items', 'count',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('cart_items', 'checked',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('cart_items', 'create_time',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('cart_items', 'update_time',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('chat_messages', 'is_user',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('chat_messages', 'timestamp',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('complaint_reports', 'id',
               existing_type=sa.VARCHAR(length=32),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.alter_column('complaint_reports', 'user_id',
               existing_type=sa.VARCHAR(length=32),
               type_=sa.String(length=36),
               existing_nullable=False)
    op.alter_column('complaint_reports', 'base_constitution_report_id',
               existing_type=sa.VARCHAR(length=32),
               type_=sa.String(length=36),
               existing_nullable=True)
    op.alter_column('complaint_reports', 'created_at',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('complaint_reports', 'updated_at',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('constitution_reports', 'created_at',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('constitution_reports', 'updated_at',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('conversations', 'title',
               existing_type=sa.VARCHAR(length=200),
               nullable=False)
    op.alter_column('conversations', 'created_at',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('conversations', 'updated_at',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.add_column('goods', sa.Column('intro', sa.Text(), nullable=True))
    op.add_column('goods', sa.Column('effect', sa.Text(), nullable=True))
    op.add_column('goods', sa.Column('tea_type', sa.String(length=50), nullable=True))
    op.add_column('goods', sa.Column('grams', sa.Integer(), nullable=True))
    op.add_column('goods', sa.Column('seasonal_recommend', sa.Boolean(), nullable=True))
    op.add_column('goods', sa.Column('self_select_enabled', sa.Boolean(), nullable=True))
    op.add_column('goods', sa.Column('self_select_categories', sa.JSON(), nullable=True))
    op.alter_column('goods', 'name',
               existing_type=sa.VARCHAR(length=128),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('goods', 'sales',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('goods', 'status',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('goods', 'create_time',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('goods', 'update_time',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.create_index(op.f('ix_goods_name'), 'goods', ['name'], unique=False)
    op.alter_column('goods_images', 'goods_id',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
    op.alter_column('goods_images', 'url',
               existing_type=sa.VARCHAR(length=256),
               type_=sa.String(length=255),
               existing_nullable=False)
    op.alter_column('goods_images', 'sort',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('goods_images', 'create_time')
    op.alter_column('goods_skus', 'goods_id',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
    op.alter_column('goods_skus', 'name',
               existing_type=sa.VARCHAR(length=128),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('goods_skus', 'stock',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('goods_skus', 'update_time')
    op.drop_column('goods_skus', 'create_time')
    op.alter_column('order_goods', 'order_id',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
    op.alter_column('order_goods', 'goods_id',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
    op.alter_column('order_goods', 'create_time',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('orders', 'order_no',
               existing_type=sa.VARCHAR(length=64),
               nullable=False)
    op.alter_column('orders', 'user_id',
               existing_type=sa.VARCHAR(length=32),
               nullable=False)
    op.alter_column('orders', 'status',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('orders', 'create_time',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.add_column('users', sa.Column('wechat_authorized', sa.Boolean(), nullable=True))
    op.add_column('users', sa.Column('wechat_auth_time', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('is_wechat_user', sa.Boolean(), nullable=True))
    op.add_column('users', sa.Column('last_login_at', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('is_active', sa.Boolean(), nullable=True))
    op.alter_column('users', 'created_at',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('users', 'updated_at',
               existing_type=sa.DATETIME(),
               nullable=False)
    op.alter_column('users', 'is_pregnant',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('users', 'is_preparing_pregnancy',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'is_preparing_pregnancy',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('users', 'is_pregnant',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('users', 'updated_at',
               existing_type=sa.DATETIME(),
               nullable=True)
    op.alter_column('users', 'created_at',
               existing_type=sa.DATETIME(),
               nullable=True)
    op.drop_column('users', 'is_active')
    op.drop_column('users', 'last_login_at')
    op.drop_column('users', 'is_wechat_user')
    op.drop_column('users', 'wechat_auth_time')
    op.drop_column('users', 'wechat_authorized')
    # ### end Alembic commands ###

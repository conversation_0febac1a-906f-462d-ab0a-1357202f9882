"""initial migration

Revision ID: f181d77a4acf
Revises: 
Create Date: 2025-04-15 21:10:21.146741

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f181d77a4acf'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('goods',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('original_price', sa.Float(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('image', sa.String(length=256), nullable=True),
    sa.Column('sales', sa.Integer(), nullable=True),
    sa.Column('category_id', sa.String(length=32), nullable=True),
    sa.Column('category_name', sa.String(length=64), nullable=True),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('goods', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_goods_category_id'), ['category_id'], unique=False)

    op.create_table('users',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('username', sa.String(length=64), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('password_hash', sa.String(length=128), nullable=True),
    sa.Column('mobile', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('gender', sa.String(length=10), nullable=True),
    sa.Column('age', sa.Integer(), nullable=True),
    sa.Column('height', sa.Float(), nullable=True),
    sa.Column('weight', sa.Float(), nullable=True),
    sa.Column('is_pregnant', sa.Boolean(), nullable=True),
    sa.Column('is_preparing_pregnancy', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('mobile'),
    sa.UniqueConstraint('username')
    )
    op.create_table('addresses',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('user_id', sa.String(length=32), nullable=True),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('mobile', sa.String(length=20), nullable=False),
    sa.Column('province', sa.String(length=64), nullable=False),
    sa.Column('city', sa.String(length=64), nullable=False),
    sa.Column('district', sa.String(length=64), nullable=False),
    sa.Column('detail', sa.String(length=256), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('addresses', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_addresses_user_id'), ['user_id'], unique=False)

    op.create_table('cart_items',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('user_id', sa.String(length=32), nullable=True),
    sa.Column('goods_id', sa.String(length=32), nullable=True),
    sa.Column('sku_id', sa.String(length=32), nullable=True),
    sa.Column('name', sa.String(length=128), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('count', sa.Integer(), nullable=True),
    sa.Column('image', sa.String(length=256), nullable=True),
    sa.Column('checked', sa.Boolean(), nullable=True),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('cart_items', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_cart_items_goods_id'), ['goods_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_cart_items_user_id'), ['user_id'], unique=False)

    op.create_table('chat_messages',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('user_id', sa.String(length=32), nullable=True),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('is_user', sa.Boolean(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('chat_messages', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_chat_messages_user_id'), ['user_id'], unique=False)

    op.create_table('goods_images',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('goods_id', sa.String(length=32), nullable=True),
    sa.Column('url', sa.String(length=256), nullable=False),
    sa.Column('sort', sa.Integer(), nullable=True),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['goods_id'], ['goods.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('goods_images', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_goods_images_goods_id'), ['goods_id'], unique=False)

    op.create_table('goods_skus',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('goods_id', sa.String(length=32), nullable=True),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('stock', sa.Integer(), nullable=True),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('update_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['goods_id'], ['goods.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('goods_skus', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_goods_skus_goods_id'), ['goods_id'], unique=False)

    op.create_table('orders',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('order_no', sa.String(length=32), nullable=True),
    sa.Column('user_id', sa.String(length=32), nullable=True),
    sa.Column('address_id', sa.String(length=32), nullable=True),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.Column('total_price', sa.Float(), nullable=False),
    sa.Column('pay_price', sa.Float(), nullable=True),
    sa.Column('address_snapshot', sa.Text(), nullable=True),
    sa.Column('remark', sa.String(length=256), nullable=True),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.Column('pay_time', sa.DateTime(), nullable=True),
    sa.Column('ship_time', sa.DateTime(), nullable=True),
    sa.Column('complete_time', sa.DateTime(), nullable=True),
    sa.Column('cancel_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['address_id'], ['addresses.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('order_no')
    )
    with op.batch_alter_table('orders', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_orders_address_id'), ['address_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_orders_user_id'), ['user_id'], unique=False)

    op.create_table('order_goods',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('order_id', sa.String(length=32), nullable=True),
    sa.Column('goods_id', sa.String(length=32), nullable=True),
    sa.Column('sku_id', sa.String(length=32), nullable=True),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('count', sa.Integer(), nullable=False),
    sa.Column('image', sa.String(length=256), nullable=True),
    sa.Column('sku_name', sa.String(length=128), nullable=True),
    sa.Column('create_time', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('order_goods', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_order_goods_goods_id'), ['goods_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_order_goods_order_id'), ['order_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('order_goods', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_order_goods_order_id'))
        batch_op.drop_index(batch_op.f('ix_order_goods_goods_id'))

    op.drop_table('order_goods')
    with op.batch_alter_table('orders', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_orders_user_id'))
        batch_op.drop_index(batch_op.f('ix_orders_address_id'))

    op.drop_table('orders')
    with op.batch_alter_table('goods_skus', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_goods_skus_goods_id'))

    op.drop_table('goods_skus')
    with op.batch_alter_table('goods_images', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_goods_images_goods_id'))

    op.drop_table('goods_images')
    with op.batch_alter_table('chat_messages', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_chat_messages_user_id'))

    op.drop_table('chat_messages')
    with op.batch_alter_table('cart_items', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_cart_items_user_id'))
        batch_op.drop_index(batch_op.f('ix_cart_items_goods_id'))

    op.drop_table('cart_items')
    with op.batch_alter_table('addresses', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_addresses_user_id'))

    op.drop_table('addresses')
    op.drop_table('users')
    with op.batch_alter_table('goods', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_goods_category_id'))

    op.drop_table('goods')
    # ### end Alembic commands ###

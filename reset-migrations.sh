#!/bin/bash
# 重置数据库迁移脚本
# 用于服务器端重置迁移到干净状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

DOCKER_COMPOSE_CMD="docker compose"

log_info "=== Mastea 数据库迁移重置 ==="

# 检查是否在正确的目录
if [ ! -f "docker-compose.yml" ]; then
    log_error "请在包含docker-compose.yml的目录中运行此脚本"
    exit 1
fi

# 1. 备份当前数据库（可选）
backup_database() {
    log_info "备份当前数据库..."
    $DOCKER_COMPOSE_CMD exec postgres pg_dump -U mastea mastea_prod > backup_$(date +%Y%m%d_%H%M%S).sql
    log_success "数据库备份完成"
}

# 2. 停止应用服务（保持数据库运行）
stop_app() {
    log_info "停止应用服务..."
    $DOCKER_COMPOSE_CMD stop fastapi_app
    log_success "应用服务已停止"
}

# 3. 清理数据库
reset_database() {
    log_info "重置数据库..."
    
    # 删除所有表
    $DOCKER_COMPOSE_CMD exec postgres psql -U mastea -d mastea_prod -c "
    DROP SCHEMA public CASCADE;
    CREATE SCHEMA public;
    GRANT ALL ON SCHEMA public TO mastea;
    GRANT ALL ON SCHEMA public TO public;
    "
    
    log_success "数据库已重置"
}

# 4. 清理迁移历史
reset_migrations() {
    log_info "重置迁移历史..."
    
    # 备份旧的migrations目录
    if [ -d "migrations" ]; then
        mv migrations migrations_backup_$(date +%Y%m%d_%H%M%S)
    fi
    
    # 复制新的干净迁移
    cp -r migrations-clean migrations
    
    log_success "迁移文件已重置"
}

# 5. 重新启动应用并运行迁移
restart_and_migrate() {
    log_info "重新启动应用..."
    $DOCKER_COMPOSE_CMD up -d
    
    # 等待服务启动
    sleep 30
    
    log_info "运行数据库迁移..."
    $DOCKER_COMPOSE_CMD exec -T fastapi_app bash -c "cd migrations && alembic upgrade head"
    
    log_success "迁移完成"
}

# 6. 验证结果
verify_migration() {
    log_info "验证迁移结果..."
    
    # 检查关键字段
    WECHAT_FIELDS=$($DOCKER_COMPOSE_CMD exec postgres psql -U mastea -d mastea_prod -t -c "SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name LIKE '%wechat_authorized%';" | tr -d ' ')
    
    if [ "$WECHAT_FIELDS" = "wechat_authorized" ]; then
        log_success "wechat_authorized字段验证通过"
    else
        log_error "wechat_authorized字段验证失败"
        exit 1
    fi
    
    # 检查应用健康状态
    sleep 10
    if curl -f http://localhost:8001/api/v1/health &>/dev/null; then
        log_success "应用健康检查通过"
    else
        log_warning "应用健康检查失败，请检查日志"
    fi
    
    log_success "=== 迁移重置完成！ ==="
}

# 主函数
main() {
    echo "此操作将重置数据库和迁移历史，所有数据将丢失！"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    backup_database
    stop_app
    reset_database
    reset_migrations
    restart_and_migrate
    verify_migration
}

# 执行主函数
main "$@"

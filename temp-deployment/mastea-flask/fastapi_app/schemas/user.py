from pydantic import BaseModel
from typing import Optional, Dict
from datetime import datetime

class UserBase(BaseModel):
    username: str
    email: str
    mobile: str
    gender: Optional[str] = None
    age: Optional[int] = None
    height: Optional[float] = None
    weight: Optional[float] = None
    is_pregnant: Optional[bool] = False
    is_preparing_pregnancy: Optional[bool] = False
    # 新增字段
    real_name: Optional[str] = None
    address: Optional[str] = None
    zip_code: Optional[str] = None
    avatar_url: Optional[str] = None
    is_member: Optional[bool] = False

class User(UserBase):
    id: str

    class Config:
        from_attributes = True

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    gender: Optional[str] = None
    age: Optional[int] = None
    height: Optional[float] = None
    weight: Optional[float] = None
    is_pregnant: Optional[bool] = None
    is_preparing_pregnancy: Optional[bool] = None
    # 新增字段
    real_name: Optional[str] = None
    address: Optional[str] = None
    zip_code: Optional[str] = None
    avatar_url: Optional[str] = None
    is_member: Optional[bool] = None
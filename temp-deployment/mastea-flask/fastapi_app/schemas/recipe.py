from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

# 草药成分相关Schema
class HerbIngredientBase(BaseModel):
    name: str
    description: Optional[str] = None
    intro: Optional[str] = None
    image: Optional[str] = None
    flavor: Optional[str] = None
    nature: Optional[str] = None
    is_active: Optional[bool] = True

class HerbIngredientCreate(HerbIngredientBase):
    pass

class HerbIngredientUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    intro: Optional[str] = None
    image: Optional[str] = None
    flavor: Optional[str] = None
    nature: Optional[str] = None
    is_active: Optional[bool] = None

class HerbIngredientResponse(HerbIngredientBase):
    id: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    class Config:
        from_attributes = True

# 配方成分关联Schema
class RecipeIngredientBase(BaseModel):
    recipe_id: str
    herb_ingredient_id: str
    amount: Optional[float] = None
    unit: Optional[str] = None

class RecipeIngredientCreate(RecipeIngredientBase):
    pass

class RecipeIngredientUpdate(BaseModel):
    amount: Optional[float] = None
    unit: Optional[str] = None

class RecipeIngredientResponse(RecipeIngredientBase):
    id: str
    herb_ingredient: Optional[HerbIngredientResponse] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    class Config:
        from_attributes = True

# 配方相关Schema
class RecipeBase(BaseModel):
    name: str
    description: Optional[str] = None
    intro: Optional[str] = None
    image: Optional[str] = None
    grams: Optional[int] = None
    goods_id: Optional[str] = None
    is_active: Optional[bool] = True

class RecipeCreate(RecipeBase):
    pass

class RecipeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    intro: Optional[str] = None
    image: Optional[str] = None
    grams: Optional[int] = None
    goods_id: Optional[str] = None
    is_active: Optional[bool] = None

class RecipeResponse(RecipeBase):
    id: str
    ingredients: List[RecipeIngredientResponse] = []
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    class Config:
        from_attributes = True

# 茶品相关Schema（扩展商品Schema）
class TeaProductBase(BaseModel):
    name: str
    description: Optional[str] = None
    price: float
    image: Optional[str] = None
    category_name: Optional[str] = None
    intro: Optional[str] = None
    effect: Optional[str] = None
    tea_type: Optional[str] = None  # basicRecommend, customization
    grams: Optional[int] = None
    seasonal_recommend: Optional[bool] = False
    self_select_enabled: Optional[bool] = False
    self_select_categories: Optional[str] = None  # JSON格式的分类列表

class TeaProductCreate(TeaProductBase):
    pass

class TeaProductUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    image: Optional[str] = None
    category_name: Optional[str] = None
    intro: Optional[str] = None
    effect: Optional[str] = None
    tea_type: Optional[str] = None
    grams: Optional[int] = None
    seasonal_recommend: Optional[bool] = None
    self_select_enabled: Optional[bool] = None
    self_select_categories: Optional[str] = None

class TeaProductResponse(TeaProductBase):
    id: str
    sales: int = 0
    status: int = 0
    recipes: List[RecipeResponse] = []
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    class Config:
        from_attributes = True

# 茶品推荐相关Schema
class BasicRecommendTea(BaseModel):
    id: str
    name: str
    image: str
    intro: str
    price: float
    effect: str
    recipes: List[RecipeResponse] = []

class CustomizationTea(BaseModel):
    id: str
    name: str
    price: float
    recipes: List[RecipeResponse] = []

class RecommendTeaData(BaseModel):
    BCReportId: int
    userName: str
    date: str
    basicRecommend: List[BasicRecommendTea] = []
    customization: Optional[CustomizationTea] = None

# 茶品列表查询参数
class TeaListQuery(BaseModel):
    category: Optional[str] = None
    tea_type: Optional[str] = None
    seasonal_recommend: Optional[bool] = None
    self_select_category: Optional[str] = None
    skip: int = 0
    limit: int = 20

# 自选茶包相关Schema
class SelfSelectTea(BaseModel):
    id: str
    name: str
    image: str
    intro: str
    price: float
    effect: Optional[str] = None
    type: str = "basicRecommend"
    category: Optional[str] = None
    seasonalRecommend: bool = False
    selfSelect: dict = {
        "isSelected": True,
        "subCategories": []
    }

    @classmethod
    def from_goods(cls, goods):
        """从Goods对象创建SelfSelectTea"""
        import json
        
        # 解析自选分类
        sub_categories = []
        if goods.self_select_categories:
            try:
                sub_categories = json.loads(goods.self_select_categories)
            except:
                sub_categories = []
        
        return cls(
            id=goods.id,
            name=goods.name,
            image=goods.image or "",
            intro=goods.intro or "",
            price=goods.price,
            effect=goods.effect,
            type=goods.tea_type or "basicRecommend",
            category=goods.category_name,
            seasonalRecommend=goods.seasonal_recommend,
            selfSelect={
                "isSelected": goods.self_select_enabled,
                "subCategories": sub_categories
            }
        )

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class ChatMessageBase(BaseModel):
    message: str

class ChatMessageCreate(ChatMessageBase):
    conversation_id: Optional[str] = None
    task_type: Optional[str] = None
    user_id: Optional[str] = None

class ChatMessage(ChatMessageBase):
    id: str
    conversation_id: str
    user_id: Optional[str] = None
    is_user: bool
    timestamp: int

    class Config:
        from_attributes = True

    def model_post_init(self, __context):
        # 确保timestamp是整数时间戳
        if hasattr(self, 'timestamp') and self.timestamp is not None:
            if isinstance(self.timestamp, datetime):
                self.timestamp = int(self.timestamp.timestamp() * 1000)
            elif not isinstance(self.timestamp, int):
                # 尝试转换为整数
                try:
                    self.timestamp = int(self.timestamp)
                except (ValueError, TypeError):
                    self.timestamp = int(datetime.now().timestamp() * 1000) 
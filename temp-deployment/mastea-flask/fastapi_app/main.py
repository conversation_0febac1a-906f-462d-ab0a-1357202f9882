from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import RedirectResponse, FileResponse
from fastapi_app.api.v1.endpoints import auth, user, goods, cart, address, order, chat, report, admin, health, tea, wechat
from fastapi_app.api.v1.endpoints import cart_enhanced, report_enhanced, product_enhanced
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import os
from fastapi_app.core.logging import setup_logging
# 导入所有模型以确保SQLAlchemy正确初始化关系
import fastapi_app.models

# 设置日志
logger = setup_logging()

# 创建应用并添加元数据
app = FastAPI(
    title="Mastea API",
    description="麦斯特养生茶后端API服务",
    version="1.0.0",
    docs_url="/api/v1/docs",  # 修改文档URL到/api/v1路径下
    redoc_url="/api/v1/redoc",
    openapi_url="/api/v1/openapi.json"
)

@app.on_event("startup")
async def startup_event():
    logger.info("应用程序正在启动...")
    # 可以在这里添加其他启动初始化代码

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("应用程序正在关闭...")
    # 可以在这里添加清理代码

# 注册所有API路由
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证管理"])
app.include_router(user.router, prefix="/api/v1/users", tags=["用户管理"])
app.include_router(goods.router, prefix="/api/v1/goods", tags=["商品管理"])
app.include_router(cart.router, prefix="/api/v1/cart", tags=["购物车管理"])
app.include_router(address.router, prefix="/api/v1/addresses", tags=["地址管理"])
app.include_router(order.router, prefix="/api/v1/orders", tags=["订单管理"])
app.include_router(chat.router, prefix="/api/v1/chat", tags=["AI聊天"])
app.include_router(report.router, prefix="/api/v1/reports", tags=["体质/主诉报告"])
app.include_router(tea.router, prefix="/api/v1/tea", tags=["茶品管理"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["管理后台"])
app.include_router(health.router, prefix="/api/v1", tags=["系统健康检查"])
app.include_router(wechat.router, prefix="/api/v1/wechat", tags=["微信小程序"])

# 为了兼容k_api，也挂载在根路径下
app.include_router(wechat.router, prefix="/wechat", tags=["微信小程序(兼容)"])
app.include_router(cart_enhanced.router, prefix="/cart", tags=["购物车(兼容k_api)"])
app.include_router(report_enhanced.router, prefix="/report", tags=["体质报告(兼容k_api)"])
app.include_router(product_enhanced.router, prefix="/product", tags=["茶品产品(兼容k_api)"])
app.include_router(report.router, prefix="/api/reports", tags=["体质/主诉报告(兼容)"])
app.include_router(chat.router, prefix="/api/chat", tags=["AI聊天(兼容)"])

# 设置模板
templates = Jinja2Templates(directory="fastapi_app/templates")

# 挂载静态文件
static_dir = os.path.join(os.getcwd(), "fastapi_app", "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")
else:
    print(f"Warning: Static directory not found at {static_dir}")

# 如果存在uploads文件夹，则挂载
if os.path.exists("uploads"):
    app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# 添加文档重定向路由
@app.get("/docs")
@app.get("/docs/")
async def redirect_to_docs():
    return RedirectResponse(url="/api/v1/docs")

@app.get("/redoc")
@app.get("/redoc/")
async def redirect_to_redoc():
    return RedirectResponse(url="/api/v1/redoc")

# 根路径
@app.get("/")
def read_root():
    return {"message": "欢迎使用麦斯特养生茶API服务"}

# HTML页面路由
@app.get("/admin", include_in_schema=False)
async def admin_index(request: Request):
    """管理系统首页"""
    return templates.TemplateResponse("admin/index.html", {"request": request})

@app.get("/admin/login", include_in_schema=False)
async def admin_login(request: Request):
    """管理系统登录页"""
    return templates.TemplateResponse("admin/login.html", {"request": request})

@app.get("/chat", include_in_schema=False)
async def chat_page(request: Request):
    """聊天页面"""
    return templates.TemplateResponse("chat.html", {"request": request})
    
@app.get("/api_test", include_in_schema=False)
async def api_test_page(request: Request):
    """API测试页面"""
    return templates.TemplateResponse("api_test.html", {"request": request})

@app.get("/assessment", include_in_schema=False)
async def assessment_page(request: Request):
    """体质评估页面"""
    return templates.TemplateResponse("report_assessment.html", {"request": request})

@app.get("/test", include_in_schema=False)
async def comprehensive_test_page(request: Request):
    """综合功能测试页面"""
    return templates.TemplateResponse("comprehensive_test.html", {"request": request})

@app.get("/dashboard", include_in_schema=False)
async def user_dashboard_page(request: Request):
    """用户友好的测试面板"""
    return templates.TemplateResponse("user_dashboard.html", {"request": request})

@app.get("/dashboard_enhanced", include_in_schema=False)
async def enhanced_dashboard_page(request: Request):
    """增强版智能健康管理平台 - 集成所有功能"""
    return templates.TemplateResponse("dashboard_enhanced.html", {"request": request})

@app.get("/goods", include_in_schema=False)
async def goods_page(request: Request):
    """商品浏览页面"""
    return templates.TemplateResponse("goods.html", {"request": request})

@app.get("/report/complaint/{report_id}", include_in_schema=False)
async def complaint_report_page(request: Request, report_id: str):
    """综合主诉报告详情页 - 直接在评估页面中展示"""
    return templates.TemplateResponse("report_assessment.html", {
        "request": request, 
        "report_id_to_load": report_id, 
        "report_type_to_load": "complaint"
    })

# 解决部分浏览器对于 /favicon.ico 的请求
@app.get('/favicon.ico', include_in_schema=False)
async def favicon():
    return FileResponse("fastapi_app/static/favicon.ico") 
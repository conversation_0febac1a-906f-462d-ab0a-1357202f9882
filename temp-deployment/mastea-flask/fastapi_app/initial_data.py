import logging
from fastapi_app.core.database import engine, SessionLocal, Base
from fastapi_app.models import user, goods, cart, address, order, conversation, chat, report

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_db() -> None:
    logger.info("Creating all tables in database...")
    # In a production environment, you might want to use Alembic for migrations
    Base.metadata.create_all(bind=engine)
    logger.info("Tables created successfully.")

if __name__ == "__main__":
    init_db() 
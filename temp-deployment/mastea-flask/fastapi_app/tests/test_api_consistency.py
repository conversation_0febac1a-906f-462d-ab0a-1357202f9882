import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from fastapi_app.main import app
from fastapi_app.core.database import get_db
from fastapi_app.models.user import User
from fastapi_app.models.goods import Goods
from fastapi_app.models.cart import CartItem
from fastapi_app.models.report import ConstitutionReport
from fastapi_app.core.security import create_access_token
import uuid
import json

client = TestClient(app)

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class TestAPIConsistency:
    """API接口一致性测试 - 确保所有接口都按照前端示例格式返回"""
    
    @pytest.fixture
    def db_session(self):
        """获取数据库会话"""
        db = next(get_db())
        yield db
        db.close()
    
    @pytest.fixture
    def test_user(self, db_session: Session):
        """创建测试用户"""
        unique_id = generate_uuid()[:8]
        user = User(
            id=generate_uuid(),
            username=f"test_user_{unique_id}",
            email=f"test_{unique_id}@example.com",
            real_name="测试用户",
            mobile=f"138{unique_id[:8]}",  # 使用唯一ID生成手机号
            address="上海市浦东新区张杨路500号",
            zip_code="200120",
            avatar_url="https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/user-avatar-default.png",
            is_member=True,
            password_hash="hashed_password"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user
    
    @pytest.fixture
    def auth_headers(self, test_user: User):
        """获取认证头"""
        token = create_access_token(data={"sub": str(test_user.id)})
        return {"Authorization": f"Bearer {token}"}
    
    @pytest.fixture
    def sample_cart_item(self, db_session: Session, test_user: User):
        """创建示例购物车项目"""
        cart_item = CartItem(
            id=generate_uuid(),
            user_id=test_user.id,
            goods_id=generate_uuid(),
            name="莲子心淡竹叶茶",
            price=76.0,
            count=2,
            image="https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-10000000.png",
            packaging="10包/袋",
            is_vip=False
        )
        db_session.add(cart_item)
        db_session.commit()
        db_session.refresh(cart_item)
        return cart_item
    
    @pytest.fixture
    def sample_constitution_report(self, db_session: Session, test_user: User):
        """创建示例体质报告"""
        report_data = {
            "score": 76,
            "overall": {
                "text": "您的体质在30-35岁的男性中，优于60%的人。",
                "BCType": "阳虚质",
                "mainSymptoms": "失眠多梦且入睡困难伴有焦虑和抑郁"
            },
            "analysis": {
                "BCIndex": {
                    "qiXu": 25,
                    "yangXu": 80,
                    "yinXu": 75,
                    "tanShi": 80,
                    "shiRe": 90,
                    "xueYu": 40,
                    "qiYu": 78,
                    "teBing": 50
                },
                "mainBC": "阳虚质",
                "secondaryBC": ["气虚质", "阴虚质", "痰湿质"]
            },
            "definition": {
                "definition": "阳虚内蕴，以畏寒怕冷、手足不温等阳虚表现为主要特征。",
                "bodyShape": "形体白胖，肌肉松软。",
                "commonSymptoms": "畏寒怕冷，手足不温，喜热饮食，精神不振。",
                "psychology": "性格多沉静、内向。",
                "diseases": "易患痰饮、肿胀、泄泻等病。",
                "environment": "不耐寒邪，耐夏不耐冬。"
            },
            "dailyTips": {
                "term": "立夏",
                "intro": "根据您的体质特点，以下是为您定制的健康建议：",
                "food": "宜食温热食物，忌生冷。",
                "sleep": "保持充足睡眠，避免熬夜。",
                "exercise": "适量运动，避免大汗淋漓。",
                "mood": "保持心情愉悦，避免过度思虑。"
            }
        }
        
        report = ConstitutionReport(
            id=generate_uuid(),
            user_id=test_user.id,
            report_data_json=json.dumps(report_data),
            title="体质评估报告"
        )
        db_session.add(report)
        db_session.commit()
        db_session.refresh(report)
        return report
    
    def test_user_info_format(self, test_user, auth_headers, sample_cart_item):
        """测试用户信息接口格式"""
        response = client.get("/api/v1/users/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # 检查响应结构
        assert "code" in data
        assert "message" in data
        assert "data" in data
        assert data["code"] == 0
        
        user_data = data["data"]
        
        # 检查用户信息字段
        required_fields = [
            "id", "username", "realName", "phone", "address", 
            "zipCode", "avatarUrl", "isMember", "cart"
        ]
        for field in required_fields:
            assert field in user_data, f"Missing required field: {field}"
        
        # 检查购物车格式
        assert isinstance(user_data["cart"], list)
        if user_data["cart"]:
            cart_item = user_data["cart"][0]
            cart_required_fields = ["id", "name", "price", "image", "quantity", "packaging", "isVip"]
            for field in cart_required_fields:
                assert field in cart_item, f"Missing cart field: {field}"
    
    def test_tea_products_format(self):
        """测试茶品列表接口格式"""
        response = client.get("/api/v1/tea/products")
        
        assert response.status_code == 200
        data = response.json()
        
        # 检查响应结构
        assert "code" in data
        assert "message" in data
        assert "data" in data
        assert data["code"] == 0
        
        tea_products = data["data"]
        assert isinstance(tea_products, list)
        
        if tea_products:
            tea_product = tea_products[0]
            required_fields = [
                "id", "name", "image", "intro", "price", "effect", 
                "type", "category", "seasonalRecommend", "selfSelect"
            ]
            for field in required_fields:
                assert field in tea_product, f"Missing tea product field: {field}"
            
            # 检查selfSelect结构
            assert "isSelected" in tea_product["selfSelect"]
            assert "subCategories" in tea_product["selfSelect"]
            assert isinstance(tea_product["selfSelect"]["subCategories"], list)
    
    def test_tea_recommend_format(self, test_user, auth_headers):
        """测试茶品推荐接口格式"""
        report_id = 10000000
        response = client.get(f"/api/v1/tea/recommend/{report_id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # 检查响应结构
        assert "code" in data
        assert "message" in data
        assert "data" in data
        assert data["code"] == 0
        
        recommend_data = data["data"]
        required_fields = ["BCReportId", "userName", "date", "basicRecommend"]
        for field in required_fields:
            assert field in recommend_data, f"Missing recommend field: {field}"
        
        # 检查基础推荐格式
        assert isinstance(recommend_data["basicRecommend"], list)
        if recommend_data["basicRecommend"]:
            basic_tea = recommend_data["basicRecommend"][0]
            basic_required_fields = ["id", "name", "image", "intro", "price", "effect", "recipes"]
            for field in basic_required_fields:
                assert field in basic_tea, f"Missing basic tea field: {field}"
            
            # 检查配方格式
            assert isinstance(basic_tea["recipes"], list)
            if basic_tea["recipes"]:
                recipe = basic_tea["recipes"][0]
                recipe_required_fields = ["id", "name", "image", "intro", "grams"]
                for field in recipe_required_fields:
                    assert field in recipe, f"Missing recipe field: {field}"
    
    def test_constitution_report_format(self, test_user, sample_constitution_report):
        """测试体质报告接口格式"""
        response = client.get(f"/api/v1/reports/constitution/{sample_constitution_report.id}")
        
        assert response.status_code == 200
        data = response.json()
        
        # 检查响应结构
        assert "code" in data
        assert "message" in data
        assert "data" in data
        assert data["code"] == 0
        
        report_data = data["data"]
        required_fields = ["id", "userName", "date", "score", "overall", "analysis", "definition", "dailyTips"]
        for field in required_fields:
            assert field in report_data, f"Missing report field: {field}"
        
        # 检查overall结构
        overall_fields = ["text", "BCType", "mainSymptoms"]
        for field in overall_fields:
            assert field in report_data["overall"], f"Missing overall field: {field}"
        
        # 检查analysis结构
        assert "BCIndex" in report_data["analysis"]
        assert "mainBC" in report_data["analysis"]
        assert "secondaryBC" in report_data["analysis"]
        
        # 检查BCIndex结构
        bc_index_fields = ["qiXu", "yangXu", "yinXu", "tanShi", "shiRe", "xueYu", "qiYu", "teBing"]
        for field in bc_index_fields:
            assert field in report_data["analysis"]["BCIndex"], f"Missing BCIndex field: {field}"
    
    def test_cart_format(self, test_user, auth_headers, sample_cart_item):
        """测试购物车接口格式"""
        response = client.get("/api/v1/cart/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        
        # 检查响应结构
        assert "code" in data
        assert "message" in data
        assert "data" in data
        assert data["code"] == 0
        
        cart_data = data["data"]
        assert isinstance(cart_data, list)
        
        if cart_data:
            cart_item = cart_data[0]
            required_fields = ["id", "name", "price", "image", "quantity", "packaging", "isVip"]
            for field in required_fields:
                assert field in cart_item, f"Missing cart item field: {field}"
            
            # 检查数据类型
            assert isinstance(cart_item["id"], int)
            assert isinstance(cart_item["price"], int)
            assert isinstance(cart_item["quantity"], int)
            assert isinstance(cart_item["isVip"], bool)

if __name__ == "__main__":
    pytest.main([__file__])

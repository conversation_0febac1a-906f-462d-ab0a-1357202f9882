import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from fastapi_app.main import app
from fastapi_app.core.database import get_db
from fastapi_app.models.user import User
from fastapi_app.core.security import create_access_token
import uuid
import json

client = TestClient(app)

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class TestCompleteFunctionality:
    """完整功能测试 - 验证整个系统的端到端功能"""
    
    @pytest.fixture
    def db_session(self):
        """获取数据库会话"""
        db = next(get_db())
        yield db
        db.close()
    
    @pytest.fixture
    def test_user(self, db_session: Session):
        """创建测试用户"""
        unique_id = generate_uuid()[:8]
        user = User(
            id=generate_uuid(),
            username=f"complete_test_user_{unique_id}",
            email=f"complete_{unique_id}@example.com",
            real_name="完整测试用户",
            mobile=f"139{unique_id[:8]}",
            password_hash="hashed_password",
            is_member=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user
    
    @pytest.fixture
    def auth_headers(self, test_user: User):
        """获取认证头"""
        token = create_access_token(data={"sub": str(test_user.id)})
        return {"Authorization": f"Bearer {token}"}
    
    def test_complete_user_workflow(self, test_user, auth_headers):
        """测试完整的用户工作流程"""
        # 1. 获取用户信息
        response = client.get("/api/v1/users/me", headers=auth_headers)
        assert response.status_code == 200
        user_data = response.json()
        assert user_data["code"] == 0
        assert "data" in user_data
        assert user_data["data"]["username"] == test_user.username
        assert user_data["data"]["isMember"] == True
        assert "cart" in user_data["data"]
        
        print(f"✅ 用户信息获取成功: {user_data['data']['username']}")
    
    def test_complete_tea_workflow(self):
        """测试完整的茶品工作流程"""
        # 1. 获取茶品列表
        response = client.get("/api/v1/tea/products")
        assert response.status_code == 200
        tea_data = response.json()
        assert tea_data["code"] == 0
        assert "data" in tea_data
        assert isinstance(tea_data["data"], list)
        
        if tea_data["data"]:
            tea_product = tea_data["data"][0]
            tea_id = tea_product["id"]
            
            print(f"✅ 茶品列表获取成功，共 {len(tea_data['data'])} 个茶品")
            
            # 2. 获取茶品详情
            response = client.get(f"/api/v1/tea/products/{tea_id}")
            assert response.status_code == 200
            detail_data = response.json()
            assert detail_data["code"] == 0
            assert detail_data["data"]["id"] == tea_id
            
            print(f"✅ 茶品详情获取成功: {detail_data['data']['name']}")
        
        # 3. 获取时令推荐
        response = client.get("/api/v1/tea/seasonal-recommend")
        assert response.status_code == 200
        seasonal_data = response.json()
        assert seasonal_data["code"] == 0
        assert isinstance(seasonal_data["data"], list)
        
        print(f"✅ 时令推荐获取成功，共 {len(seasonal_data['data'])} 个推荐")
        
        # 4. 获取自选茶包
        response = client.get("/api/v1/tea/self-select")
        assert response.status_code == 200
        self_select_data = response.json()
        assert self_select_data["code"] == 0
        assert isinstance(self_select_data["data"], list)
        
        print(f"✅ 自选茶包获取成功，共 {len(self_select_data['data'])} 个茶包")
        
        # 5. 按分类获取自选茶包
        response = client.get("/api/v1/tea/self-select?category=助眠")
        assert response.status_code == 200
        category_data = response.json()
        assert category_data["code"] == 0
        
        print(f"✅ 助眠分类茶包获取成功，共 {len(category_data['data'])} 个茶包")
    
    def test_complete_tea_recommendation_workflow(self, test_user, auth_headers):
        """测试完整的茶品推荐工作流程"""
        # 1. 获取茶品推荐
        report_id = 10000000
        response = client.get(f"/api/v1/tea/recommend/{report_id}", headers=auth_headers)
        assert response.status_code == 200
        recommend_data = response.json()
        assert recommend_data["code"] == 0
        assert "data" in recommend_data
        
        data = recommend_data["data"]
        assert data["BCReportId"] == report_id
        assert data["userName"] == test_user.username
        assert "basicRecommend" in data
        assert isinstance(data["basicRecommend"], list)
        
        print(f"✅ 茶品推荐获取成功，用户: {data['userName']}")
        print(f"   基础推荐: {len(data['basicRecommend'])} 个茶品")
        
        if data["basicRecommend"]:
            basic_tea = data["basicRecommend"][0]
            assert "id" in basic_tea
            assert "name" in basic_tea
            assert "recipes" in basic_tea
            print(f"   推荐茶品示例: {basic_tea['name']}")
        
        if data.get("customization"):
            print(f"   定制茶品: {data['customization']['name']}")
    
    def test_complete_constitution_report_workflow(self):
        """测试完整的体质报告工作流程"""
        # 1. 获取体质报告列表
        response = client.get("/api/v1/reports/constitution-list")
        assert response.status_code == 200
        reports_data = response.json()
        assert reports_data["code"] == 0
        assert "data" in reports_data
        assert isinstance(reports_data["data"], list)
        
        print(f"✅ 体质报告列表获取成功，共 {len(reports_data['data'])} 个报告")
        
        if reports_data["data"]:
            report = reports_data["data"][0]
            report_id = report["id"]
            
            print(f"   报告示例: {report['userName']} - {report['overall']['BCType']}")
            
            # 2. 获取体质报告详情
            response = client.get(f"/api/v1/reports/constitution/{report_id}")
            assert response.status_code == 200
            detail_data = response.json()
            assert detail_data["code"] == 0
            
            detail = detail_data["data"]
            assert detail["id"] == report_id
            assert "overall" in detail
            assert "analysis" in detail
            assert "definition" in detail
            assert "dailyTips" in detail
            
            print(f"✅ 体质报告详情获取成功: {detail['overall']['BCType']}")
            print(f"   评分: {detail['score']}")
            print(f"   主要症状: {detail['overall']['mainSymptoms']}")
    
    def test_complete_cart_workflow(self, test_user, auth_headers):
        """测试完整的购物车工作流程"""
        # 1. 获取购物车
        response = client.get("/api/v1/cart/", headers=auth_headers)
        assert response.status_code == 200
        cart_data = response.json()
        assert cart_data["code"] == 0
        assert "data" in cart_data
        assert isinstance(cart_data["data"], list)
        
        print(f"✅ 购物车获取成功，共 {len(cart_data['data'])} 个商品")
        
        if cart_data["data"]:
            cart_item = cart_data["data"][0]
            assert "id" in cart_item
            assert "name" in cart_item
            assert "price" in cart_item
            assert "quantity" in cart_item
            assert "packaging" in cart_item
            assert "isVip" in cart_item
            
            print(f"   购物车商品示例: {cart_item['name']} x {cart_item['quantity']}")
    
    def test_complete_herb_workflow(self):
        """测试完整的草药工作流程"""
        # 1. 获取草药列表
        response = client.get("/api/v1/tea/herbs")
        assert response.status_code == 200
        herbs_data = response.json()
        assert herbs_data["code"] == 0
        assert "data" in herbs_data
        assert isinstance(herbs_data["data"], list)
        
        print(f"✅ 草药列表获取成功，共 {len(herbs_data['data'])} 个草药")
        
        if herbs_data["data"]:
            herb = herbs_data["data"][0]
            assert "id" in herb
            assert "name" in herb
            assert "intro" in herb
            
            print(f"   草药示例: {herb['name']}")
        
        # 2. 按名称搜索草药
        response = client.get("/api/v1/tea/herbs?name=莲子")
        assert response.status_code == 200
        search_data = response.json()
        assert search_data["code"] == 0
        
        print(f"✅ 草药搜索成功，找到 {len(search_data['data'])} 个相关草药")
    
    def test_api_response_consistency(self):
        """测试API响应格式一致性"""
        # 所有API都应该返回统一的格式: {code, message, data}
        endpoints = [
            "/api/v1/tea/products",
            "/api/v1/tea/seasonal-recommend", 
            "/api/v1/tea/self-select",
            "/api/v1/tea/herbs",
            "/api/v1/reports/constitution-list"
        ]
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            assert response.status_code == 200
            data = response.json()
            
            # 检查统一的响应格式
            assert "code" in data, f"Missing 'code' in {endpoint}"
            assert "message" in data, f"Missing 'message' in {endpoint}"
            assert "data" in data, f"Missing 'data' in {endpoint}"
            assert data["code"] == 0, f"Non-zero code in {endpoint}"
            
        print("✅ 所有API响应格式一致性检查通过")
    
    def test_data_integrity(self):
        """测试数据完整性"""
        # 1. 检查茶品数据完整性
        response = client.get("/api/v1/tea/products")
        tea_data = response.json()["data"]
        
        for tea in tea_data:
            required_fields = ["id", "name", "price", "type"]
            for field in required_fields:
                assert field in tea, f"Missing field {field} in tea product"
            
            assert isinstance(tea["price"], int), "Price should be integer"
            assert tea["type"] in ["basicRecommend", "customization"], "Invalid tea type"
        
        # 2. 检查体质报告数据完整性
        response = client.get("/api/v1/reports/constitution-list")
        reports_data = response.json()["data"]
        
        for report in reports_data:
            required_fields = ["id", "userName", "date", "score", "overall"]
            for field in required_fields:
                assert field in report, f"Missing field {field} in constitution report"
            
            assert isinstance(report["score"], int), "Score should be integer"
            assert "BCType" in report["overall"], "Missing BCType in overall"
        
        print("✅ 数据完整性检查通过")

if __name__ == "__main__":
    pytest.main([__file__])

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from fastapi_app.main import app
from fastapi_app.core.database import get_db
from fastapi_app.models.goods import Goods
from fastapi_app.models.recipe import Recipe, HerbIngredient
from fastapi_app.models.user import User
from fastapi_app.core.security import create_access_token
import uuid
import json

client = TestClient(app)

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class TestTeaAPI:
    """茶品API测试"""
    
    @pytest.fixture
    def db_session(self):
        """获取数据库会话"""
        db = next(get_db())
        yield db
        db.close()
    
    @pytest.fixture
    def test_user(self, db_session: Session):
        """创建测试用户"""
        unique_id = generate_uuid()[:8]
        user = User(
            id=generate_uuid(),
            username=f"test_tea_user_{unique_id}",
            email=f"tea_{unique_id}@test.com",
            password_hash="hashed_password"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user
    
    @pytest.fixture
    def auth_headers(self, test_user: User):
        """获取认证头"""
        token = create_access_token(data={"sub": str(test_user.id)})
        return {"Authorization": f"Bearer {token}"}
    
    @pytest.fixture
    def sample_tea_product(self, db_session: Session):
        """创建示例茶品"""
        tea = Goods(
            id=generate_uuid(),
            name="测试茶品",
            description="测试茶品描述",
            intro="测试茶品介绍",
            price=88.0,
            image="https://example.com/tea.jpg",
            effect="测试功效",
            tea_type="basicRecommend",
            category_name="测试分类",
            seasonal_recommend=True,
            self_select_enabled=True,
            self_select_categories=json.dumps(["全部", "助眠"]),
            status=0,
            sales=0
        )
        db_session.add(tea)
        db_session.commit()
        db_session.refresh(tea)
        return tea
    
    @pytest.fixture
    def sample_herb(self, db_session: Session):
        """创建示例草药"""
        herb = HerbIngredient(
            id=generate_uuid(),
            name="测试草药",
            description="测试草药描述",
            intro="测试草药介绍",
            image="https://example.com/herb.jpg",
            flavor="甘",
            nature="平",
            is_active=True
        )
        db_session.add(herb)
        db_session.commit()
        db_session.refresh(herb)
        return herb
    
    def test_get_tea_products(self, sample_tea_product):
        """测试获取茶品列表"""
        response = client.get("/api/v1/tea/products")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # 检查是否包含我们创建的茶品
        tea_names = [tea["name"] for tea in data]
        assert "测试茶品" in tea_names
    
    def test_get_tea_products_with_filters(self, sample_tea_product):
        """测试带筛选条件的茶品列表"""
        # 按茶品类型筛选
        response = client.get("/api/v1/tea/products?tea_type=basicRecommend")
        assert response.status_code == 200
        data = response.json()
        for tea in data:
            assert tea["tea_type"] == "basicRecommend"
        
        # 按时令推荐筛选
        response = client.get("/api/v1/tea/products?seasonal_recommend=true")
        assert response.status_code == 200
        data = response.json()
        for tea in data:
            assert tea["seasonal_recommend"] == True
    
    def test_get_tea_product_detail(self, sample_tea_product):
        """测试获取茶品详情"""
        response = client.get(f"/api/v1/tea/products/{sample_tea_product.id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == sample_tea_product.id
        assert data["name"] == "测试茶品"
        assert data["price"] == 88.0
        assert data["tea_type"] == "basicRecommend"
        assert data["seasonal_recommend"] == True
        assert data["self_select_enabled"] == True
        assert "recipes" in data
    
    def test_get_tea_product_detail_not_found(self):
        """测试获取不存在的茶品详情"""
        fake_id = generate_uuid()
        response = client.get(f"/api/v1/tea/products/{fake_id}")
        
        assert response.status_code == 404
        assert "茶品不存在" in response.json()["detail"]
    
    def test_get_seasonal_recommended_teas(self, sample_tea_product):
        """测试获取时令推荐茶品"""
        response = client.get("/api/v1/tea/seasonal-recommend")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # 检查返回的茶品是否都是时令推荐
        for tea in data:
            assert tea["seasonalRecommend"] == True
    
    def test_get_self_select_teas(self, sample_tea_product):
        """测试获取自选茶包"""
        response = client.get("/api/v1/tea/self-select")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # 检查返回的茶品是否都支持自选
        for tea in data:
            assert tea["selfSelect"]["isSelected"] == True
    
    def test_get_self_select_teas_with_category(self, sample_tea_product):
        """测试按分类获取自选茶包"""
        response = client.get("/api/v1/tea/self-select?category=助眠")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # 检查返回的茶品是否包含指定分类
        for tea in data:
            assert "助眠" in tea["selfSelect"]["subCategories"]
    
    def test_get_recommended_teas_by_report(self, test_user, auth_headers):
        """测试根据体质报告获取推荐茶品"""
        report_id = 10000000
        response = client.get(
            f"/api/v1/tea/recommend/{report_id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["BCReportId"] == report_id
        assert data["userName"] == test_user.username
        assert "basicRecommend" in data
        assert isinstance(data["basicRecommend"], list)
        
        # 检查基础推荐茶品的结构
        if data["basicRecommend"]:
            basic_tea = data["basicRecommend"][0]
            assert "id" in basic_tea
            assert "name" in basic_tea
            assert "price" in basic_tea
            assert "effect" in basic_tea
            assert "recipes" in basic_tea
    
    def test_get_recommended_teas_unauthorized(self):
        """测试未授权访问推荐茶品"""
        report_id = 10000000
        response = client.get(f"/api/v1/tea/recommend/{report_id}")
        
        assert response.status_code == 401
    
    def test_get_herb_ingredients(self, sample_herb):
        """测试获取草药成分列表"""
        response = client.get("/api/v1/tea/herbs")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # 检查是否包含我们创建的草药
        herb_names = [herb["name"] for herb in data]
        assert "测试草药" in herb_names
    
    def test_get_herb_ingredients_with_name_filter(self, sample_herb):
        """测试按名称筛选草药成分"""
        response = client.get("/api/v1/tea/herbs?name=测试")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        
        # 检查返回的草药名称是否包含筛选关键词
        for herb in data:
            assert "测试" in herb["name"]
    
    def test_tea_product_response_structure(self, sample_tea_product):
        """测试茶品响应数据结构"""
        response = client.get(f"/api/v1/tea/products/{sample_tea_product.id}")
        
        assert response.status_code == 200
        data = response.json()
        
        # 检查必需字段
        required_fields = [
            "id", "name", "description", "price", "image", "category_name",
            "intro", "effect", "tea_type", "seasonal_recommend", 
            "self_select_enabled", "sales", "status", "recipes"
        ]
        
        for field in required_fields:
            assert field in data, f"Missing required field: {field}"
        
        # 检查数据类型
        assert isinstance(data["price"], (int, float))
        assert isinstance(data["seasonal_recommend"], bool)
        assert isinstance(data["self_select_enabled"], bool)
        assert isinstance(data["sales"], int)
        assert isinstance(data["status"], int)
        assert isinstance(data["recipes"], list)

if __name__ == "__main__":
    pytest.main([__file__])

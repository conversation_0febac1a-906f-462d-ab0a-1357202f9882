import pytest
import httpx
import uuid
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from fastapi_app.main import app
from fastapi_app.core.database import get_db
from fastapi_app.models.user import User
from fastapi_app.crud import user as crud_user
from fastapi_app.services.wechat import wechat_service

client = TestClient(app)

def generate_unique_mock_data():
    """生成唯一的测试数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "access_token_response": {
            "access_token": f"mock_access_token_{unique_id}",
            "expires_in": 7200,
            "refresh_token": f"mock_refresh_token_{unique_id}",
            "openid": f"mock_openid_{unique_id}",
            "scope": "snsapi_userinfo",
            "unionid": f"mock_unionid_{unique_id}"
        },
        "user_info_response": {
            "openid": f"mock_openid_{unique_id}",
            "unionid": f"mock_unionid_{unique_id}",
            "nickname": f"测试用户_{unique_id}",
            "headimgurl": f"https://example.com/avatar_{unique_id}.jpg",
            "sex": 1,  # 男性
            "province": "上海",
            "city": "上海",
            "country": "中国"
        }
    }

# 默认测试数据（用于不需要唯一性的测试）
MOCK_ACCESS_TOKEN_RESPONSE = {
    "access_token": "mock_access_token",
    "expires_in": 7200,
    "refresh_token": "mock_refresh_token",
    "openid": "mock_openid_12345",
    "scope": "snsapi_userinfo",
    "unionid": "mock_unionid_67890"
}

MOCK_USER_INFO_RESPONSE = {
    "openid": "mock_openid_12345",
    "unionid": "mock_unionid_67890",
    "nickname": "测试用户",
    "headimgurl": "https://example.com/avatar.jpg",
    "sex": 1,  # 男性
    "province": "上海",
    "city": "上海",
    "country": "中国"
}

class TestWeChatAuth:
    """微信登录功能测试"""
    
    @pytest.fixture
    def db_session(self):
        """获取数据库会话"""
        db = next(get_db())
        yield db
        db.close()
    
    @patch('fastapi_app.services.wechat.httpx.AsyncClient')
    async def test_wechat_service_get_access_token_success(self, mock_client):
        """测试微信服务获取access_token成功"""
        # Mock HTTP response
        mock_response = AsyncMock()
        mock_response.json.return_value = MOCK_ACCESS_TOKEN_RESPONSE
        mock_response.raise_for_status.return_value = None
        
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        # Test the service
        result = await wechat_service.get_access_token("mock_code")
        
        assert result["access_token"] == "mock_access_token"
        assert result["openid"] == "mock_openid_12345"
        assert result["unionid"] == "mock_unionid_67890"
    
    @patch('fastapi_app.services.wechat.httpx.AsyncClient')
    async def test_wechat_service_get_access_token_error(self, mock_client):
        """测试微信服务获取access_token失败"""
        # Mock error response
        mock_response = AsyncMock()
        mock_response.json.return_value = {
            "errcode": 40029,
            "errmsg": "invalid code"
        }
        mock_response.raise_for_status.return_value = None
        
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        # Test the service should raise HTTPException
        with pytest.raises(Exception):  # Should raise HTTPException
            await wechat_service.get_access_token("invalid_code")
    
    @patch('fastapi_app.services.wechat.httpx.AsyncClient')
    async def test_wechat_service_get_user_info_success(self, mock_client):
        """测试微信服务获取用户信息成功"""
        # Mock HTTP response
        mock_response = AsyncMock()
        mock_response.json.return_value = MOCK_USER_INFO_RESPONSE
        mock_response.raise_for_status.return_value = None
        
        mock_client.return_value.__aenter__.return_value.get.return_value = mock_response
        
        # Test the service
        result = await wechat_service.get_user_info("mock_access_token", "mock_openid_12345")
        
        assert result["openid"] == "mock_openid_12345"
        assert result["nickname"] == "测试用户"
        assert result["sex"] == 1
    
    def test_create_wechat_user(self, db_session: Session):
        """测试创建微信用户"""
        mock_data = generate_unique_mock_data()
        wechat_info = mock_data["user_info_response"]

        # Create user
        user = crud_user.create_wechat_user(db_session, wechat_info)

        assert user.wechat_openid == wechat_info["openid"]
        assert user.wechat_unionid == wechat_info["unionid"]
        assert user.wechat_nickname == wechat_info["nickname"]
        assert user.gender == "male"  # sex=1 should map to male
        assert user.password_hash is None
        assert user.username.startswith("wx_")
    
    def test_get_user_by_wechat_openid(self, db_session: Session):
        """测试通过微信openid查询用户"""
        # Create a user first
        mock_data = generate_unique_mock_data()
        wechat_info = mock_data["user_info_response"]
        created_user = crud_user.create_wechat_user(db_session, wechat_info)

        # Query by openid
        found_user = crud_user.get_user_by_wechat_openid(db_session, wechat_info["openid"])

        assert found_user is not None
        assert found_user.id == created_user.id
        assert found_user.wechat_openid == wechat_info["openid"]
    
    def test_get_user_by_wechat_unionid(self, db_session: Session):
        """测试通过微信unionid查询用户"""
        # Create a user first
        mock_data = generate_unique_mock_data()
        wechat_info = mock_data["user_info_response"]
        created_user = crud_user.create_wechat_user(db_session, wechat_info)

        # Query by unionid
        found_user = crud_user.get_user_by_wechat_unionid(db_session, wechat_info["unionid"])

        assert found_user is not None
        assert found_user.id == created_user.id
        assert found_user.wechat_unionid == wechat_info["unionid"]
    
    def test_update_user_wechat_info(self, db_session: Session):
        """测试更新用户微信信息"""
        # Create a regular user first
        unique_id = str(uuid.uuid4())[:8]
        regular_user = User(
            username=f"regular_user_{unique_id}",
            email=f"test_{unique_id}@example.com",
            password_hash="hashed_password"
        )
        db_session.add(regular_user)
        db_session.commit()
        db_session.refresh(regular_user)

        # Update with WeChat info
        mock_data = generate_unique_mock_data()
        wechat_info = mock_data["user_info_response"]
        updated_user = crud_user.update_user_wechat_info(db_session, regular_user, wechat_info)

        assert updated_user.wechat_openid == wechat_info["openid"]
        assert updated_user.wechat_unionid == wechat_info["unionid"]
        assert updated_user.wechat_nickname == wechat_info["nickname"]
        assert updated_user.avatar_url == wechat_info["headimgurl"]
        assert updated_user.gender == "male"
    
    @patch('fastapi_app.services.wechat.wechat_service.get_access_token')
    @patch('fastapi_app.services.wechat.wechat_service.get_user_info')
    def test_wechat_login_api_new_user(self, mock_get_user_info, mock_get_access_token):
        """测试微信登录API - 新用户"""
        # Mock service responses
        mock_get_access_token.return_value = MOCK_ACCESS_TOKEN_RESPONSE
        mock_get_user_info.return_value = MOCK_USER_INFO_RESPONSE
        
        # Make API request
        response = client.post("/api/v1/auth/wechat/login", json={
            "code": "mock_wechat_code",
            "state": "test_state"
        })
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["code"] == 0
        assert data["message"] == "微信登录成功"
        assert "token" in data["data"]
        assert "userInfo" in data["data"]
        assert data["data"]["loginType"] == "wechat"
        
        user_info = data["data"]["userInfo"]
        assert user_info["wechat_nickname"] == "测试用户"
        assert user_info["username"].startswith("wx_")
    
    def test_wechat_login_api_invalid_code(self):
        """测试微信登录API - 无效授权码"""
        with patch('fastapi_app.services.wechat.wechat_service.get_access_token') as mock_get_token:
            # Mock service to raise HTTPException
            from fastapi import HTTPException
            mock_get_token.side_effect = HTTPException(status_code=400, detail="invalid code")
            
            response = client.post("/api/v1/auth/wechat/login", json={
                "code": "invalid_code"
            })
            
            assert response.status_code == 400
            assert "invalid code" in response.json()["detail"]

if __name__ == "__main__":
    pytest.main([__file__])

#!/usr/bin/env python3
"""
初始化体质报告测试数据脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi_app.core.database import SessionLocal
from fastapi_app.models.user import User
from fastapi_app.models.report import ConstitutionReport
import uuid
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

def create_sample_constitution_reports():
    """创建示例体质报告数据"""
    db = SessionLocal()
    try:
        # 检查是否已有体质报告数据
        existing_report = db.query(ConstitutionReport).first()
        if existing_report:
            logger.info("体质报告数据已存在，跳过初始化")
            return True
        
        # 获取现有用户或创建测试用户
        users = db.query(User).limit(3).all()
        if not users:
            # 创建测试用户
            for i in range(3):
                user = User(
                    id=generate_uuid(),
                    username=f"test_user_{i+1}",
                    email=f"test{i+1}@example.com",
                    password_hash="hashed_password"
                )
                db.add(user)
            db.commit()
            users = db.query(User).limit(3).all()
        
        # 创建体质报告数据
        reports_data = [
            {
                "user_index": 0,
                "title": "阳虚质体质报告",
                "report_data": {
                    "score": 76,
                    "overall": {
                        "text": "您的体质在30-35岁的男性中，优于60%的人。",
                        "BCType": "阳虚质",
                        "mainSymptoms": "失眠多梦且入睡困难伴有焦虑和抑郁"
                    },
                    "analysis": {
                        "BCIndex": {
                            "qiXu": 25,
                            "yangXu": 80,
                            "yinXu": 75,
                            "tanShi": 80,
                            "shiRe": 90,
                            "xueYu": 40,
                            "qiYu": 78,
                            "teBing": 50
                        },
                        "mainBC": "阳虚质",
                        "secondaryBC": ["气虚质", "阴虚质", "痰湿质"]
                    },
                    "definition": {
                        "definition": "阳虚内蕴，以畏寒怕冷、手足不温等阳虚表现为主要特征。",
                        "bodyShape": "形体白胖，肌肉松软。",
                        "commonSymptoms": "畏寒怕冷，手足不温，喜热饮食，精神不振。",
                        "psychology": "性格多沉静、内向。",
                        "diseases": "易患痰饮、肿胀、泄泻等病。",
                        "environment": "不耐寒邪，耐夏不耐冬。"
                    },
                    "dailyTips": {
                        "term": "立夏",
                        "intro": "您的主诉症状为「失眠多梦」，您的体质为「阳虚质」，结合当前节气「立夏」，以下是为您定制化的健康建议：",
                        "food": "宜食温热食物，忌生冷。",
                        "sleep": "保持充足睡眠，避免熬夜。",
                        "exercise": "适量运动，避免大汗淋漓。",
                        "mood": "保持心情愉悦，避免过度思虑。"
                    }
                }
            },
            {
                "user_index": 1,
                "title": "气虚质体质报告",
                "report_data": {
                    "score": 82,
                    "overall": {
                        "text": "您的体质在25-30岁的女性中，优于75%的人。气虚体质主要表现为容易疲劳，抵抗力较弱。",
                        "BCType": "气虚质",
                        "mainSymptoms": "容易疲劳，气短乏力，易感冒"
                    },
                    "analysis": {
                        "BCIndex": {
                            "qiXu": 85,
                            "yangXu": 45,
                            "yinXu": 30,
                            "tanShi": 35,
                            "shiRe": 25,
                            "xueYu": 20,
                            "qiYu": 40,
                            "teBing": 15
                        },
                        "mainBC": "气虚质",
                        "secondaryBC": ["阳虚质", "气郁质"]
                    },
                    "definition": {
                        "definition": "元气不足，以疲乏、气短、自汗等气虚表现为主要特征。",
                        "bodyShape": "形体偏瘦或正常。",
                        "commonSymptoms": "平素语音低弱，气短懒言，容易疲劳，自汗，易感冒，舌淡苔薄，脉弱。",
                        "psychology": "性格内向，不喜冒险。",
                        "diseases": "易患感冒、内脏下垂等病。",
                        "environment": "不耐受风、寒、暑、湿邪。"
                    },
                    "dailyTips": {
                        "term": "小满",
                        "intro": "您的主诉症状为「容易疲劳」，您的体质为「气虚质」，结合当前节气「小满」，以下是为您定制化的健康建议：",
                        "food": "宜食用补气健脾的食物，如山药、大枣、黄芪等。",
                        "sleep": "宜保证充足睡眠，避免熬夜。",
                        "exercise": "宜进行温和的运动，如太极、八段锦等。",
                        "mood": "宜保持心情愉悦，避免过度劳累。"
                    }
                }
            },
            {
                "user_index": 2,
                "title": "阴虚质体质报告",
                "report_data": {
                    "score": 68,
                    "overall": {
                        "text": "您的体质在35-40岁的男性中，优于55%的人。阴虚体质主要表现为口干、手足心热等症状。",
                        "BCType": "阴虚质",
                        "mainSymptoms": "口干咽燥，手足心热，夜间盗汗"
                    },
                    "analysis": {
                        "BCIndex": {
                            "qiXu": 35,
                            "yangXu": 25,
                            "yinXu": 88,
                            "tanShi": 30,
                            "shiRe": 45,
                            "xueYu": 40,
                            "qiYu": 50,
                            "teBing": 20
                        },
                        "mainBC": "阴虚质",
                        "secondaryBC": ["气虚质", "气郁质", "血瘀质"]
                    },
                    "definition": {
                        "definition": "阴液亏少，以口燥咽干、手足心热等虚热表现为主要特征。",
                        "bodyShape": "形体偏瘦。",
                        "commonSymptoms": "手足心热，口燥咽干，鼻微干，喜冷饮，大便干燥，舌红少津，脉细数。",
                        "psychology": "性情急躁，外向好动。",
                        "diseases": "易患虚劳、失精、不寐等病。",
                        "environment": "不耐受暑、热、燥邪。"
                    },
                    "dailyTips": {
                        "term": "芒种",
                        "intro": "您的主诉症状为「口干咽燥」，您的体质为「阴虚质」，结合当前节气「芒种」，以下是为您定制化的健康建议：",
                        "food": "宜食用滋阴润燥的食物，如银耳、百合、梨等。",
                        "sleep": "宜早睡晚起，保证充足睡眠。",
                        "exercise": "宜进行舒缓的运动，避免剧烈运动。",
                        "mood": "宜保持心情平和，避免情绪激动。"
                    }
                }
            }
        ]
        
        # 创建体质报告
        for report_info in reports_data:
            if report_info["user_index"] < len(users):
                user = users[report_info["user_index"]]
                
                report = ConstitutionReport(
                    id=str(uuid.uuid4()),
                    user_id=user.id,
                    title=report_info["title"],
                    report_data_json=json.dumps(report_info["report_data"])
                )
                db.add(report)
                logger.info(f"创建体质报告: {report_info['title']} for user {user.username}")
        
        db.commit()
        logger.info("体质报告数据初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"初始化体质报告数据失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def main():
    """主函数"""
    logger.info("开始初始化体质报告测试数据...")
    
    try:
        if not create_sample_constitution_reports():
            logger.error("创建体质报告数据失败！")
            return 1
        
        logger.info("体质报告测试数据初始化成功！")
        return 0
        
    except Exception as e:
        logger.error(f"初始化过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from fastapi_app.crud import goods as crud_goods
from fastapi_app.schemas import goods as schemas_goods
from fastapi_app.core.database import get_db

router = APIRouter()

@router.get("/")
def read_goods_list(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    goods_list = crud_goods.get_goods_list(db, skip=skip, limit=limit)
    return {
        "message": "获取商品列表成功",
        "data": {
            "items": goods_list,
            "total": len(goods_list),
            "page": skip // limit + 1 if limit > 0 else 1,
            "limit": limit
        }
    }

@router.get("/{goods_id}")
def read_goods_detail(goods_id: str, db: Session = Depends(get_db)):
    db_goods = crud_goods.get_goods_detail(db, goods_id=goods_id)
    if db_goods is None:
        raise HTTPException(status_code=404, detail="Goods not found")
    return {
        "message": "获取商品详情成功",
        "data": db_goods
    } 
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.orm import Session
from fastapi_app.schemas.auth import UserCreate, UserLogin, Token, WeChatLogin
from fastapi_app.crud import user as crud_user
from fastapi_app.core.database import get_db
from fastapi_app.core.security import create_access_token
from fastapi_app.services.wechat import wechat_service
from typing import Optional
import logging
from datetime import datetime, timedelta

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/login")
def login(user: UserLogin, db: Session = Depends(get_db)):
    logger.info(f"登录尝试: 用户名={user.username}")
    
    if not user.username:
        logger.warning("登录失败: 未提供用户名")
        raise HTTPException(status_code=422, detail="Please provide username")
    
    # 特殊处理admin用户 - 与Flask版本保持一致，只检查用户名
    if user.username == "admin":
        # 创建一个固定的admin用户ID，确保与Flask版本一致
        admin_id = "admin_user_id"
        access_token = create_access_token(data={"sub": admin_id})
        logger.info(f"Admin用户登录成功，生成token: {access_token[:10]}...")
        # 返回与Flask版本一致的格式
        return {
            "code": 0,
            "message": "登录成功",
            "data": {
                "token": access_token,
                "access_token": access_token,  # 添加兼容FastAPI内置OAuth的字段
                "tokenExpired": int((datetime.now() + timedelta(hours=2)).timestamp() * 1000),
                "userInfo": {
                    "id": admin_id,
                    "username": "admin",
                    "email": "<EMAIL>",
                    "status": 0,  # 添加状态字段
                    "mobile": "",
                    "created_at": None,
                    "updated_at": None
                },
                "cartCount": 0,
                "favoriteCount": 0
            }
        }
        
    # 尝试通过用户名、邮箱或手机号查找用户
    db_user = None
    
    # 先尝试用username字段查询
    db_user = crud_user.get_user_by_username(db, username=user.username)
    
    # 如果找不到，尝试用邮箱查询
    if not db_user:
        try:
            db_user = crud_user.get_user_by_email(db, email=user.username)
        except:
            pass
    
    # 如果还找不到，尝试用手机号查询
    if not db_user:
        try:
            db_user = crud_user.get_user_by_phone(db, phone=user.username)
        except:
            pass
    
    if not db_user:
        logger.warning(f"Failed login attempt: user not found - {user.username}")
        return {
            "code": 1,
            "message": "用户名或密码错误"
        }
        
    if not crud_user.verify_password(user.password, db_user.password_hash):
        logger.warning(f"Failed login attempt: incorrect password for user: {user.username}")
        return {
            "code": 1,
            "message": "用户名或密码错误"
        }
    
    access_token = create_access_token(data={"sub": str(db_user.id)})
    logger.info(f"User {db_user.username} logged in successfully")
    
    # 获取购物车数量
    cart_count = len(db_user.cart_items) if hasattr(db_user, 'cart_items') else 0
    
    # 返回与Flask版本一致的格式
    return {
        "code": 0,
        "message": "登录成功",
        "data": {
            "token": access_token,
            "access_token": access_token,  # 添加兼容FastAPI内置OAuth的字段
            "tokenExpired": int((datetime.now() + timedelta(hours=2)).timestamp() * 1000),
            "userInfo": {
                "id": db_user.id,
                "username": db_user.username,
                "email": db_user.email,
                "mobile": db_user.mobile,
                "status": 0,  # 固定值
                "created_at": db_user.created_at.isoformat() if hasattr(db_user, 'created_at') and db_user.created_at else None,
                "updated_at": db_user.updated_at.isoformat() if hasattr(db_user, 'updated_at') and db_user.updated_at else None,
            },
            "cartCount": cart_count,
            "favoriteCount": 0  # 暂无收藏功能
        }
    }

@router.post("/register", response_model=dict)
def register(user: UserCreate, db: Session = Depends(get_db)):
    # 检查用户名是否已存在
    db_user_by_username = crud_user.get_user_by_username(db, username=user.username)
    if db_user_by_username:
        return {
            "code": 1, 
            "message": "用户名已注册"
        }
    
    # 检查邮箱是否已存在
    db_user_by_email = crud_user.get_user_by_email(db, email=str(user.email))
    if db_user_by_email:
        return {
            "code": 1, 
            "message": "邮箱已注册"
        }
    
    # 检查手机号是否已存在
    db_user_by_mobile = crud_user.get_user_by_phone(db, phone=user.mobile)
    if db_user_by_mobile:
        return {
            "code": 1, 
            "message": "手机号已注册"
        }
    
    created_user = crud_user.create_user(db=db, user=user)
    
    # 创建访问令牌
    access_token = create_access_token(data={"sub": str(created_user.id)})
    
    # 返回符合Flask格式的响应
    return {
        "code": 0,
        "message": f"注册成功",
        "data": {
            "token": access_token,
            "access_token": access_token,
            "tokenExpired": int((datetime.now() + timedelta(hours=2)).timestamp() * 1000),
            "userInfo": {
                "id": created_user.id,
                "username": created_user.username,
                "email": created_user.email,
                "mobile": created_user.mobile,
                "status": 0
            }
        }
    }

@router.post("/logout")
def logout():
    # JWT无状态，服务端不需要额外处理
    return {
        "code": 0,
        "message": "退出成功",
        "data": {}
    }

@router.post("/password/reset")
def reset_password():
    # Placeholder for password reset logic
    return {
        "code": 0,
        "message": "密码重置功能未实现",
        "data": {}
    }

@router.post("/wechat/login")
async def wechat_login(wechat_data: WeChatLogin, db: Session = Depends(get_db)):
    """
    微信登录接口

    Args:
        wechat_data: 包含微信授权码的数据
        db: 数据库会话

    Returns:
        登录成功的响应，包含token和用户信息
    """
    logger.info(f"微信登录尝试: code={wechat_data.code[:10]}...")

    try:
        # 1. 通过授权码获取access_token
        token_data = await wechat_service.get_access_token(wechat_data.code)

        # 2. 获取用户信息
        user_info = await wechat_service.get_user_info(
            token_data['access_token'],
            token_data['openid']
        )

        # 3. 查找或创建用户
        db_user = None

        # 优先通过unionid查找用户（如果有的话）
        if user_info.get('unionid'):
            db_user = crud_user.get_user_by_wechat_unionid(db, user_info['unionid'])

        # 如果没有找到，通过openid查找
        if not db_user:
            db_user = crud_user.get_user_by_wechat_openid(db, user_info['openid'])

        # 如果用户不存在，创建新用户
        if not db_user:
            logger.info(f"创建新的微信用户: openid={user_info['openid']}")
            db_user = crud_user.create_wechat_user(db, user_info)
        else:
            # 更新现有用户的微信信息
            logger.info(f"更新现有用户的微信信息: user_id={db_user.id}")
            db_user = crud_user.update_user_wechat_info(db, db_user, user_info)

        # 4. 生成JWT token
        access_token = create_access_token(data={"sub": str(db_user.id)})

        # 5. 获取购物车数量
        cart_count = len(db_user.cart_items) if db_user.cart_items else 0

        logger.info(f"微信登录成功: user_id={db_user.id}, username={db_user.username}")

        # 6. 返回登录成功响应
        return {
            "code": 0,
            "message": "微信登录成功",
            "data": {
                "token": access_token,
                "access_token": access_token,
                "tokenExpired": int((datetime.now() + timedelta(hours=2)).timestamp() * 1000),
                "userInfo": {
                    "id": db_user.id,
                    "username": db_user.username,
                    "email": db_user.email,
                    "mobile": db_user.mobile,
                    "real_name": db_user.real_name,
                    "address": db_user.address,
                    "zip_code": db_user.zip_code,
                    "avatar_url": db_user.avatar_url,
                    "is_member": db_user.is_member,
                    "wechat_nickname": db_user.wechat_nickname,
                    "status": 0,
                    "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
                    "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None,
                },
                "cartCount": cart_count,
                "favoriteCount": 0,
                "loginType": "wechat"  # 标识登录方式
            }
        }

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"微信登录过程中发生未知错误: {e}")
        raise HTTPException(status_code=500, detail="微信登录失败，请重试")

@router.post("/sms/send")
def send_sms():
    # Placeholder for sending sms logic
    return {
        "code": 0,
        "message": "短信发送功能未实现", 
        "data": {}
    } 
"""
增强的购物车API端点
整合k_api的购物车接口到主后端项目
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import logging

from fastapi_app.core.database import get_db
from fastapi_app.core.response import APIResponse, handle_exception
from fastapi_app.core.security import get_current_user
from fastapi_app.models.user import User
from fastapi_app.schemas.cart_enhanced import (
    CartItemAdd, CartItemUpdate, CartItemRemove, CartItemSelect,
    CartResponse, CartAddResponse, CartUpdateResponse, 
    CartRemoveResponse, CartSelectResponse
)
from fastapi_app.services.cart_enhanced import cart_service

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("")
async def get_cart_list(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取购物车列表
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        购物车数据，包含商品列表、总价和总数量
    """
    try:
        # 调用服务获取购物车数据
        cart_data = cart_service.get_cart(db, current_user.id)
        
        return APIResponse.success(
            data=cart_data,
            message="获取购物车列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取购物车列表失败: {e}")
        return APIResponse.error(
            message=f"获取购物车列表失败: {str(e)}",
            code=500
        )

@router.post("/add")
async def add_to_cart(
    cart_item: CartItemAdd,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    添加商品到购物车
    
    Args:
        cart_item: 添加到购物车的商品信息
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        添加结果，包含购物车项ID和总数量
    """
    try:
        # 调用服务添加商品到购物车
        cart_item_id, total_quantity = cart_service.add_cart_item(
            db, 
            current_user.id, 
            cart_item.teaProductId, 
            cart_item.quantity,
            cart_item.productType
        )
        
        return APIResponse.success(
            data={
                "cartItemId": cart_item_id,
                "totalQuantity": total_quantity
            },
            message="添加成功"
        )
        
    except ValueError as e:
        logger.warning(f"添加商品到购物车参数错误: {e}")
        return APIResponse.error(
            message=str(e),
            code=400
        )
    except Exception as e:
        logger.error(f"添加商品到购物车失败: {e}")
        return APIResponse.error(
            message=f"添加商品到购物车失败: {str(e)}",
            code=500
        )

@router.put("/update")
async def update_cart(
    cart_item: CartItemUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    更新购物车商品数量
    
    Args:
        cart_item: 更新的购物车商品信息
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        更新结果，包含购物车项、总价和总数量
    """
    try:
        # 调用服务更新购物车商品数量
        item_info, total_price, total_quantity = cart_service.update_cart_item(
            db, current_user.id, cart_item.cartItemId, cart_item.quantity
        )
        
        return APIResponse.success(
            data={
                "item": item_info,
                "totalPrice": total_price,
                "totalQuantity": total_quantity
            },
            message="更新成功"
        )
        
    except ValueError as e:
        logger.warning(f"更新购物车商品数量参数错误: {e}")
        return APIResponse.error(
            message=str(e),
            code=400
        )
    except Exception as e:
        logger.error(f"更新购物车商品数量失败: {e}")
        return APIResponse.error(
            message=f"更新购物车商品数量失败: {str(e)}",
            code=500
        )

@router.delete("/remove")
async def remove_from_cart(
    cart_item: CartItemRemove,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除购物车商品
    
    Args:
        cart_item: 要删除的购物车商品ID列表
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        删除结果，包含总价和总数量
    """
    try:
        # 调用服务删除购物车商品
        total_price, total_quantity = cart_service.remove_cart_items(
            db, current_user.id, cart_item.cartItemIds
        )
        
        return APIResponse.success(
            data={
                "totalPrice": total_price,
                "totalQuantity": total_quantity
            },
            message="删除成功"
        )
        
    except ValueError as e:
        logger.warning(f"删除购物车商品参数错误: {e}")
        return APIResponse.error(
            message=str(e),
            code=400
        )
    except Exception as e:
        logger.error(f"删除购物车商品失败: {e}")
        return APIResponse.error(
            message=f"删除购物车商品失败: {str(e)}",
            code=500
        )

@router.delete("/clear")
async def clear_cart(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    清空购物车
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        清空结果
    """
    try:
        # 调用服务清空购物车
        cart_service.clear_cart(db, current_user.id)
        
        return APIResponse.success(
            data=None,
            message="清空成功"
        )
        
    except Exception as e:
        logger.error(f"清空购物车失败: {e}")
        return APIResponse.error(
            message=f"清空购物车失败: {str(e)}",
            code=500
        )

@router.put("/select")
async def update_cart_selection(
    cart_item: CartItemSelect,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    更新购物车商品选择状态
    
    Args:
        cart_item: 要更新选择状态的购物车商品ID列表和选择状态
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        更新结果，包含选中商品ID列表和选中商品总价
    """
    try:
        # 调用服务更新购物车商品选择状态
        selected_items, selected_total_price = cart_service.update_cart_item_selection(
            db, current_user.id, cart_item.cartItemIds, cart_item.selected
        )
        
        return APIResponse.success(
            data={
                "selectedItems": selected_items,
                "selectedTotalPrice": selected_total_price
            },
            message="更新成功"
        )
        
    except ValueError as e:
        logger.warning(f"更新购物车商品选择状态参数错误: {e}")
        return APIResponse.error(
            message=str(e),
            code=400
        )
    except Exception as e:
        logger.error(f"更新购物车商品选择状态失败: {e}")
        return APIResponse.error(
            message=f"更新购物车商品选择状态失败: {str(e)}",
            code=500
        )

# 兼容性端点：支持k_api的路径格式
@router.get("/", include_in_schema=False)
async def get_cart_legacy(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取购物车列表（兼容k_api格式）
    """
    return await get_cart_list(current_user, db)

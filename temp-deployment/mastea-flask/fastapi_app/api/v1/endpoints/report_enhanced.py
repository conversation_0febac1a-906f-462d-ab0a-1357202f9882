"""
增强的体质报告API端点
整合k_api的体质报告接口到主后端项目
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
import logging

from fastapi_app.core.database import get_db
from fastapi_app.core.response import APIResponse
from fastapi_app.core.security import get_current_user
from fastapi_app.models.user import User
from fastapi_app.services.report_enhanced import report_service

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/overview")
async def report_overview(
    limit: int = Query(30, ge=1, le=100, description="历史报告返回数量，默认为30"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    体质报告总览接口
    
    获取用户体质报告总览数据，包括最新体质分析结果和历史报告列表
    
    Args:
        limit: 历史报告返回数量，默认为30
        current_user: 当前用户，从令牌中获取
        db: 数据库会话
        
    Returns:
        包含报告总览数据的响应
    """
    try:
        report_data = await report_service.get_report_overview(db, current_user.id, limit)
        
        if report_data["has_completed_test"]:
            return APIResponse.success(
                data=report_data,
                message="获取体质报告总览成功"
            )
        else:
            return APIResponse.success(
                data=report_data,
                message="尚无体质报告数据"
            )
            
    except Exception as e:
        logger.error(f"获取体质报告总览失败: {e}")
        return APIResponse.error(
            message=f"服务器错误: {str(e)}",
            code=500
        )

@router.get("/detail")
async def report_detail(
    report_id: str = Query(..., description="报告ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    体质报告详情接口
    
    获取用户特定体质报告的详细信息
    
    Args:
        report_id: 报告ID
        current_user: 当前用户，从令牌中获取
        db: 数据库会话
        
    Returns:
        包含报告详情数据的响应
    """
    try:
        report_data = await report_service.get_report_detail(db, report_id, current_user.id)
        
        if not report_data:
            return APIResponse.error(
                message="未找到指定报告",
                code=404
            )
            
        return APIResponse.success(
            data=report_data,
            message="获取体质报告详情成功"
        )
        
    except Exception as e:
        logger.error(f"获取体质报告详情失败: {e}")
        return APIResponse.error(
            message=f"服务器错误: {str(e)}",
            code=500
        )

@router.get("/tea-recommend")
async def tea_recommend(
    report_id: str = Query(..., description="体质报告ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    体质报告推荐茶品接口
    
    获取基于用户体质报告的个性化茶品推荐
    
    Args:
        report_id: 体质报告ID
        current_user: 当前用户，从令牌中获取
        db: 数据库会话
        
    Returns:
        包含茶品推荐数据的响应
    """
    try:
        recommend_data = await report_service.get_tea_recommend(db, report_id, current_user.id)
        
        if not recommend_data:
            return APIResponse.error(
                message="未找到指定报告",
                code=404
            )
            
        return APIResponse.success(
            data=recommend_data,
            message="获取茶品推荐成功"
        )
        
    except Exception as e:
        logger.error(f"获取茶品推荐失败: {e}")
        return APIResponse.error(
            message=f"服务器错误: {str(e)}",
            code=500
        )

# 兼容性端点：支持不需要认证的访问（用于开发测试）
@router.get("/overview/public")
async def report_overview_public(
    user_id: str = Query(..., description="用户ID"),
    limit: int = Query(30, ge=1, le=100, description="历史报告返回数量"),
    db: Session = Depends(get_db)
):
    """
    体质报告总览接口（公开版本，用于测试）
    
    警告：此接口仅应在开发环境中使用
    """
    try:
        report_data = await report_service.get_report_overview(db, user_id, limit)
        
        return APIResponse.success(
            data=report_data,
            message="获取体质报告总览成功"
        )
        
    except Exception as e:
        logger.error(f"获取体质报告总览失败: {e}")
        return APIResponse.error(
            message=f"服务器错误: {str(e)}",
            code=500
        )

@router.get("/detail/public")
async def report_detail_public(
    report_id: str = Query(..., description="报告ID"),
    user_id: str = Query(..., description="用户ID"),
    db: Session = Depends(get_db)
):
    """
    体质报告详情接口（公开版本，用于测试）
    
    警告：此接口仅应在开发环境中使用
    """
    try:
        report_data = await report_service.get_report_detail(db, report_id, user_id)
        
        if not report_data:
            return APIResponse.error(
                message="未找到指定报告",
                code=404
            )
            
        return APIResponse.success(
            data=report_data,
            message="获取体质报告详情成功"
        )
        
    except Exception as e:
        logger.error(f"获取体质报告详情失败: {e}")
        return APIResponse.error(
            message=f"服务器错误: {str(e)}",
            code=500
        )

@router.get("/tea-recommend/public")
async def tea_recommend_public(
    report_id: str = Query(..., description="体质报告ID"),
    user_id: str = Query(..., description="用户ID"),
    db: Session = Depends(get_db)
):
    """
    体质报告推荐茶品接口（公开版本，用于测试）
    
    警告：此接口仅应在开发环境中使用
    """
    try:
        recommend_data = await report_service.get_tea_recommend(db, report_id, user_id)
        
        if not recommend_data:
            return APIResponse.error(
                message="未找到指定报告",
                code=404
            )
            
        return APIResponse.success(
            data=recommend_data,
            message="获取茶品推荐成功"
        )
        
    except Exception as e:
        logger.error(f"获取茶品推荐失败: {e}")
        return APIResponse.error(
            message=f"服务器错误: {str(e)}",
            code=500
        )

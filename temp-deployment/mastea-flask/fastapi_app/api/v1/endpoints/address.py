from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from fastapi_app.core.database import get_db
from fastapi_app.core.security import get_current_user
from fastapi_app.models.user import User as UserModel
from fastapi_app.crud import address as crud_address
from fastapi_app.schemas import address as schemas_address

router = APIRouter()

@router.get("/", response_model=List[schemas_address.Address])
def get_addresses(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    if not current_user:
        raise HTTPException(status_code=401, detail="未登录")
    return crud_address.get_addresses(db, user_id=current_user.id)

@router.post("/", response_model=schemas_address.Address)
def create_address(
    address: schemas_address.AddressCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    if not current_user:
        raise HTTPException(status_code=401, detail="未登录")
    return crud_address.create_address(db, user_id=current_user.id, address=address)

@router.put("/{address_id}", response_model=schemas_address.Address)
def update_address(
    address_id: str,
    address_update: schemas_address.AddressUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    db_address = crud_address.update_address(db, address_id=address_id, address_update=address_update)
    if not db_address or db_address.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="Address not found")
    return db_address

@router.delete("/{address_id}")
def delete_address(
    address_id: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    db_address = crud_address.delete_address(db, address_id=address_id)
    if not db_address or db_address.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="Address not found")
    return {"ok": True} 
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy.orm import Session
from sqlalchemy import func, text
from datetime import datetime, timedelta
import random
import traceback
import logging
import uuid
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
import os

from fastapi_app.core.database import get_db
from fastapi_app.core.security import get_current_user, oauth2_scheme
from fastapi_app.models.user import User
from fastapi_app.models.goods import Goods
from fastapi_app.models.chat import ChatMessage
from fastapi_app.schemas.user import User as UserSchema
from fastapi_app.schemas.goods import Goods as GoodsSchema, GoodsCreate
from fastapi_app.schemas.chat import ChatMessage as ChatMessageSchema

router = APIRouter()
logger = logging.getLogger(__name__)

# 设置模板目录
templates = Jinja2Templates(directory="fastapi_app/templates")

# 简单的管理员验证
def get_admin_user(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    if not current_user:
        raise HTTPException(status_code=401, detail="认证失败")
    
    # 简单的管理员检查 - 这里为测试目的允许所有用户作为管理员
    # 实际生产环境中应该有更严格的角色验证
    return current_user

@router.get("/dashboard")
async def get_dashboard(db: Session = Depends(get_db)):
    """获取仪表盘统计数据"""
    try:
        # 数据库查询 (用户、商品、聊天)
        user_count = db.query(User).count()
        
        # 使用原始SQL查询获取商品数量，避免ORM模型与数据库不匹配的问题
        goods_count_result = db.execute(text("SELECT COUNT(*) FROM goods")).scalar()
        goods_count = goods_count_result or 0
        
        # 使用原始SQL查询获取聊天消息数量
        chat_count_result = db.execute(text("SELECT COUNT(*) FROM chat_messages")).scalar()
        chat_count = chat_count_result or 0
        
        # 使用原始SQL查询获取今日新用户数量
        today = datetime.now().date()
        today_str = today.strftime('%Y-%m-%d')
        today_users_sql = text("SELECT COUNT(*) FROM users WHERE DATE(created_at) = :today")
        today_users_result = db.execute(today_users_sql, {"today": today}).scalar()
        today_users = today_users_result or 0
        
        # 示例数据
        order_count = 0
        today_orders = 0
        today_sales = 0.0

        # 生成过去7天的用户增长数据
        user_growth_data = []
        for i in range(6, -1, -1):
            past_date = (datetime.now() - timedelta(days=i)).date()
            date_str = past_date.strftime('%Y-%m-%d')
            
            # 使用原始SQL查询获取指定日期的新用户数
            sql = text("SELECT COUNT(*) FROM users WHERE DATE(created_at) = :date")
            result = db.execute(sql, {"date": past_date}).scalar()
            count = result or 0
            
            user_growth_data.append({
                'date': date_str,
                'count': count
            })
        
        # 生成过去7天的销售趋势数据（模拟数据）
        sales_trend_data = []
        for i in range(6, -1, -1):
            past_date = (datetime.now() - timedelta(days=i)).date()
            date_str = past_date.strftime('%Y-%m-%d')
            # 使用固定的随机种子确保每次生成相同的"随机"数据
            random.seed(int(past_date.strftime('%Y%m%d')))
            amount = random.randint(50, 200)
            
            sales_trend_data.append({
                'date': date_str,
                'amount': amount
            })

        # 构建仪表盘统计数据
        stats = {
            'user_stats': {
                'total': user_count,
                'today_new': today_users,
                'active': user_count // 2,  # 示例数据
                'gender_ratio': {'male': 60, 'female': 40}  # 示例数据
            },
            'order_stats': {
                'total': order_count,
                'today': today_orders,
                'pending': 0,
                'completed': 0,
                'total_sales': float(today_sales),
                'refund': 0
            },
            'goods_stats': {
                'total': goods_count,
                'active': goods_count,
                'out_of_stock': 0,
                'most_popular': []
            },
            'chat_stats': {
                'postgresql_messages': chat_count,
                'mongodb_chat_documents': 0,  # 示例数据
                'mongodb_ai_conversations': 0  # 示例数据
            },
            'charts': {
                'sales_trend': sales_trend_data,
                'user_growth': user_growth_data
            },
            'user_count': user_count,
            'goods_count': goods_count,
            'order_count': order_count,
            'today_users': today_users,
            'today_orders': today_orders,
            'today_sales': float(today_sales),
        }

        return {
            'code': 0,
            'message': '获取成功',
            'data': stats
        }
    except Exception as e:
        error_msg = f"获取仪表盘数据失败: {str(e)}"
        stack_trace = traceback.format_exc()
        logger.error(f"{error_msg}\n{stack_trace}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/users")
async def get_users(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    keyword: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取用户列表"""
    query = db.query(User).order_by(User.created_at.desc())
    
    # 搜索条件
    if keyword:
        query = query.filter(
            (User.username.ilike(f'%{keyword}%')) | 
            (User.email.ilike(f'%{keyword}%')) | 
            (User.mobile.ilike(f'%{keyword}%'))
        )
    
    # 计算总数
    total = query.count()
    
    # 分页
    users = query.offset((page - 1) * per_page).limit(per_page).all()
    
    # 计算总页数
    pages = (total + per_page - 1) // per_page if per_page > 0 else 0
    
    return {
        'code': 0,
        'message': '获取成功',
        'data': {
            'items': [user.to_dict() for user in users],
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': pages
        }
    }

@router.get("/goods")
async def get_goods(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    keyword: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取商品列表"""
    try:
        # 使用原始SQL查询，避免ORM模型与数据库不匹配的问题
        count_sql = "SELECT COUNT(*) FROM goods"
        if keyword:
            count_sql += f" WHERE name ILIKE '%{keyword}%'"
        
        total_result = db.execute(text(count_sql)).scalar()
        total = total_result or 0
        
        # 查询商品列表
        sql = "SELECT id, name, price, original_price, image, description, sales, category_id, category_name, status, create_time, update_time FROM goods"
        if keyword:
            sql += f" WHERE name ILIKE '%{keyword}%'"
        
        sql += f" ORDER BY create_time DESC LIMIT {per_page} OFFSET {(page - 1) * per_page}"
        
        result = db.execute(text(sql))
        items = []
        
        for row in result:
            # 转换为字典
            item = {
                'id': row[0],
                'name': row[1],
                'price': float(row[2]) if row[2] else 0.0,
                'original_price': float(row[3]) if row[3] else None,
                'image': row[4],
                'description': row[5],
                'sales': row[6],
                'category_id': row[7],
                'category_name': row[8],
                'status': row[9],
                'created_at': row[10].isoformat() if row[10] else None,
                'updated_at': row[11].isoformat() if row[11] else None
            }
            items.append(item)
        
        # 计算总页数
        pages = (total + per_page - 1) // per_page if per_page > 0 else 0
        
        return {
            'code': 0,
            'message': '获取成功',
            'data': {
                'items': items,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': pages
            }
        }
    except Exception as e:
        error_msg = f"获取商品列表失败: {str(e)}"
        stack_trace = traceback.format_exc()
        logger.error(f"{error_msg}\n{stack_trace}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/chat-messages")
async def get_chat_messages(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    user_id: Optional[str] = None,
    conversation_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取聊天记录"""
    try:
        # 使用原始SQL查询，避免ORM模型与数据库不匹配的问题
        count_sql = "SELECT COUNT(*) FROM chat_messages WHERE 1=1"
        params = {}
        
        if user_id:
            count_sql += " AND user_id = :user_id"
            params['user_id'] = user_id
        
        if conversation_id:
            count_sql += " AND conversation_id = :conversation_id"
            params['conversation_id'] = conversation_id
        
        total_result = db.execute(text(count_sql), params).scalar()
        total = total_result or 0
        
        # 聊天记录查询
        sql = "SELECT id, user_id, message, is_user, timestamp, conversation_id FROM chat_messages WHERE 1=1"
        
        if user_id:
            sql += " AND user_id = :user_id"
        
        if conversation_id:
            sql += " AND conversation_id = :conversation_id"
        
        sql += f" ORDER BY timestamp DESC LIMIT {per_page} OFFSET {(page - 1) * per_page}"
        
        result = db.execute(text(sql), params)
        message_list = []
        
        for row in result:
            # 转换为字典
            message = {
                'id': row[0],
                'user_id': row[1],
                'message': row[2],
                'is_user': row[3],
                'timestamp': int(row[4].timestamp() * 1000) if row[4] else None,
                'conversation_id': row[5] if len(row) > 5 else None
            }
            message_list.append(message)
        
        # 计算总页数
        pages = (total + per_page - 1) // per_page if per_page > 0 else 0
        
        return {
            'code': 0,
            'message': '获取成功',
            'data': {
                'items': message_list,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': pages
            }
        }
    except Exception as e:
        error_msg = f"获取聊天记录失败: {str(e)}"
        stack_trace = traceback.format_exc()
        logger.error(f"{error_msg}\n{stack_trace}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.get("/orders")
async def get_orders(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    status: Optional[int] = Query(None),
    keyword: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取订单列表"""
    try:
        # 构建查询条件
        count_sql = "SELECT COUNT(*) FROM orders WHERE 1=1"
        params = {}
        
        if status is not None:
            count_sql += " AND status = :status"
            params['status'] = status
            
        if keyword:
            count_sql += " AND (order_no ILIKE :keyword OR id ILIKE :keyword)"
            params['keyword'] = f'%{keyword}%'
        
        total_result = db.execute(text(count_sql), params).scalar()
        total = total_result or 0
        
        # 查询订单列表
        sql = """
        SELECT o.id, o.order_no, o.user_id, o.status, o.total_price, o.pay_price, 
               o.create_time, o.pay_time, o.ship_time, o.complete_time, o.cancel_time, 
               o.remark, o.address_snapshot,
               u.username, u.mobile
        FROM orders o 
        LEFT JOIN users u ON o.user_id = u.id 
        WHERE 1=1
        """
        
        if status is not None:
            sql += " AND o.status = :status"
            
        if keyword:
            sql += " AND (o.order_no ILIKE :keyword OR o.id ILIKE :keyword)"
        
        sql += f" ORDER BY o.create_time DESC LIMIT {per_page} OFFSET {(page - 1) * per_page}"
        
        result = db.execute(text(sql), params)
        items = []
        
        status_map = {0: '待付款', 1: '待发货', 2: '待收货', 3: '已完成', 4: '已取消'}
        
        for row in result:
            order = {
                'id': row[0],
                'order_no': row[1],
                'user_id': row[2],
                'status': row[3],
                'status_text': status_map.get(row[3], '未知'),
                'total_price': float(row[4]) if row[4] else 0.0,
                'pay_price': float(row[5]) if row[5] else 0.0,
                'create_time': row[6].isoformat() if row[6] else None,
                'pay_time': row[7].isoformat() if row[7] else None,
                'ship_time': row[8].isoformat() if row[8] else None,
                'complete_time': row[9].isoformat() if row[9] else None,
                'cancel_time': row[10].isoformat() if row[10] else None,
                'remark': row[11],
                'address_snapshot': row[12],
                'username': row[13],
                'user_mobile': row[14]
            }
            items.append(order)
        
        pages = (total + per_page - 1) // per_page if per_page > 0 else 0
        
        return {
            'code': 0,
            'message': '获取成功',
            'data': {
                'items': items,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': pages
            }
        }
    except Exception as e:
        error_msg = f"获取订单列表失败: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/orders/{order_id}/ship")
async def ship_order_admin(
    order_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_admin_user)
):
    """管理员发货"""
    from fastapi_app.crud import order as crud_order
    
    order = crud_order.ship_order(db, order_id=order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在或状态不允许发货")
    
    return {
        "code": 0,
        "message": "发货成功",
        "data": {
            "order_id": order_id,
            "status": order.status,
            "ship_time": order.ship_time.isoformat() if order.ship_time else None
        }
    }

@router.get("/orders/stats")
async def get_order_stats(db: Session = Depends(get_db)):
    """获取订单统计"""
    try:
        # 使用原始SQL查询订单统计
        stats_sql = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN status = 0 THEN 1 END) as pending_payment,
            COUNT(CASE WHEN status = 1 THEN 1 END) as pending_ship,
            COUNT(CASE WHEN status = 2 THEN 1 END) as pending_receive,
            COUNT(CASE WHEN status = 3 THEN 1 END) as completed,
            COUNT(CASE WHEN status = 4 THEN 1 END) as cancelled,
            COALESCE(SUM(CASE WHEN status = 3 THEN total_price END), 0) as total_sales,
            COUNT(CASE WHEN DATE(create_time) = CURRENT_DATE THEN 1 END) as today_orders,
            COALESCE(SUM(CASE WHEN DATE(create_time) = CURRENT_DATE THEN total_price END), 0) as today_sales
        FROM orders
        """
        
        result = db.execute(text(stats_sql)).fetchone()
        
        stats = {
            'total': result[0] or 0,
            'pending_payment': result[1] or 0,
            'pending_ship': result[2] or 0,
            'pending_receive': result[3] or 0,
            'completed': result[4] or 0,
            'cancelled': result[5] or 0,
            'total_sales': float(result[6] or 0),
            'today_orders': result[7] or 0,
            'today_sales': float(result[8] or 0)
        }
        
        return {
            'code': 0,
            'message': '获取成功',
            'data': stats
        }
    except Exception as e:
        error_msg = f"获取订单统计失败: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=error_msg)

@router.post("/goods")
async def create_goods(
    goods_data: dict,
    db: Session = Depends(get_db)
):
    """添加新商品"""
    try:
        # 生成商品ID
        goods_id = str(uuid.uuid4()).replace('-', '')
        
        # 使用原始SQL插入商品，避免ORM模型问题
        insert_sql = text("""
            INSERT INTO goods (id, name, description, price, original_price, 
                             category_name, status, sales, create_time, update_time)
            VALUES (:id, :name, :description, :price, :original_price, 
                    :category_name, :status, :sales, :create_time, :update_time)
        """)
        
        db.execute(insert_sql, {
            'id': goods_id,
            'name': goods_data['name'],
            'description': goods_data.get('description', ''),
            'price': goods_data['price'],
            'original_price': goods_data.get('original_price'),
            'category_name': goods_data.get('category_name', '默认分类'),
            'status': goods_data.get('status', 0),
            'sales': 0,
            'create_time': datetime.now(),
            'update_time': datetime.now()
        })
        
        db.commit()
        
        # 查询刚创建的商品
        select_sql = text("SELECT * FROM goods WHERE id = :id")
        result = db.execute(select_sql, {'id': goods_id}).fetchone()
        
        goods_dict = {
            'id': result[0],
            'name': result[1],
            'price': float(result[2]) if result[2] else 0.0,
            'original_price': float(result[3]) if result[3] else None,
            'description': result[4],
            'category_name': result[8],
            'status': result[9],
            'sales': result[6],
            'created_at': result[10].isoformat() if result[10] else None
        }
        
        return {
            'code': 0,
            'message': '商品添加成功',
            'data': goods_dict
        }
    except Exception as e:
        db.rollback()
        error_msg = f"添加商品失败: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=error_msg) 
# 导入所有模型以确保SQLAlchemy正确初始化关系
# 注意导入顺序：先导入独立模型，然后导入有外键关系的模型

# 基本模型
from fastapi_app.models.user import User
from fastapi_app.models.goods import Goods
from fastapi_app.models.cart import CartItem  # 确保CartItem在User之后被导入
from fastapi_app.models.address import Address
from fastapi_app.models.order import Order
from fastapi_app.models.conversation import Conversation
from fastapi_app.models.chat import ChatMessage
from fastapi_app.models.report import ConstitutionReport, ComplaintReport
from fastapi_app.models.recipe import Recipe, HerbIngredient, RecipeIngredient
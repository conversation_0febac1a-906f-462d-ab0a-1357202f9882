import uuid
from datetime import datetime
from sqlalchemy import String, DateTime, Float, Integer, Text, ForeignKey, <PERSON>olean
from sqlalchemy.orm import Mapped, mapped_column, relationship
from fastapi_app.core.database import Base
from typing import List, TYPE_CHECKING, Dict, Any, Optional

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class Goods(Base):
    __tablename__ = 'goods'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    content: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    price: Mapped[float] = mapped_column(Float, nullable=False, default=0.0)
    original_price: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    image: Mapped[Optional[str]] = mapped_column(String(256), nullable=True)
    sales: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    status: Mapped[int] = mapped_column(Integer, nullable=False, default=0)  # 0-上架, 1-下架
    category_id: Mapped[Optional[str]] = mapped_column(String(32), nullable=True, index=True)
    category_name: Mapped[Optional[str]] = mapped_column(String(64), nullable=True)

    # 茶品特有属性
    intro: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # 茶品介绍
    effect: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # 功效
    tea_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # 茶品类型: basicRecommend, customization
    grams: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 克数
    seasonal_recommend: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否为时令推荐
    self_select_enabled: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否支持自选
    self_select_categories: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # 自选分类，JSON格式存储

    create_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    update_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联
    goods_skus: Mapped[List["GoodsSKU"]] = relationship("GoodsSKU", back_populates="goods", cascade="all, delete-orphan")
    goods_images: Mapped[List["GoodsImage"]] = relationship("GoodsImage", back_populates="goods", cascade="all, delete-orphan")
    recipes: Mapped[List["Recipe"]] = relationship("Recipe", back_populates="goods", cascade="all, delete-orphan")
    
    def to_dict(self, with_detail=False) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            'id': self.id,
            'name': self.name,
            'price': self.price,
            'original_price': self.original_price,
            'image': self.image,
            'description': self.description,
            'sales': self.sales,
            'category_id': self.category_id,
            'category_name': self.category_name,
            'status': self.status,
            # 茶品特有字段
            'intro': self.intro,
            'effect': self.effect,
            'tea_type': self.tea_type,
            'grams': self.grams,
            'seasonal_recommend': self.seasonal_recommend,
            'self_select_enabled': self.self_select_enabled,
            'self_select_categories': self.self_select_categories,
            'created_at': self.create_time.isoformat() if self.create_time else None,
            'updated_at': self.update_time.isoformat() if self.update_time else None
        }
        
        if with_detail:
            result.update({
                'content': self.content,
                'images': [image.to_dict() for image in self.goods_images] if self.goods_images else [],
                'skus': [sku.to_dict() for sku in self.goods_skus] if self.goods_skus else [],
                'recipes': [recipe.to_dict() for recipe in self.recipes] if self.recipes else []
            })
        
        return result

class GoodsSKU(Base):
    __tablename__ = 'goods_skus'
    
    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    goods_id: Mapped[str] = mapped_column(String(32), ForeignKey('goods.id', ondelete='CASCADE'), nullable=False, index=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    price: Mapped[float] = mapped_column(Float, nullable=False, default=0.0)
    stock: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    
    # 关联
    goods: Mapped["Goods"] = relationship("Goods", back_populates="goods_skus")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'goods_id': self.goods_id,
            'name': self.name,
            'price': self.price,
            'stock': self.stock
        }

class GoodsImage(Base):
    __tablename__ = 'goods_images'
    
    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    goods_id: Mapped[str] = mapped_column(String(32), ForeignKey('goods.id', ondelete='CASCADE'), nullable=False, index=True)
    url: Mapped[str] = mapped_column(String(255), nullable=False)
    sort: Mapped[int] = mapped_column(Integer, nullable=False, default=0)  # 排序
    
    # 关联
    goods: Mapped["Goods"] = relationship("Goods", back_populates="goods_images")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'goods_id': self.goods_id,
            'url': self.url,
            'sort': self.sort
        } 
from sqlalchemy.orm import Session
from fastapi_app.models.user import User
from fastapi_app.schemas.auth import UserCreate
from fastapi_app.schemas.user import UserUpdate
from passlib.context import CryptContext
from werkzeug.security import generate_password_hash, check_password_hash

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    # 尝试使用werkzeug进行验证，与Flask应用保持一致
    try:
        return check_password_hash(hashed_password, plain_password)
    except Exception:
        # 如果werkzeug验证失败，尝试使用passlib验证（以防有新创建的用户使用passlib）
        try:
            return pwd_context.verify(plain_password, hashed_password)
        except Exception:
            return False

def get_password_hash(password: str) -> str:
    # 保持与Flask应用相同的哈希方法
    return generate_password_hash(password)

def get_user(db: Session, user_id: int):
    return db.query(User).filter(User.id == user_id).first()

def get_user_by_email(db: Session, email: str):
    return db.query(User).filter(User.email == email).first()

def get_user_by_username(db: Session, username: str):
    return db.query(User).filter(User.username == username).first()

def get_user_by_phone(db: Session, phone: str):
    """使用mobile字段查询用户"""
    return db.query(User).filter(User.mobile == phone).first()

def get_user_by_wechat_openid(db: Session, openid: str):
    """通过微信openid查询用户"""
    return db.query(User).filter(User.wechat_openid == openid).first()

def get_user_by_wechat_unionid(db: Session, unionid: str):
    """通过微信unionid查询用户"""
    return db.query(User).filter(User.wechat_unionid == unionid).first()

def get_users(db: Session, skip: int = 0, limit: int = 100):
    return db.query(User).offset(skip).limit(limit).all()

def create_user(db: Session, user: UserCreate) -> User:
    hashed_password = get_password_hash(user.password)
    
    # 处理username字段，测试中直接传入了username，如果没有就使用nickname
    username = user.username if user.username else user.nickname
    
    # 处理mobile字段，优先使用传入的mobile，如果没有就使用phone
    mobile = user.mobile if user.mobile else user.phone
    
    db_user = User(
        username=username,
        email=user.email,
        mobile=mobile,
        password_hash=hashed_password,
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_user(db: Session, db_user: User, user_in: UserUpdate) -> User:
    user_data = user_in.model_dump(exclude_unset=True)
    for key, value in user_data.items():
        setattr(db_user, key, value)
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def create_wechat_user(db: Session, wechat_info: dict) -> User:
    """创建微信用户"""
    # 生成唯一的用户名
    base_username = f"wx_{wechat_info['openid'][:8]}"
    username = base_username
    counter = 1
    while get_user_by_username(db, username):
        username = f"{base_username}_{counter}"
        counter += 1

    # 处理性别信息
    gender = None
    if wechat_info.get('sex') == 1:
        gender = 'male'
    elif wechat_info.get('sex') == 2:
        gender = 'female'

    db_user = User(
        username=username,
        wechat_openid=wechat_info['openid'],
        wechat_unionid=wechat_info.get('unionid'),
        wechat_nickname=wechat_info.get('nickname'),
        wechat_avatar=wechat_info.get('headimgurl'),
        avatar_url=wechat_info.get('headimgurl'),
        gender=gender,
        password_hash=None  # 微信用户没有密码
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_user_wechat_info(db: Session, db_user: User, wechat_info: dict) -> User:
    """更新用户的微信信息"""
    db_user.wechat_openid = wechat_info['openid']
    db_user.wechat_unionid = wechat_info.get('unionid')
    db_user.wechat_nickname = wechat_info.get('nickname')
    db_user.wechat_avatar = wechat_info.get('headimgurl')

    # 如果用户没有头像，使用微信头像
    if not db_user.avatar_url:
        db_user.avatar_url = wechat_info.get('headimgurl')

    # 处理性别信息（如果用户没有设置性别）
    if not db_user.gender and wechat_info.get('sex'):
        if wechat_info.get('sex') == 1:
            db_user.gender = 'male'
        elif wechat_info.get('sex') == 2:
            db_user.gender = 'female'

    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user
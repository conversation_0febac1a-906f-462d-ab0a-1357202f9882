/**
 * Mastea管理系统JS
 */

// 全局变量
let currentPage = "dashboard";  // 当前页面
let userCurrentPage = 1;        // 用户列表当前页
let goodsCurrentPage = 1;       // 商品列表当前页
let chatsCurrentPage = 1;       // 聊天记录当前页
let apiBase = '/api/v1';        // API基础地址
let salesChart = null;          // 销售图表实例
let userChart = null;           // 用户图表实例

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('管理系统初始化...');
    
    // 检查Chart.js是否加载
    if (typeof Chart === 'undefined') {
        console.error('Chart.js库未加载，图表功能将不可用');
        alert('图表库加载失败，部分功能可能不可用');
    } else {
        console.log('Chart.js库已加载，版本:', Chart.version);
    }
    
    // 检查登录状态
    checkLogin();
    
    // 加载仪表盘
    loadDashboard();
    
    // 监听菜单点击
    document.querySelectorAll('.sidebar a').forEach(link => {
        link.addEventListener('click', function(e) {
            // 阻止默认行为
            e.preventDefault();
            
            // 更新当前选中菜单
            document.querySelectorAll('.sidebar a').forEach(el => {
                el.classList.remove('active');
            });
            this.classList.add('active');
            
            // 加载对应页面
            const page = this.getAttribute('href').substring(1);
            loadPage(page);
        });
    });
});

// 检查登录状态
function checkLogin() {
    const token = localStorage.getItem('admin_token') || localStorage.getItem('token');
    if (!token) {
        window.location.href = '/admin/login';
        return;
    }
    
    // 验证token有效性
    fetch(`${apiBase}/users/me`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('未登录或token已过期');
        }
        return response.json();
    })
    .then(data => {
        if (!data || !data.data) {
            throw new Error('用户信息获取失败');
        }
        // 保存用户信息
        localStorage.setItem('currentUser', JSON.stringify(data.data));
    })
    .catch(error => {
        console.error('验证登录失败:', error);
        // 清除无效token
        localStorage.removeItem('token');
        localStorage.removeItem('admin_token');
        // 跳转到登录页
        window.location.href = '/admin/login';
    });
}

// 加载页面
function loadPage(page) {
    currentPage = page;
    
    // 特殊页面处理
    if (page === 'dashboard') {
        loadDashboard();
    } else if (page === 'users') {
        loadUsers();
    } else if (page === 'goods') {
        loadGoods();
    } else if (page === 'orders') {
        loadOrders();
    } else if (page === 'chats') {
        loadChats();
    }
}

// 获取认证请求头
function getAuthHeaders() {
    const token = localStorage.getItem('admin_token') || localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 加载仪表盘
function loadDashboard() {
    // 先显示加载中状态
    document.getElementById('userCount').textContent = '加载中...';
    document.getElementById('goodsCount').textContent = '加载中...';
    document.getElementById('orderCount').textContent = '加载中...';
    document.getElementById('todayUsers').textContent = '加载中...';
    document.getElementById('todayOrders').textContent = '加载中...';
    document.getElementById('todaySales').textContent = '加载中...';
    
    fetch(`${apiBase}/admin/dashboard`, {
        headers: getAuthHeaders()
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP错误! 状态码: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.code === 0 && data.data) {
            const stats = data.data;
            
            // 更新基础统计数据
            document.getElementById('userCount').textContent = stats.user_count || 0;
            document.getElementById('goodsCount').textContent = stats.goods_count || 0;
            document.getElementById('orderCount').textContent = stats.order_count || 0;
            document.getElementById('todayUsers').textContent = stats.today_users || 0;
            document.getElementById('todayOrders').textContent = stats.today_orders || 0;
            document.getElementById('todaySales').textContent = (stats.today_sales || 0).toFixed(2);
            
            // 更新聊天统计
            document.getElementById('pgChatCount').textContent = stats.chat_stats?.postgresql_messages || 0;
            document.getElementById('mongoChatDocsCount').textContent = stats.chat_stats?.mongodb_chat_documents || 0;
            document.getElementById('mongoAiConvsCount').textContent = stats.chat_stats?.mongodb_ai_conversations || 0;
            
            // 确保图表数据存在且格式正确
            if (stats.charts && Array.isArray(stats.charts.sales_trend) && Array.isArray(stats.charts.user_growth)) {
                // 检查数据格式
                let salesDataValid = true;
                let userDataValid = true;
                
                // 验证销售数据格式
                if (stats.charts.sales_trend.length > 0) {
                    const firstItem = stats.charts.sales_trend[0];
                    if (!firstItem.hasOwnProperty('date') || !firstItem.hasOwnProperty('amount')) {
                        console.error('销售趋势数据格式错误，缺少必要字段');
                        salesDataValid = false;
                    }
                }
                
                // 验证用户数据格式
                if (stats.charts.user_growth.length > 0) {
                    const firstItem = stats.charts.user_growth[0];
                    if (!firstItem.hasOwnProperty('date') || !firstItem.hasOwnProperty('count')) {
                        console.error('用户增长数据格式错误，缺少必要字段');
                        userDataValid = false;
                    }
                }
                
                try {
                    // 绘制图表，使用简单的固定数据进行测试
                    setTimeout(() => {
                        if (salesDataValid) {
                            drawSalesChart(stats.charts.sales_trend);
                        } else {
                            // 使用固定测试数据
                            const testSalesData = [
                                {date: '2025-06-06', amount: 100},
                                {date: '2025-06-07', amount: 150},
                                {date: '2025-06-08', amount: 120},
                                {date: '2025-06-09', amount: 180},
                                {date: '2025-06-10', amount: 200},
                                {date: '2025-06-11', amount: 160},
                                {date: '2025-06-12', amount: 220}
                            ];
                            drawSalesChart(testSalesData);
                        }
                        
                        if (userDataValid) {
                            drawUserChart(stats.charts.user_growth);
                        } else {
                            // 使用固定测试数据
                            const testUserData = [
                                {date: '2025-06-06', count: 5},
                                {date: '2025-06-07', count: 8},
                                {date: '2025-06-08', count: 3},
                                {date: '2025-06-09', count: 7},
                                {date: '2025-06-10', count: 10},
                                {date: '2025-06-11', count: 6},
                                {date: '2025-06-12', count: 12}
                            ];
                            drawUserChart(testUserData);
                        }
                    }, 500); // 增加延迟，确保DOM已完全加载
                } catch (chartError) {
                    console.error('绘制图表时出错:', chartError);
                }
            } else {
                // 隐藏加载状态
                const loadingEls = document.querySelectorAll('.chart-loading');
                loadingEls.forEach(el => el.style.display = 'none');
                
                // 显示错误信息
                const errorEls = document.querySelectorAll('.chart-error');
                errorEls.forEach(el => {
                    el.textContent = '图表数据格式不正确或缺失';
                    el.classList.remove('d-none');
                });
            }
        } else {
            console.error('仪表盘数据格式错误:', data);
            // 隐藏加载状态
            const loadingEls = document.querySelectorAll('.chart-loading');
            loadingEls.forEach(el => el.style.display = 'none');
            
            // 显示错误信息
            const errorEls = document.querySelectorAll('.chart-error');
            errorEls.forEach(el => {
                el.textContent = data.message || '仪表盘数据格式错误';
                el.classList.remove('d-none');
            });
        }
    })
    .catch(error => {
        console.error('加载仪表盘失败:', error);
        
        // 设置默认值
        document.getElementById('userCount').textContent = '-';
        document.getElementById('goodsCount').textContent = '-';
        document.getElementById('orderCount').textContent = '-';
        document.getElementById('todayUsers').textContent = '-';
        document.getElementById('todayOrders').textContent = '-';
        document.getElementById('todaySales').textContent = '-.--';
        
        // 隐藏加载状态
        const loadingEls = document.querySelectorAll('.chart-loading');
        loadingEls.forEach(el => el.style.display = 'none');
        
        // 显示错误信息
        const errorEls = document.querySelectorAll('.chart-error');
        errorEls.forEach(el => {
            el.textContent = `加载失败: ${error.message}`;
            el.classList.remove('d-none');
        });
    });
}

// 加载用户列表
function loadUsers(page = 1, keyword = '') {
    userCurrentPage = page;
    
    // 显示用户管理页面
    const template = document.getElementById('users-template');
    document.getElementById('pageContent').innerHTML = template.innerHTML;
    
    // 绑定搜索按钮事件
    document.getElementById('userSearchBtn').addEventListener('click', function() {
        const keyword = document.getElementById('userSearchInput').value;
        loadUsers(1, keyword);
    });
    
    // 发送API请求获取用户列表
    const params = new URLSearchParams();
    params.append('page', page);
    params.append('per_page', 10);
    if (keyword) params.append('keyword', keyword);
    
    fetch(`${apiBase}/admin/users?${params.toString()}`, {
        headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0 && data.data) {
            const result = data.data;
            const tableBody = document.getElementById('userTableBody');
            tableBody.innerHTML = '';
            
            // 渲染用户列表
            if (result.items.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center">没有找到用户</td></tr>';
            } else {
                result.items.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${user.id}</td>
                        <td>${user.username}</td>
                        <td>${user.email || '-'}</td>
                        <td>${user.mobile || '-'}</td>
                        <td>${formatDate(user.created_at)}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }
            
            // 渲染分页
            renderPagination('userPagination', result.page, result.pages, function(p) {
                loadUsers(p, keyword);
            });
        }
    })
    .catch(error => {
        console.error('加载用户列表失败:', error);
        showToast('加载用户列表失败', 'danger');
    });
}

// 加载商品列表
function loadGoods(page = 1, keyword = '') {
    goodsCurrentPage = page;
    
    // 显示商品管理页面
    const template = document.getElementById('goods-template');
    document.getElementById('pageContent').innerHTML = template.innerHTML;
    
    // 绑定搜索按钮事件
    document.getElementById('goodsSearchBtn').addEventListener('click', function() {
        const keyword = document.getElementById('goodsSearchInput').value;
        loadGoods(1, keyword);
    });
    
    // 发送API请求获取商品列表
    const params = new URLSearchParams();
    params.append('page', page);
    params.append('per_page', 10);
    if (keyword) params.append('keyword', keyword);
    
    fetch(`${apiBase}/admin/goods?${params.toString()}`, {
        headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0 && data.data) {
            const result = data.data;
            const tableBody = document.getElementById('goodsTableBody');
            tableBody.innerHTML = '';
            
            // 渲染商品列表
            if (result.items.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">没有找到商品</td></tr>';
            } else {
                result.items.forEach(goods => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${goods.id}</td>
                        <td><img src="${goods.image || '/static/img/no-image.png'}" alt="${goods.name}" style="width: 50px; height: 50px;"></td>
                        <td>${goods.name}</td>
                        <td>￥${goods.price ? goods.price.toFixed(2) : '0.00'}</td>
                        <td>${goods.stock || '0'}</td>
                        <td>${goods.is_enabled ? '<span class="badge bg-success">上架</span>' : '<span class="badge bg-secondary">下架</span>'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary me-1">编辑</button>
                            <button class="btn btn-sm btn-outline-danger">下架</button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            }
            
            // 渲染分页
            renderPagination('goodsPagination', result.page, result.pages, function(p) {
                loadGoods(p, keyword);
            });
        }
    })
    .catch(error => {
        console.error('加载商品列表失败:', error);
        showToast('加载商品列表失败', 'danger');
    });
}

// 加载聊天记录
function loadChats(page = 1, user_id = null, conversation_id = null) {
    chatsCurrentPage = page;
    
    // 显示聊天记录页面
    document.getElementById('pageContent').innerHTML = `
        <h2 class="mb-4">聊天记录</h2>
        <div class="card">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="chatUserIdInput" placeholder="用户ID筛选...">
                            <button class="btn btn-outline-secondary" type="button" id="chatFilterBtn">筛选</button>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户ID</th>
                                <th>会话ID</th>
                                <th>消息内容</th>
                                <th>角色</th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody id="chatTableBody">
                            <!-- 聊天数据将通过API加载 -->
                        </tbody>
                    </table>
                </div>
                <nav>
                    <ul class="pagination" id="chatPagination">
                        <!-- 分页将通过函数生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    `;
    
    // 绑定筛选按钮事件
    document.getElementById('chatFilterBtn').addEventListener('click', function() {
        const userId = document.getElementById('chatUserIdInput').value.trim();
        loadChats(1, userId || null);
    });
    
    // 发送API请求获取聊天记录
    const params = new URLSearchParams();
    params.append('page', page);
    params.append('per_page', 10);
    if (user_id) params.append('user_id', user_id);
    if (conversation_id) params.append('conversation_id', conversation_id);
    
    fetch(`${apiBase}/admin/chat-messages?${params.toString()}`, {
        headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0 && data.data) {
            const result = data.data;
            const tableBody = document.getElementById('chatTableBody');
            tableBody.innerHTML = '';
            
            // 渲染聊天记录
            if (result.items.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center">没有找到聊天记录</td></tr>';
            } else {
                result.items.forEach(message => {
                    // 截断过长消息
                    let displayMessage = message.message;
                    if (displayMessage.length > 50) {
                        displayMessage = displayMessage.substring(0, 47) + '...';
                    }
                    
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${message.id}</td>
                        <td>${message.user_id}</td>
                        <td>${message.conversation_id}</td>
                        <td title="${message.message}">${displayMessage}</td>
                        <td>${message.is_user ? '<span class="badge bg-primary">用户</span>' : '<span class="badge bg-info">AI</span>'}</td>
                        <td>${formatDate(message.timestamp)}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }
            
            // 渲染分页
            renderPagination('chatPagination', result.page, result.pages, function(p) {
                loadChats(p, user_id, conversation_id);
            });
        }
    })
    .catch(error => {
        console.error('加载聊天记录失败:', error);
        showToast('加载聊天记录失败', 'danger');
    });
}

// 渲染分页
function renderPagination(elementId, currentPage, totalPages, callback) {
    const paginationEl = document.getElementById(elementId);
    paginationEl.innerHTML = '';
    
    // 只有1页时不显示分页
    if (totalPages <= 1) return;
    
    // 上一页
    const prevItem = document.createElement('li');
    prevItem.className = `page-item ${currentPage <= 1 ? 'disabled' : ''}`;
    prevItem.innerHTML = `<a class="page-link" href="#">上一页</a>`;
    if (currentPage > 1) {
        prevItem.addEventListener('click', function(e) {
            e.preventDefault();
            callback(currentPage - 1);
        });
    }
    paginationEl.appendChild(prevItem);
    
    // 页码
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);
    if (endPage - startPage < 4) {
        startPage = Math.max(1, endPage - 4);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        const pageItem = document.createElement('li');
        pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;
        pageItem.innerHTML = `<a class="page-link" href="#">${i}</a>`;
        
        if (i !== currentPage) {
            pageItem.addEventListener('click', function(e) {
                e.preventDefault();
                callback(i);
            });
        }
        
        paginationEl.appendChild(pageItem);
    }
    
    // 下一页
    const nextItem = document.createElement('li');
    nextItem.className = `page-item ${currentPage >= totalPages ? 'disabled' : ''}`;
    nextItem.innerHTML = `<a class="page-link" href="#">下一页</a>`;
    if (currentPage < totalPages) {
        nextItem.addEventListener('click', function(e) {
            e.preventDefault();
            callback(currentPage + 1);
        });
    }
    paginationEl.appendChild(nextItem);
}

// 绘制销售趋势图表
function drawSalesChart(data) {
    // 保存数据以便重新加载
    window.salesChartData = data;
    
    // 显示加载状态
    const loadingEl = document.getElementById('salesChartLoading');
    const errorEl = document.getElementById('salesChartError');
    
    if (loadingEl) loadingEl.style.display = 'flex';
    if (errorEl) errorEl.classList.add('d-none');
    
    if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('销售图表数据为空或格式不正确');
        if (loadingEl) loadingEl.style.display = 'none';
        if (errorEl) {
            errorEl.textContent = '无法绘制图表：数据为空或格式不正确';
            errorEl.classList.remove('d-none');
        }
        return;
    }
    
    const ctx = document.getElementById('salesChart');
    if (!ctx) {
        console.error('找不到销售图表的canvas元素');
        if (loadingEl) loadingEl.style.display = 'none';
        if (errorEl) {
            errorEl.textContent = '无法绘制图表：找不到图表元素';
            errorEl.classList.remove('d-none');
        }
        return;
    }
    
    // 确保Chart对象可用
    if (typeof Chart === 'undefined') {
        console.error('Chart.js库未加载');
        if (loadingEl) loadingEl.style.display = 'none';
        if (errorEl) {
            errorEl.textContent = '无法绘制图表：Chart.js库未加载';
            errorEl.classList.remove('d-none');
        }
        return;
    }
    
    // 销毁旧图表
    if (salesChart instanceof Chart) {
        try {
            salesChart.destroy();
        } catch (error) {
            console.error('销毁旧图表时出错:', error);
        }
    }
    
    try {
        // 准备数据
        const labels = data.map(item => item.date || '');
        const amounts = data.map(item => item.amount || 0);
        
        // 创建新图表
        salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '销售额',
                    data: amounts,
                    borderColor: 'rgba(54, 162, 235, 1)',
                    tension: 0.1,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // 隐藏加载状态
        if (loadingEl) loadingEl.style.display = 'none';
    } catch (error) {
        console.error('绘制销售图表时出错:', error);
        
        // 显示错误信息
        if (loadingEl) loadingEl.style.display = 'none';
        if (errorEl) {
            errorEl.textContent = `绘制图表失败: ${error.message}`;
            errorEl.classList.remove('d-none');
        }
    }
}

// 绘制用户增长图表
function drawUserChart(data) {
    // 保存数据以便重新加载
    window.userChartData = data;
    
    // 显示加载状态
    const loadingEl = document.getElementById('userChartLoading');
    const errorEl = document.getElementById('userChartError');
    
    if (loadingEl) loadingEl.style.display = 'flex';
    if (errorEl) errorEl.classList.add('d-none');
    
    if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('用户图表数据为空或格式不正确');
        if (loadingEl) loadingEl.style.display = 'none';
        if (errorEl) {
            errorEl.textContent = '无法绘制图表：数据为空或格式不正确';
            errorEl.classList.remove('d-none');
        }
        return;
    }
    
    const ctx = document.getElementById('userChart');
    if (!ctx) {
        console.error('找不到用户图表的canvas元素');
        if (loadingEl) loadingEl.style.display = 'none';
        if (errorEl) {
            errorEl.textContent = '无法绘制图表：找不到图表元素';
            errorEl.classList.remove('d-none');
        }
        return;
    }
    
    // 确保Chart对象可用
    if (typeof Chart === 'undefined') {
        console.error('Chart.js库未加载');
        if (loadingEl) loadingEl.style.display = 'none';
        if (errorEl) {
            errorEl.textContent = '无法绘制图表：Chart.js库未加载';
            errorEl.classList.remove('d-none');
        }
        return;
    }
    
    // 销毁旧图表
    if (userChart instanceof Chart) {
        try {
            userChart.destroy();
        } catch (error) {
            console.error('销毁旧图表时出错:', error);
        }
    }
    
    try {
        // 准备数据
        const labels = data.map(item => item.date || '');
        const counts = data.map(item => item.count || 0);
        
        // 创建新图表
        userChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '新增用户',
                    data: counts,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // 隐藏加载状态
        if (loadingEl) loadingEl.style.display = 'none';
    } catch (error) {
        console.error('绘制用户图表时出错:', error);
        
        // 显示错误信息
        if (loadingEl) loadingEl.style.display = 'none';
        if (errorEl) {
            errorEl.textContent = `绘制图表失败: ${error.message}`;
            errorEl.classList.remove('d-none');
        }
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    console.log(`显示Toast消息: ${message} (类型: ${type})`);
    
    // 使用Bootstrap的toast组件
    const toastEl = document.getElementById('liveToast');
    const toastMessage = document.getElementById('toastMessage');
    
    if (!toastEl || !toastMessage) {
        // 降级为alert
        console.warn('Toast元素不存在，使用alert作为替代');
        alert(message);
        return;
    }
    
    try {
        // 设置消息内容
        toastMessage.textContent = message;
        
        // 设置样式
        toastEl.classList.remove('bg-success', 'bg-danger', 'bg-warning', 'bg-info', 'text-white');
        switch (type) {
            case 'success':
                toastEl.classList.add('bg-success', 'text-white');
                break;
            case 'danger':
                toastEl.classList.add('bg-danger', 'text-white');
                break;
            case 'warning':
                toastEl.classList.add('bg-warning');
                break;
            default:
                toastEl.classList.add('bg-info', 'text-white');
        }
        
        // 显示toast
        if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
            const toast = new bootstrap.Toast(toastEl);
            toast.show();
        } else {
            console.warn('Bootstrap Toast组件不可用，使用简单显示');
            toastEl.style.display = 'block';
            setTimeout(() => {
                toastEl.style.display = 'none';
            }, 3000);
        }
    } catch (error) {
        console.error('显示Toast消息时出错:', error);
        // 最后的降级方案
        try {
            alert(message);
        } catch (e) {
            console.error('显示alert时也出错:', e);
        }
    }
}

// 格式化日期
function formatDate(timestamp) {
    if (!timestamp) return '-';
    
    // 处理不同格式的日期
    let date;
    if (typeof timestamp === 'string') {
        date = new Date(timestamp);
    } else if (typeof timestamp === 'number') {
        date = new Date(timestamp);
    } else {
        return '-';
    }
    
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

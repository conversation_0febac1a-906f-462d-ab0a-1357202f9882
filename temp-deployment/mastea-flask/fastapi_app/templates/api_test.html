<!DOCTYPE html>
<html>
<head>
    <title>Mastea API 测试工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .api-section { margin-bottom: 2rem; }
        .response-area { 
            min-height: 200px; 
            max-height: 400px;
            overflow-y: auto;
        }
        .token-info {
            background-color: #f8f9fa;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Mastea API 测试工具</h1>
        
        <!-- Token 信息 -->
        <div class="token-info">
            <h5>认证信息</h5>
            <div class="mb-2">
                <label class="form-label">Token:</label>
                <input type="text" class="form-control" id="tokenInput" readonly>
            </div>
            <div class="mb-2">
                <label class="form-label">Token 过期时间:</label>
                <input type="text" class="form-control" id="tokenExpiredInput" readonly>
            </div>
        </div>
        
        <!-- 认证相关 -->
        <div class="api-section">
            <h2>认证相关</h2>
            
            <!-- 登录 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>登录</h5>
                </div>
                <div class="card-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" name="username" value="demo">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-control" name="password" value="demo123">
                        </div>
                        <button type="submit" class="btn btn-primary">登录</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 注册 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>注册</h5>
                </div>
                <div class="card-body">
                    <form id="registerForm">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" name="username">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-control" name="password">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">手机号</label>
                            <input type="text" class="form-control" name="mobile">
                        </div>
                        <button type="submit" class="btn btn-primary">注册</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 用户相关 -->
        <div class="api-section">
            <h2>用户相关</h2>
            
            <!-- 获取用户信息 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>获取用户信息</h5>
                </div>
                <div class="card-body">
                    <button id="getUserInfoBtn" class="btn btn-primary">获取信息</button>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 更新用户信息 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>更新用户信息</h5>
                </div>
                <div class="card-body">
                    <form id="updateUserForm">
                        <div class="mb-3">
                            <label class="form-label">性别</label>
                            <select class="form-select" name="gender">
                                <option value="">请选择</option>
                                <option value="male">男</option>
                                <option value="female">女</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">年龄</label>
                            <input type="number" class="form-control" name="age">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">身高(cm)</label>
                            <input type="number" class="form-control" name="height">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">体重(kg)</label>
                            <input type="number" class="form-control" name="weight">
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_pregnant" id="isPregnant">
                                <label class="form-check-label" for="isPregnant">是否怀孕</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_preparing_pregnancy" id="isPreparingPregnancy">
                                <label class="form-check-label" for="isPreparingPregnancy">是否备孕</label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">更新信息</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 商品相关 -->
        <div class="api-section">
            <h2>商品相关</h2>
            
            <!-- 获取商品列表 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>获取商品列表</h5>
                </div>
                <div class="card-body">
                    <form id="goodsListForm">
                        <div class="mb-3">
                            <label class="form-label">分类ID</label>
                            <input type="number" class="form-control" name="category_id">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">页码</label>
                            <input type="number" class="form-control" name="page" value="1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">每页数量</label>
                            <input type="number" class="form-control" name="page_size" value="10">
                        </div>
                        <button type="submit" class="btn btn-primary">获取列表</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 获取商品详情 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>获取商品详情</h5>
                </div>
                <div class="card-body">
                    <form id="goodsDetailForm">
                        <div class="mb-3">
                            <label class="form-label">商品ID</label>
                            <input type="text" class="form-control" name="goods_id">
                        </div>
                        <button type="submit" class="btn btn-primary">获取详情</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 获取商品分类 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>获取商品分类</h5>
                </div>
                <div class="card-body">
                    <button id="getCategoriesBtn" class="btn btn-primary">获取分类</button>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 购物车相关 -->
        <div class="api-section">
            <h2>购物车相关</h2>
            
            <!-- 添加商品到购物车 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>添加商品到购物车</h5>
                </div>
                <div class="card-body">
                    <form id="addToCartForm">
                        <div class="mb-3">
                            <label class="form-label">商品ID</label>
                            <input type="text" class="form-control" name="goods_id">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">数量</label>
                            <input type="number" class="form-control" name="quantity" value="1">
                        </div>
                        <button type="submit" class="btn btn-primary">添加到购物车</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 获取购物车列表 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>获取购物车列表</h5>
                </div>
                <div class="card-body">
                    <button id="getCartListBtn" class="btn btn-primary">获取列表</button>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 更新购物车商品 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>更新购物车商品</h5>
                </div>
                <div class="card-body">
                    <form id="updateCartForm">
                        <div class="mb-3">
                            <label class="form-label">购物车商品ID</label>
                            <input type="text" class="form-control" name="cart_item_id">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">数量</label>
                            <input type="number" class="form-control" name="quantity">
                        </div>
                        <button type="submit" class="btn btn-primary">更新数量</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 删除购物车商品 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>删除购物车商品</h5>
                </div>
                <div class="card-body">
                    <form id="deleteCartForm">
                        <div class="mb-3">
                            <label class="form-label">购物车商品ID</label>
                            <input type="text" class="form-control" name="cart_item_id">
                        </div>
                        <button type="submit" class="btn btn-danger">删除商品</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 订单相关 -->
        <div class="api-section">
            <h2>订单相关</h2>
            
            <!-- 创建订单 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>创建订单</h5>
                </div>
                <div class="card-body">
                    <form id="createOrderForm">
                        <div class="mb-3">
                            <label class="form-label">地址ID</label>
                            <input type="text" class="form-control" name="address_id">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">购物车商品ID（多个用逗号分隔）</label>
                            <input type="text" class="form-control" name="cart_item_ids">
                        </div>
                        <button type="submit" class="btn btn-primary">创建订单</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 获取订单列表 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>获取订单列表</h5>
                </div>
                <div class="card-body">
                    <form id="orderListForm">
                        <div class="mb-3">
                            <label class="form-label">订单状态</label>
                            <select class="form-select" name="status">
                                <option value="">全部</option>
                                <option value="1">待支付</option>
                                <option value="2">已支付</option>
                                <option value="3">已发货</option>
                                <option value="4">已完成</option>
                                <option value="5">已取消</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">页码</label>
                            <input type="number" class="form-control" name="page" value="1">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">每页数量</label>
                            <input type="number" class="form-control" name="page_size" value="10">
                        </div>
                        <button type="submit" class="btn btn-primary">获取列表</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 获取订单详情 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>获取订单详情</h5>
                </div>
                <div class="card-body">
                    <form id="orderDetailForm">
                        <div class="mb-3">
                            <label class="form-label">订单ID</label>
                            <input type="text" class="form-control" name="order_id">
                        </div>
                        <button type="submit" class="btn btn-primary">获取详情</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 地址相关 -->
        <div class="api-section">
            <h2>地址相关</h2>
            
            <!-- 添加地址 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>添加地址</h5>
                </div>
                <div class="card-body">
                    <form id="addAddressForm">
                        <div class="mb-3">
                            <label class="form-label">收货人</label>
                            <input type="text" class="form-control" name="name">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">手机号</label>
                            <input type="text" class="form-control" name="mobile">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">省份</label>
                            <input type="text" class="form-control" name="province">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">城市</label>
                            <input type="text" class="form-control" name="city">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">区县</label>
                            <input type="text" class="form-control" name="district">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">详细地址</label>
                            <input type="text" class="form-control" name="detail">
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_default" id="isDefaultAddress">
                                <label class="form-check-label" for="isDefaultAddress">设为默认地址</label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">添加地址</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 获取地址列表 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>获取地址列表</h5>
                </div>
                <div class="card-body">
                    <button id="getAddressListBtn" class="btn btn-primary">获取列表</button>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 更新地址 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>更新地址</h5>
                </div>
                <div class="card-body">
                    <form id="updateAddressForm">
                        <div class="mb-3">
                            <label class="form-label">地址ID</label>
                            <input type="text" class="form-control" name="address_id">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">收货人</label>
                            <input type="text" class="form-control" name="name">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">手机号</label>
                            <input type="text" class="form-control" name="mobile">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">省份</label>
                            <input type="text" class="form-control" name="province">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">城市</label>
                            <input type="text" class="form-control" name="city">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">区县</label>
                            <input type="text" class="form-control" name="district">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">详细地址</label>
                            <input type="text" class="form-control" name="detail">
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_default" id="updateIsDefaultAddress">
                                <label class="form-check-label" for="updateIsDefaultAddress">设为默认地址</label>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">更新地址</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
            
            <!-- 删除地址 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5>删除地址</h5>
                </div>
                <div class="card-body">
                    <form id="deleteAddressForm">
                        <div class="mb-3">
                            <label class="form-label">地址ID</label>
                            <input type="text" class="form-control" name="address_id">
                        </div>
                        <button type="submit" class="btn btn-danger">删除地址</button>
                    </form>
                    <div class="mt-3">
                        <h6>响应：</h6>
                        <pre class="response-area bg-light p-3"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5001/api';
        let currentToken = '';
        let tokenExpired = 0;
        
        // 更新 token 显示
        function updateTokenDisplay() {
            document.getElementById('tokenInput').value = currentToken;
            if (tokenExpired) {
                const date = new Date(tokenExpired);
                document.getElementById('tokenExpiredInput').value = date.toLocaleString();
            } else {
                document.getElementById('tokenExpiredInput').value = '';
            }
        }
        
        // 设置 token
        function setToken(token, expired) {
            currentToken = token;
            tokenExpired = expired;
            updateTokenDisplay();
        }
        
        // 发送请求的通用函数
        async function sendRequest(url, method, data = null, needAuth = true) {
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };
            
            if (needAuth && currentToken) {
                headers['Authorization'] = `Bearer ${currentToken}`;
            }
            
            try {
                const response = await fetch(url, {
                    method,
                    headers,
                    body: data ? JSON.stringify(data) : null,
                    mode: 'cors',
                    credentials: 'omit'
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('Error:', error);
                return {
                    code: 1,
                    message: '请求失败',
                    error: error.message
                };
            }
        }
        
        // 设置默认值
        function setDefaultValues() {
            // 登录表单默认值
            document.querySelector('#loginForm [name="username"]').value = 'demo';
            document.querySelector('#loginForm [name="password"]').value = 'demo123';
            
            // 注册表单默认值
            document.querySelector('#registerForm [name="username"]').value = 'newuser';
            document.querySelector('#registerForm [name="password"]').value = 'password123';
            document.querySelector('#registerForm [name="email"]').value = '<EMAIL>';
            document.querySelector('#registerForm [name="mobile"]').value = '13800138000';
            
            // 更新用户信息表单默认值
            document.querySelector('#updateUserForm [name="gender"]').value = 'male';
            document.querySelector('#updateUserForm [name="age"]').value = '25';
            document.querySelector('#updateUserForm [name="height"]').value = '170';
            document.querySelector('#updateUserForm [name="weight"]').value = '65';
            document.querySelector('#updateUserForm [name="is_pregnant"]').checked = false;
            document.querySelector('#updateUserForm [name="is_preparing_pregnancy"]').checked = false;
            
            // 商品列表表单默认值
            document.querySelector('#goodsListForm [name="category_id"]').value = '2';  // 绿茶分类
            document.querySelector('#goodsListForm [name="page"]').value = '1';
            document.querySelector('#goodsListForm [name="page_size"]').value = '10';
            
            // 商品详情表单默认值
            document.querySelector('#goodsDetailForm [name="goods_id"]').value = '1';  // 西湖龙井
            
            // 添加购物车表单默认值
            document.querySelector('#addToCartForm [name="goods_id"]').value = '1';
            document.querySelector('#addToCartForm [name="quantity"]').value = '1';
            
            // 更新购物车表单默认值
            document.querySelector('#updateCartForm [name="cart_item_id"]').value = '1';
            document.querySelector('#updateCartForm [name="quantity"]').value = '2';
            
            // 删除购物车表单默认值
            document.querySelector('#deleteCartForm [name="cart_item_id"]').value = '1';
            
            // 创建订单表单默认值
            document.querySelector('#createOrderForm [name="address_id"]').value = '1';
            document.querySelector('#createOrderForm [name="cart_item_ids"]').value = '1,2';
            
            // 订单列表表单默认值
            document.querySelector('#orderListForm [name="status"]').value = '';
            document.querySelector('#orderListForm [name="page"]').value = '1';
            document.querySelector('#orderListForm [name="page_size"]').value = '10';
            
            // 订单详情表单默认值
            document.querySelector('#orderDetailForm [name="order_id"]').value = '1';
            
            // 添加地址表单默认值
            document.querySelector('#addAddressForm [name="name"]').value = '张三';
            document.querySelector('#addAddressForm [name="mobile"]').value = '13800138000';
            document.querySelector('#addAddressForm [name="province"]').value = '浙江省';
            document.querySelector('#addAddressForm [name="city"]').value = '杭州市';
            document.querySelector('#addAddressForm [name="district"]').value = '西湖区';
            document.querySelector('#addAddressForm [name="detail"]').value = '文三路 123 号';
            document.querySelector('#addAddressForm [name="is_default"]').checked = true;
            
            // 更新地址表单默认值
            document.querySelector('#updateAddressForm [name="address_id"]').value = '1';
            document.querySelector('#updateAddressForm [name="name"]').value = '李四';
            document.querySelector('#updateAddressForm [name="mobile"]').value = '13900139000';
            document.querySelector('#updateAddressForm [name="province"]').value = '浙江省';
            document.querySelector('#updateAddressForm [name="city"]').value = '杭州市';
            document.querySelector('#updateAddressForm [name="district"]').value = '滨江区';
            document.querySelector('#updateAddressForm [name="detail"]').value = '江南大道 456 号';
            document.querySelector('#updateAddressForm [name="is_default"]').checked = true;
            
            // 删除地址表单默认值
            document.querySelector('#deleteAddressForm [name="address_id"]').value = '1';
        }
        
        // 页面加载完成后设置默认值
        document.addEventListener('DOMContentLoaded', setDefaultValues);
        
        // 登录
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                username: formData.get('username'),
                password: formData.get('password')
            };
            
            const result = await sendRequest(`${API_BASE_URL}/login`, 'POST', data, false);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
            
            if (result.code === 0) {
                setToken(result.data.token, result.data.tokenExpired);
            }
        });
        
        // 注册
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                username: formData.get('username'),
                password: formData.get('password'),
                email: formData.get('email'),
                mobile: formData.get('mobile')
            };
            
            const result = await sendRequest(`${API_BASE_URL}/register`, 'POST', data, false);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
            
            if (result.code === 0) {
                setToken(result.data.token, result.data.tokenExpired);
            }
        });
        
        // 获取用户信息
        document.getElementById('getUserInfoBtn').addEventListener('click', async () => {
            const result = await sendRequest(`${API_BASE_URL}/user/info`, 'GET');
            document.getElementById('getUserInfoBtn').nextElementSibling.querySelector('pre').textContent = 
                JSON.stringify(result, null, 2);
        });
        
        // 更新用户信息
        document.getElementById('updateUserForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                gender: formData.get('gender'),
                age: formData.get('age') ? parseInt(formData.get('age')) : null,
                height: formData.get('height') ? parseInt(formData.get('height')) : null,
                weight: formData.get('weight') ? parseInt(formData.get('weight')) : null,
                is_pregnant: formData.get('is_pregnant') === 'on',
                is_preparing_pregnancy: formData.get('is_preparing_pregnancy') === 'on'
            };
            
            const result = await sendRequest(`${API_BASE_URL}/user/update`, 'POST', data);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 获取商品列表
        document.getElementById('goodsListForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const params = new URLSearchParams({
                category_id: formData.get('category_id'),
                page: formData.get('page'),
                page_size: formData.get('page_size')
            });
            
            const result = await sendRequest(`${API_BASE_URL}/goods/list?${params}`, 'GET');
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 获取商品详情
        document.getElementById('goodsDetailForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const goodsId = formData.get('goods_id');
            
            const result = await sendRequest(`${API_BASE_URL}/goods/detail/${goodsId}`, 'GET');
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 获取商品分类
        document.getElementById('getCategoriesBtn').addEventListener('click', async () => {
            const result = await sendRequest(`${API_BASE_URL}/goods/categories`, 'GET');
            document.getElementById('getCategoriesBtn').nextElementSibling.querySelector('pre').textContent = 
                JSON.stringify(result, null, 2);
        });
        
        // 添加商品到购物车
        document.getElementById('addToCartForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                goods_id: formData.get('goods_id'),
                quantity: parseInt(formData.get('quantity'))
            };
            
            const result = await sendRequest(`${API_BASE_URL}/cart/add`, 'POST', data);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 获取购物车列表
        document.getElementById('getCartListBtn').addEventListener('click', async () => {
            const result = await sendRequest(`${API_BASE_URL}/cart/list`, 'GET');
            document.getElementById('getCartListBtn').nextElementSibling.querySelector('pre').textContent = 
                JSON.stringify(result, null, 2);
        });
        
        // 更新购物车商品
        document.getElementById('updateCartForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                cart_item_id: formData.get('cart_item_id'),
                quantity: parseInt(formData.get('quantity'))
            };
            
            const result = await sendRequest(`${API_BASE_URL}/cart/update`, 'POST', data);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 删除购物车商品
        document.getElementById('deleteCartForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                cart_item_id: formData.get('cart_item_id')
            };
            
            const result = await sendRequest(`${API_BASE_URL}/cart/delete`, 'POST', data);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 创建订单
        document.getElementById('createOrderForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                address_id: formData.get('address_id'),
                cart_item_ids: formData.get('cart_item_ids').split(',').map(id => id.trim())
            };
            
            const result = await sendRequest(`${API_BASE_URL}/order/create`, 'POST', data);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 获取订单列表
        document.getElementById('orderListForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const params = new URLSearchParams({
                status: formData.get('status'),
                page: formData.get('page'),
                page_size: formData.get('page_size')
            });
            
            const result = await sendRequest(`${API_BASE_URL}/order/list?${params}`, 'GET');
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 获取订单详情
        document.getElementById('orderDetailForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const orderId = formData.get('order_id');
            
            const result = await sendRequest(`${API_BASE_URL}/order/detail/${orderId}`, 'GET');
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 添加地址
        document.getElementById('addAddressForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                name: formData.get('name'),
                mobile: formData.get('mobile'),
                province: formData.get('province'),
                city: formData.get('city'),
                district: formData.get('district'),
                detail: formData.get('detail'),
                is_default: formData.get('is_default') === 'on'
            };
            
            const result = await sendRequest(`${API_BASE_URL}/address/add`, 'POST', data);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 获取地址列表
        document.getElementById('getAddressListBtn').addEventListener('click', async () => {
            const result = await sendRequest(`${API_BASE_URL}/address/list`, 'GET');
            document.getElementById('getAddressListBtn').nextElementSibling.querySelector('pre').textContent = 
                JSON.stringify(result, null, 2);
        });
        
        // 更新地址
        document.getElementById('updateAddressForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                address_id: formData.get('address_id'),
                name: formData.get('name'),
                mobile: formData.get('mobile'),
                province: formData.get('province'),
                city: formData.get('city'),
                district: formData.get('district'),
                detail: formData.get('detail'),
                is_default: formData.get('is_default') === 'on'
            };
            
            const result = await sendRequest(`${API_BASE_URL}/address/update`, 'POST', data);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
        
        // 删除地址
        document.getElementById('deleteAddressForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = {
                address_id: formData.get('address_id')
            };
            
            const result = await sendRequest(`${API_BASE_URL}/address/delete`, 'POST', data);
            e.target.nextElementSibling.querySelector('pre').textContent = JSON.stringify(result, null, 2);
        });
    </script>
</body>
</html> 
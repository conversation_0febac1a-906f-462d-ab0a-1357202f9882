<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mastea 后台管理系统 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        .login-form {
            width: 100%;
            max-width: 400px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .login-heading {
            text-align: center;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .alert {
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-form">
        <h2 class="login-heading">Mastea 后台管理系统</h2>
        <div class="alert alert-danger" id="alertError"></div>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" class="form-control" id="username" placeholder="请输入管理员用户名" required>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" class="form-control" id="password" placeholder="请输入密码" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">登录</button>
            <div class="mt-3 text-muted small text-center">
                默认管理员账号: admin / 密码: admin123
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已登录
            const token = localStorage.getItem('admin_token');
            if (token) {
                // 验证token有效性
                fetch('/api/v1/users/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0 || data.status === "success") {
                        // 已登录，跳转到后台首页
                        window.location.href = '/admin';
                    }
                })
                .catch(error => {
                    console.error('Error checking login:', error);
                });
            }
            
            // 登录表单提交
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                console.log("正在尝试登录，用户名:", username);
                
                fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username: username, password: password })
                })
                .then(response => {
                    console.log("登录响应状态:", response.status);
                    return response.json();
                })
                .then(data => {
                    console.log("登录响应数据:", data);
                    
                    // 检查响应中是否包含成功代码和token
                    if (data.code === 0 && data.data && data.data.token) {
                        // 登录成功，保存token
                        localStorage.setItem('admin_token', data.data.token);
                        console.log("登录成功，token已保存");
                        
                        // 直接跳转到后台首页，由后台页面进行权限验证
                        window.location.href = '/admin';
                    } else {
                        // 登录失败
                        const errorMessage = data.message || '登录失败，请检查用户名和密码';
                        console.log("登录失败:", errorMessage);
                        showError(errorMessage);
                    }
                })
                .catch(error => {
                    console.error('登录请求失败:', error);
                    showError('登录请求失败，请检查网络或联系管理员');
                });
            });
            
            // 显示错误信息
            function showError(message) {
                const alertElement = document.getElementById('alertError');
                alertElement.textContent = message;
                alertElement.style.display = 'block';
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    alertElement.style.display = 'none';
                }, 3000);
            }
        });
    </script>
</body>
</html> 
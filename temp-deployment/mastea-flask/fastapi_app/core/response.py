"""
统一响应格式工具模块
提供标准化的API响应格式，确保与前端期望一致
"""

from typing import Any, Optional, Dict, Union
from fastapi import HTTPException
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)

class APIResponse:
    """
    统一的API响应格式类
    采用k_api的响应格式标准，确保前端兼容性
    """
    
    @staticmethod
    def success(
        data: Any = None, 
        message: str = "操作成功", 
        code: int = 200
    ) -> Dict[str, Any]:
        """
        成功响应格式
        
        Args:
            data: 响应数据
            message: 响应消息
            code: 响应代码，默认200
            
        Returns:
            标准化的成功响应字典
        """
        return {
            "code": code,
            "success": True,
            "data": data,
            "message": message
        }
    
    @staticmethod
    def error(
        message: str = "操作失败", 
        code: int = 500, 
        data: Any = None
    ) -> Dict[str, Any]:
        """
        错误响应格式
        
        Args:
            message: 错误消息
            code: 错误代码
            data: 错误相关数据
            
        Returns:
            标准化的错误响应字典
        """
        return {
            "code": code,
            "success": False,
            "data": data,
            "message": message
        }
    
    @staticmethod
    def paginated(
        items: list,
        total: int,
        page: int = 1,
        page_size: int = 20,
        message: str = "获取数据成功"
    ) -> Dict[str, Any]:
        """
        分页响应格式
        
        Args:
            items: 数据列表
            total: 总数量
            page: 当前页码
            page_size: 每页大小
            message: 响应消息
            
        Returns:
            标准化的分页响应字典
        """
        return {
            "code": 200,
            "success": True,
            "data": {
                "items": items,
                "pagination": {
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size
                }
            },
            "message": message
        }

class ResponseHandler:
    """
    响应处理器，提供便捷的响应方法
    """
    
    @staticmethod
    def handle_success(data: Any = None, message: str = "操作成功") -> JSONResponse:
        """
        处理成功响应
        """
        response_data = APIResponse.success(data=data, message=message)
        return JSONResponse(content=response_data, status_code=200)
    
    @staticmethod
    def handle_error(
        message: str = "操作失败", 
        status_code: int = 500,
        error_code: int = None
    ) -> JSONResponse:
        """
        处理错误响应
        """
        # 如果没有指定错误代码，使用HTTP状态码
        if error_code is None:
            error_code = status_code
            
        response_data = APIResponse.error(message=message, code=error_code)
        return JSONResponse(content=response_data, status_code=status_code)
    
    @staticmethod
    def handle_validation_error(detail: str) -> JSONResponse:
        """
        处理验证错误
        """
        return ResponseHandler.handle_error(
            message=f"参数验证失败: {detail}",
            status_code=400,
            error_code=400
        )
    
    @staticmethod
    def handle_not_found(resource: str = "资源") -> JSONResponse:
        """
        处理资源不存在错误
        """
        return ResponseHandler.handle_error(
            message=f"{resource}不存在",
            status_code=404,
            error_code=404
        )
    
    @staticmethod
    def handle_unauthorized(message: str = "未授权访问") -> JSONResponse:
        """
        处理未授权错误
        """
        return ResponseHandler.handle_error(
            message=message,
            status_code=401,
            error_code=401
        )
    
    @staticmethod
    def handle_forbidden(message: str = "禁止访问") -> JSONResponse:
        """
        处理禁止访问错误
        """
        return ResponseHandler.handle_error(
            message=message,
            status_code=403,
            error_code=403
        )

def create_response(
    success: bool = True,
    data: Any = None,
    message: str = None,
    code: int = None
) -> Dict[str, Any]:
    """
    创建响应的便捷函数
    
    Args:
        success: 是否成功
        data: 响应数据
        message: 响应消息
        code: 响应代码
        
    Returns:
        标准化响应字典
    """
    if success:
        return APIResponse.success(
            data=data,
            message=message or "操作成功",
            code=code or 200
        )
    else:
        return APIResponse.error(
            message=message or "操作失败",
            code=code or 500,
            data=data
        )

def handle_exception(e: Exception, default_message: str = "服务器内部错误") -> Dict[str, Any]:
    """
    统一异常处理
    
    Args:
        e: 异常对象
        default_message: 默认错误消息
        
    Returns:
        错误响应字典
    """
    if isinstance(e, HTTPException):
        return APIResponse.error(
            message=e.detail,
            code=e.status_code
        )
    elif isinstance(e, ValueError):
        return APIResponse.error(
            message=str(e),
            code=400
        )
    else:
        logger.error(f"未处理的异常: {type(e).__name__}: {str(e)}")
        return APIResponse.error(
            message=default_message,
            code=500
        )

# 兼容性支持：提供Flask风格的响应格式
def flask_style_response(
    data: Any = None,
    message: str = "success",
    code: int = 0
) -> Dict[str, Any]:
    """
    Flask风格的响应格式（用于向后兼容）
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 响应代码（0表示成功）
        
    Returns:
        Flask风格的响应字典
    """
    return {
        "code": code,
        "message": message,
        "data": data
    }

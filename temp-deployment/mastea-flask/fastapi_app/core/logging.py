import logging
import sys
import os
from logging.handlers import RotatingFileHandler

def setup_logging(log_level="INFO"):
    """设置日志配置"""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_level = getattr(logging, log_level)
    
    # 创建日志目录
    log_dir = "logs"
    try:
        os.makedirs(log_dir, exist_ok=True)
    except (OSError, PermissionError):
        # 如果无法创建日志目录，使用临时目录
        log_dir = "/tmp"
    
    # 设置根日志记录器
    handlers = [logging.StreamHandler(sys.stdout)]

    # 尝试添加文件处理器
    try:
        file_handler = RotatingFileHandler(
            os.path.join(log_dir, "app.log"),
            maxBytes=10_000_000,  # 10 MB
            backupCount=5,
            encoding="utf-8"
        )
        handlers.append(file_handler)
    except (OSError, PermissionError):
        # 如果无法创建文件日志，只使用控制台日志
        print("Warning: Cannot create log file, using console logging only")

    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=handlers
    )
    
    # 为SQLAlchemy设置更详细的日志记录
    logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO)
    
    # 设置FastAPI日志
    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_access = logging.getLogger("uvicorn.access")
    
    uvicorn_logger.setLevel(log_level)
    uvicorn_access.setLevel(log_level)
    
    # 设置自定义应用日志
    app_logger = logging.getLogger("fastapi_app")
    app_logger.setLevel(log_level)
    
    return logging.getLogger(__name__) 
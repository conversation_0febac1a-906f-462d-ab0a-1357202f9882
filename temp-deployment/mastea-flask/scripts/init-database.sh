#!/bin/bash
# 数据库初始化脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 使用说明
usage() {
    echo "用法: $0 <host> <port> <user> <password> <database>"
    echo "示例: $0 localhost 5432 mastea wyh12257410 mastea_prod"
    echo ""
    echo "参数说明:"
    echo "  host     - 数据库主机地址"
    echo "  port     - 数据库端口"
    echo "  user     - 数据库用户名"
    echo "  password - 数据库密码"
    echo "  database - 数据库名称"
    exit 1
}

# 检查参数
if [ $# -ne 5 ]; then
    log_error "参数数量不正确"
    usage
fi

# 获取参数
DB_HOST=$1
DB_PORT=$2
DB_USER=$3
DB_PASSWORD=$4
DB_NAME=$5

# 检查PostgreSQL客户端
check_psql() {
    if ! command -v psql &> /dev/null; then
        log_error "psql客户端未安装"
        log_info "请安装PostgreSQL客户端："
        log_info "  Ubuntu/Debian: sudo apt-get install postgresql-client"
        log_info "  CentOS/RHEL: sudo yum install postgresql"
        log_info "  macOS: brew install postgresql"
        exit 1
    fi
    log_success "PostgreSQL客户端检查通过"
}

# 测试数据库连接
test_connection() {
    log_info "测试数据库连接..."
    
    export PGPASSWORD=$DB_PASSWORD
    
    if psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "SELECT 1;" &>/dev/null; then
        log_success "数据库连接成功"
    else
        log_error "数据库连接失败"
        log_error "请检查数据库服务是否运行，以及连接参数是否正确"
        exit 1
    fi
}

# 创建数据库
create_database() {
    log_info "创建数据库 $DB_NAME..."
    
    export PGPASSWORD=$DB_PASSWORD
    
    # 检查数据库是否已存在
    if psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
        log_warning "数据库 $DB_NAME 已存在"
    else
        # 创建数据库
        psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME;"
        if [ $? -eq 0 ]; then
            log_success "数据库 $DB_NAME 创建成功"
        else
            log_error "数据库创建失败"
            exit 1
        fi
    fi
}

# 设置数据库配置
configure_database() {
    log_info "配置数据库设置..."
    
    export PGPASSWORD=$DB_PASSWORD
    
    # 设置时区
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "ALTER DATABASE $DB_NAME SET timezone TO 'Asia/Shanghai';"
    
    # 创建扩展（如果需要）
    # psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";"
    
    log_success "数据库配置完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 设置环境变量
    export DATABASE_URL="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
    
    # 检查是否在Docker容器中
    if [ -f "/.dockerenv" ]; then
        # 在容器中运行
        alembic upgrade head
    else
        # 在宿主机上运行
        if command -v alembic &> /dev/null; then
            alembic upgrade head
        else
            log_warning "Alembic未安装，跳过数据库迁移"
            log_info "请手动运行: alembic upgrade head"
        fi
    fi
    
    if [ $? -eq 0 ]; then
        log_success "数据库迁移完成"
    else
        log_warning "数据库迁移可能失败，请检查日志"
    fi
}

# 初始化测试数据
init_test_data() {
    log_info "初始化测试数据..."
    
    # 设置环境变量
    export DATABASE_URL="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
    
    # 检查是否在Docker容器中
    if [ -f "/.dockerenv" ]; then
        # 在容器中运行
        python -m fastapi_app.initial_data
    else
        # 在宿主机上运行
        if command -v python &> /dev/null; then
            python -m fastapi_app.initial_data
        else
            log_warning "Python未找到，跳过测试数据初始化"
        fi
    fi
    
    if [ $? -eq 0 ]; then
        log_success "测试数据初始化完成"
    else
        log_warning "测试数据初始化可能失败"
    fi
}

# 验证数据库
verify_database() {
    log_info "验证数据库..."
    
    export PGPASSWORD=$DB_PASSWORD
    
    # 检查表是否存在
    table_count=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
    
    if [ "$table_count" -gt 0 ]; then
        log_success "数据库验证成功，共有 $table_count 个表"
    else
        log_warning "数据库中没有表，可能需要运行迁移"
    fi
}

# 主函数
main() {
    log_info "开始数据库初始化..."
    log_info "目标数据库: $DB_HOST:$DB_PORT/$DB_NAME"
    
    # 检查PostgreSQL客户端
    check_psql
    
    # 测试连接
    test_connection
    
    # 创建数据库
    create_database
    
    # 配置数据库
    configure_database
    
    # 运行迁移
    run_migrations
    
    # 初始化测试数据
    init_test_data
    
    # 验证数据库
    verify_database
    
    log_success "数据库初始化完成！"
    log_info "数据库连接字符串: postgresql://$DB_USER:***@$DB_HOST:$DB_PORT/$DB_NAME"
}

# 执行主函数
main "$@"

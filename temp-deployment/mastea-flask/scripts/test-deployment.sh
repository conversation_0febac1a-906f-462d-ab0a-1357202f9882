#!/bin/bash
# Ma<PERSON><PERSON>k 部署测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试API端点
test_api_endpoint() {
    local endpoint=$1
    local description=$2
    local expected_status=${3:-200}
    
    log_info "测试 $description..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json "http://localhost:8001$endpoint")
    status_code=${response: -3}
    
    if [ "$status_code" -eq "$expected_status" ]; then
        log_success "$description 测试通过 (状态码: $status_code)"
        return 0
    else
        log_error "$description 测试失败 (状态码: $status_code)"
        if [ -f /tmp/response.json ]; then
            echo "响应内容:"
            cat /tmp/response.json
            echo ""
        fi
        return 1
    fi
}

# 测试数据库连接
test_database() {
    log_info "测试数据库连接..."
    
    if docker-compose exec -T postgres pg_isready -U mastea -d mastea_prod &>/dev/null; then
        log_success "数据库连接测试通过"
        return 0
    else
        log_error "数据库连接测试失败"
        return 1
    fi
}

# 测试容器状态
test_containers() {
    log_info "检查容器状态..."
    
    # 检查所有容器是否运行
    containers=("mastea_postgres" "mastea_fastapi" "mastea_redis")
    all_running=true
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$container"; then
            log_success "容器 $container 正在运行"
        else
            log_error "容器 $container 未运行"
            all_running=false
        fi
    done
    
    if [ "$all_running" = true ]; then
        return 0
    else
        return 1
    fi
}

# 测试健康检查
test_health_checks() {
    log_info "测试应用健康检查..."
    
    # 等待应用完全启动
    sleep 10
    
    # 测试健康检查端点
    if test_api_endpoint "/api/v1/health" "健康检查端点"; then
        # 检查响应内容
        if grep -q '"status":"healthy"' /tmp/response.json; then
            log_success "应用健康状态正常"
            return 0
        else
            log_error "应用健康状态异常"
            return 1
        fi
    else
        return 1
    fi
}

# 测试主要API端点
test_main_apis() {
    log_info "测试主要API端点..."
    
    local failed=0
    
    # 测试根路径
    test_api_endpoint "/" "根路径" || ((failed++))
    
    # 测试API文档
    test_api_endpoint "/api/v1/docs" "API文档" || ((failed++))
    
    # 测试OpenAPI规范
    test_api_endpoint "/api/v1/openapi.json" "OpenAPI规范" || ((failed++))
    
    # 测试微信相关端点（可能需要认证）
    test_api_endpoint "/api/v1/wechat/test-token" "微信测试令牌" || ((failed++))
    
    if [ $failed -eq 0 ]; then
        log_success "所有API端点测试通过"
        return 0
    else
        log_error "$failed 个API端点测试失败"
        return 1
    fi
}

# 测试静态文件服务
test_static_files() {
    log_info "测试静态文件服务..."
    
    # 测试dashboard页面
    if test_api_endpoint "/dashboard_enhanced" "管理界面"; then
        log_success "静态文件服务正常"
        return 0
    else
        log_warning "静态文件服务可能有问题"
        return 1
    fi
}

# 测试数据库表结构
test_database_schema() {
    log_info "测试数据库表结构..."
    
    # 检查主要表是否存在
    tables=("users" "goods" "orders" "cart_items")
    
    for table in "${tables[@]}"; do
        if docker-compose exec -T postgres psql -U mastea -d mastea_prod -c "SELECT 1 FROM $table LIMIT 1;" &>/dev/null; then
            log_success "表 $table 存在且可访问"
        else
            log_warning "表 $table 不存在或不可访问"
        fi
    done
}

# 性能测试
test_performance() {
    log_info "进行基础性能测试..."
    
    # 测试响应时间
    start_time=$(date +%s%N)
    curl -s "http://localhost:8001/api/v1/health" > /dev/null
    end_time=$(date +%s%N)
    
    response_time=$(( (end_time - start_time) / 1000000 )) # 转换为毫秒
    
    if [ $response_time -lt 1000 ]; then
        log_success "响应时间正常: ${response_time}ms"
    else
        log_warning "响应时间较慢: ${response_time}ms"
    fi
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    cat > test-report.txt << EOF
Mastea Flask Docker部署测试报告
生成时间: $(date)

=== 容器状态 ===
$(docker-compose ps)

=== 服务健康状态 ===
$(curl -s http://localhost:8001/api/v1/health | python -m json.tool 2>/dev/null || echo "健康检查失败")

=== 数据库状态 ===
$(docker-compose exec -T postgres pg_isready -U mastea -d mastea_prod)

=== 资源使用情况 ===
$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}")

=== 日志摘要 ===
最近的应用日志:
$(docker-compose logs --tail=20 fastapi_app)
EOF
    
    log_success "测试报告已生成: test-report.txt"
}

# 主函数
main() {
    log_info "开始Mastea Flask部署测试..."
    
    local failed_tests=0
    
    # 测试容器状态
    test_containers || ((failed_tests++))
    
    # 测试数据库
    test_database || ((failed_tests++))
    
    # 测试健康检查
    test_health_checks || ((failed_tests++))
    
    # 测试主要API
    test_main_apis || ((failed_tests++))
    
    # 测试静态文件
    test_static_files || ((failed_tests++))
    
    # 测试数据库表结构
    test_database_schema
    
    # 性能测试
    test_performance
    
    # 生成报告
    generate_report
    
    echo ""
    if [ $failed_tests -eq 0 ]; then
        log_success "所有测试通过！部署成功！"
        log_info "访问地址:"
        log_info "  应用首页: http://localhost:8001"
        log_info "  API文档: http://localhost:8001/api/v1/docs"
        log_info "  管理界面: http://localhost:8001/dashboard_enhanced"
        return 0
    else
        log_error "$failed_tests 个测试失败，请检查部署"
        log_info "查看详细日志: docker-compose logs"
        return 1
    fi
}

# 执行主函数
main "$@"

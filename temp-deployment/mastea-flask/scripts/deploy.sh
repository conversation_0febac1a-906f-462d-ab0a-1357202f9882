#!/bin/bash
# Ma<PERSON>a Flask Docker部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查docker-compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    # 检查必要文件
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi
    
    if [ ! -f "Dockerfile" ]; then
        log_error "Dockerfile文件不存在"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 创建必要目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p uploads
    mkdir -p logs
    mkdir -p init-scripts
    
    log_success "目录创建完成"
}

# 初始化数据库脚本
create_init_script() {
    log_info "创建数据库初始化脚本..."
    
    cat > init-scripts/01-init.sql << 'EOF'
-- 创建数据库（如果不存在）
-- PostgreSQL会自动创建POSTGRES_DB指定的数据库

-- 设置数据库编码
ALTER DATABASE mastea_prod SET timezone TO 'Asia/Shanghai';

-- 创建扩展（如果需要）
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
EOF
    
    log_success "数据库初始化脚本创建完成"
}

# 启动服务
start_services() {
    log_info "启动Docker服务..."
    
    # 停止现有服务
    docker-compose down --remove-orphans 2>/dev/null || true
    
    # 启动服务
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        log_success "服务启动成功"
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待PostgreSQL就绪
    log_info "等待PostgreSQL数据库启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose exec -T postgres pg_isready -U mastea -d mastea_prod &>/dev/null; then
            log_success "PostgreSQL数据库已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "PostgreSQL数据库启动超时"
        exit 1
    fi
    
    # 等待FastAPI应用就绪
    log_info "等待FastAPI应用启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8001/api/v1/health &>/dev/null; then
            log_success "FastAPI应用已就绪"
            break
        fi
        sleep 3
        timeout=$((timeout-3))
    done
    
    if [ $timeout -le 0 ]; then
        log_warning "FastAPI应用健康检查超时，但可能仍在启动中"
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待一下确保数据库完全就绪
    sleep 5
    
    # 运行Alembic迁移
    docker-compose exec fastapi_app alembic upgrade head
    
    if [ $? -eq 0 ]; then
        log_success "数据库迁移完成"
    else
        log_warning "数据库迁移可能失败，请检查日志"
    fi
}

# 显示服务状态
show_status() {
    log_info "服务状态："
    docker-compose ps
    
    echo ""
    log_info "服务访问地址："
    log_info "  FastAPI应用: http://localhost:8001"
    log_info "  API文档: http://localhost:8001/api/v1/docs"
    log_info "  管理界面: http://localhost:8001/dashboard_enhanced"
    log_info "  健康检查: http://localhost:8001/api/v1/health"
    
    echo ""
    log_info "数据库连接信息："
    log_info "  主机: localhost"
    log_info "  端口: 5432"
    log_info "  数据库: mastea_prod"
    log_info "  用户: mastea"
    
    echo ""
    log_info "常用命令："
    log_info "  查看日志: docker-compose logs -f"
    log_info "  重启服务: docker-compose restart"
    log_info "  停止服务: docker-compose down"
}

# 主函数
main() {
    log_info "开始Mastea Flask Docker部署..."
    
    # 检查环境
    check_environment
    
    # 创建目录
    create_directories
    
    # 创建初始化脚本
    create_init_script
    
    # 启动服务
    start_services
    
    # 等待服务就绪
    wait_for_services
    
    # 运行数据库迁移
    run_migrations
    
    # 显示状态
    show_status
    
    log_success "Mastea Flask部署完成！"
}

# 执行主函数
main "$@"

#!/bin/bash
# Mastea Flask 服务器部署脚本
# 用于在远程服务器上部署应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认配置
SERVER_HOST="**************"
SERVER_USER="root"
SERVER_PASSWORD="Goat151477"
DEPLOY_DIR="/opt/mastea"
APP_NAME="mastea-flask"

# 使用说明
usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --host HOST       服务器地址 (默认: $SERVER_HOST)"
    echo "  -u, --user USER       SSH用户名 (默认: $SERVER_USER)"
    echo "  -d, --dir DIR         部署目录 (默认: $DEPLOY_DIR)"
    echo "  -t, --tar-file FILE   指定tar包文件"
    echo "  --help               显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置部署"
    echo "  $0 -h ************* -u ubuntu       # 指定服务器和用户"
    echo "  $0 -t mastea-deploy.tar.gz           # 使用指定的tar包"
    exit 1
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--host)
                SERVER_HOST="$2"
                shift 2
                ;;
            -u|--user)
                SERVER_USER="$2"
                shift 2
                ;;
            -d|--dir)
                DEPLOY_DIR="$2"
                shift 2
                ;;
            -t|--tar-file)
                TAR_FILE="$2"
                shift 2
                ;;
            --help)
                usage
                ;;
            *)
                log_error "未知参数: $1"
                usage
                ;;
        esac
    done
}

# 检查本地环境
check_local_environment() {
    log_info "检查本地环境..."
    
    # 检查必要工具
    for tool in sshpass rsync tar; do
        if ! command -v $tool &> /dev/null; then
            log_error "$tool 未安装"
            case $tool in
                sshpass)
                    log_info "安装命令: sudo apt-get install sshpass"
                    ;;
                rsync)
                    log_info "安装命令: sudo apt-get install rsync"
                    ;;
                tar)
                    log_info "tar 通常是系统自带的"
                    ;;
            esac
            exit 1
        fi
    done
    
    log_success "本地环境检查通过"
}

# 创建部署包
create_deployment_package() {
    log_info "创建部署包..."
    
    # 如果没有指定tar文件，则创建一个
    if [ -z "$TAR_FILE" ]; then
        TAR_FILE="mastea-deploy-$(date +%Y%m%d-%H%M%S).tar.gz"
    fi
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    PACKAGE_DIR="$TEMP_DIR/mastea-flask"
    
    mkdir -p "$PACKAGE_DIR"
    
    # 复制必要文件
    cp -r fastapi_app "$PACKAGE_DIR/"
    cp -r migrations "$PACKAGE_DIR/"
    cp -r alembic "$PACKAGE_DIR/"
    cp requirements.txt "$PACKAGE_DIR/"
    cp Dockerfile "$PACKAGE_DIR/"
    cp docker-compose.yml "$PACKAGE_DIR/"
    cp .env.docker "$PACKAGE_DIR/.env"
    cp -r scripts "$PACKAGE_DIR/"
    
    # 复制其他必要文件
    [ -f "alembic.ini" ] && cp alembic.ini "$PACKAGE_DIR/"
    [ -f "run_fastapi.py" ] && cp run_fastapi.py "$PACKAGE_DIR/"
    
    # 创建部署说明
    cat > "$PACKAGE_DIR/DEPLOY_README.md" << 'EOF'
# Mastea Flask 服务器部署说明

## 快速部署

1. 解压部署包
2. 运行部署脚本: `./scripts/deploy.sh`
3. 访问应用: http://服务器IP:8001

## 手动部署步骤

1. 安装Docker和Docker Compose
2. 启动服务: `docker-compose up -d`
3. 初始化数据库: `./scripts/init-database.sh localhost 5432 mastea wyh12257410 mastea_prod`

## 服务管理

- 查看状态: `docker-compose ps`
- 查看日志: `docker-compose logs -f`
- 重启服务: `docker-compose restart`
- 停止服务: `docker-compose down`
EOF
    
    # 创建tar包
    cd "$TEMP_DIR"
    tar -czf "$TAR_FILE" mastea-flask/
    mv "$TAR_FILE" "$OLDPWD/"
    
    # 清理临时目录
    rm -rf "$TEMP_DIR"
    
    log_success "部署包创建完成: $TAR_FILE"
}

# 测试服务器连接
test_server_connection() {
    log_info "测试服务器连接..."
    
    if sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$SERVER_USER@$SERVER_HOST" "echo 'Connection test successful'" &>/dev/null; then
        log_success "服务器连接成功"
    else
        log_error "服务器连接失败"
        log_error "请检查服务器地址、用户名和密码"
        exit 1
    fi
}

# 上传部署包
upload_package() {
    log_info "上传部署包到服务器..."
    
    # 创建部署目录
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "mkdir -p $DEPLOY_DIR"
    
    # 上传tar包
    sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no "$TAR_FILE" "$SERVER_USER@$SERVER_HOST:$DEPLOY_DIR/"
    
    if [ $? -eq 0 ]; then
        log_success "部署包上传成功"
    else
        log_error "部署包上传失败"
        exit 1
    fi
}

# 在服务器上部署
deploy_on_server() {
    log_info "在服务器上部署应用..."
    
    # 创建部署脚本
    cat > /tmp/server_deploy.sh << 'EOF'
#!/bin/bash
set -e

DEPLOY_DIR=$1
TAR_FILE=$2

cd $DEPLOY_DIR

# 停止现有服务
if [ -f "docker-compose.yml" ]; then
    docker-compose down --remove-orphans 2>/dev/null || true
fi

# 备份现有部署
if [ -d "mastea-flask" ]; then
    mv mastea-flask mastea-flask-backup-$(date +%Y%m%d-%H%M%S) 2>/dev/null || true
fi

# 解压新部署包
tar -xzf $TAR_FILE

# 进入应用目录
cd mastea-flask

# 设置脚本权限
chmod +x scripts/*.sh

# 安装Docker（如果未安装）
if ! command -v docker &> /dev/null; then
    echo "安装Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    systemctl start docker
    systemctl enable docker
fi

# 安装Docker Compose（如果未安装）
if ! command -v docker-compose &> /dev/null; then
    echo "安装Docker Compose..."
    curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

# 部署应用
echo "部署应用..."
./scripts/deploy.sh

echo "部署完成！"
echo "访问地址: http://$(curl -s ifconfig.me):8001"
EOF
    
    # 上传并执行部署脚本
    sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no /tmp/server_deploy.sh "$SERVER_USER@$SERVER_HOST:/tmp/"
    
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "chmod +x /tmp/server_deploy.sh && /tmp/server_deploy.sh $DEPLOY_DIR $(basename $TAR_FILE)"
    
    if [ $? -eq 0 ]; then
        log_success "服务器部署完成"
    else
        log_error "服务器部署失败"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 等待服务启动
    sleep 30
    
    # 测试健康检查
    if curl -f "http://$SERVER_HOST:8001/api/v1/health" &>/dev/null; then
        log_success "应用部署验证成功"
        log_info "访问地址: http://$SERVER_HOST:8001"
        log_info "API文档: http://$SERVER_HOST:8001/api/v1/docs"
        log_info "管理界面: http://$SERVER_HOST:8001/dashboard_enhanced"
    else
        log_warning "应用可能还在启动中，请稍后手动验证"
        log_info "验证命令: curl http://$SERVER_HOST:8001/api/v1/health"
    fi
}

# 清理本地文件
cleanup() {
    log_info "清理临时文件..."
    
    if [ -f "$TAR_FILE" ] && [[ "$TAR_FILE" == mastea-deploy-* ]]; then
        rm -f "$TAR_FILE"
        log_success "临时文件清理完成"
    fi
}

# 主函数
main() {
    log_info "开始Mastea Flask服务器部署..."
    
    # 解析参数
    parse_args "$@"
    
    log_info "部署配置:"
    log_info "  服务器: $SERVER_HOST"
    log_info "  用户: $SERVER_USER"
    log_info "  部署目录: $DEPLOY_DIR"
    
    # 检查本地环境
    check_local_environment
    
    # 创建部署包
    create_deployment_package
    
    # 测试服务器连接
    test_server_connection
    
    # 上传部署包
    upload_package
    
    # 在服务器上部署
    deploy_on_server
    
    # 验证部署
    verify_deployment
    
    # 清理
    cleanup
    
    log_success "Mastea Flask服务器部署完成！"
}

# 执行主函数
main "$@"

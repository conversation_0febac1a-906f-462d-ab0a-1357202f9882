"""Add Conversation model and link to ChatMessage

Revision ID: c766cb4375e4
Revises: f181d77a4acf
Create Date: 2025-05-02 16:06:12.949591

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c766cb4375e4'
down_revision = 'f181d77a4acf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('conversations',
    sa.Column('id', sa.String(length=32), nullable=False),
    sa.Column('user_id', sa.String(length=32), nullable=False),
    sa.Column('title', sa.String(length=128), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_conversations_user_id'), ['user_id'], unique=False)

    # favorites 表可能之前迁移不完整，这里保留其创建逻辑
    # 如果 favorites 表已存在且包含数据，此迁移也可能因外键约束失败，但先处理 chat_messages
    try:
        op.create_table('favorites',
        sa.Column('id', sa.String(length=32), nullable=False),
        sa.Column('user_id', sa.String(length=32), nullable=True),
        sa.Column('goods_id', sa.String(length=32), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['goods_id'], ['goods.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
        )
        with op.batch_alter_table('favorites', schema=None) as batch_op:
            batch_op.create_index(batch_op.f('ix_favorites_goods_id'), ['goods_id'], unique=False)
            batch_op.create_index(batch_op.f('ix_favorites_user_id'), ['user_id'], unique=False)
    except Exception as e:
        print(f"创建 favorites 表时可能出现错误（如果已存在则忽略）: {e}")
        # 在生产环境中可能需要更精细的错误处理或检查表是否存在

    # --- 清空 chat_messages 表 ---
    print("清空 chat_messages 表...")
    op.execute('DELETE FROM chat_messages;')
    print("chat_messages 表已清空。")
    # --- 结束添加的代码 ---

    with op.batch_alter_table('chat_messages', schema=None) as batch_op:
        # 现在添加列应该不会因为 NULL 值而出错
        batch_op.add_column(sa.Column('conversation_id', sa.String(length=32), nullable=False))
        batch_op.create_index(batch_op.f('ix_chat_messages_conversation_id'), ['conversation_id'], unique=False)
        # 外键约束名通常由 Alembic 自动生成，这里用 None 让它自动处理
        batch_op.create_foreign_key('fk_chat_messages_conversation_id', 'conversations', ['conversation_id'], ['id'], ondelete='CASCADE')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('chat_messages', schema=None) as batch_op:
        batch_op.drop_constraint('fk_chat_messages_conversation_id', type_='foreignkey') # 使用指定的外键名
        batch_op.drop_index(batch_op.f('ix_chat_messages_conversation_id'))
        batch_op.drop_column('conversation_id')

    # 降级时也处理 favorites
    with op.batch_alter_table('favorites', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_favorites_user_id'))
        batch_op.drop_index(batch_op.f('ix_favorites_goods_id'))
    op.drop_table('favorites')

    with op.batch_alter_table('conversations', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_conversations_user_id'))

    op.drop_table('conversations')
    # ### end Alembic commands ###

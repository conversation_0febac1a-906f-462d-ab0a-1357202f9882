# Docker环境配置文件
# 用于容器化部署的环境变量

# 数据库配置 - 容器环境
DATABASE_URL=*********************************************/mastea_prod
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=mastea
POSTGRES_PASSWORD=wyh12257410
POSTGRES_DB=mastea_prod
USE_POSTGRES=true

# 应用配置
ENVIRONMENT=production
SECRET_KEY=mastea-production-secret-key-2024
JWT_SECRET_KEY=mastea-jwt-production-secret-key-2024
PORT=8001

# 微信小程序配置
WECHAT_APP_ID=wxc8301173764205f1
WECHAT_APP_SECRET=d61b1e2c0191cd8e2491e4038e3eaa1a

# AI配置
SILICONFLOW_API_KEY=sk-tfgujfpskkcjhtnhfhbvegnrthmmyebrzodvizydvdjmzfnl
AI_API_BASE_URL=https://api.siliconflow.cn/v1
AI_MODEL_ID=deepseek-ai/DeepSeek-V3

# Redis配置（可选）
REDIS_URL=redis://redis:6379/0

# 日志配置
LOG_LEVEL=INFO

version: '3.8'

services:
  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: mastea_postgres
    environment:
      POSTGRES_USER: mastea
      POSTGRES_PASSWORD: wyh12257410
      POSTGRES_DB: mastea_prod
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - mastea_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mastea -d mastea_prod"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI应用服务
  fastapi_app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mastea_fastapi
    environment:
      # 数据库配置
      DATABASE_URL: "*********************************************/mastea_prod"
      POSTGRES_HOST: postgres
      POSTGRES_PORT: 5432
      POSTGRES_USER: mastea
      POSTGRES_PASSWORD: wyh12257410
      POSTGRES_DB: mastea_prod
      
      # 应用配置
      ENVIRONMENT: production
      SECRET_KEY: "mastea-production-secret-key-2024"
      JWT_SECRET_KEY: "mastea-jwt-production-secret-key-2024"
      
      # 微信小程序配置
      WECHAT_APP_ID: "wxc8301173764205f1"
      WECHAT_APP_SECRET: "d61b1e2c0191cd8e2491e4038e3eaa1a"
      
      # AI配置
      SILICONFLOW_API_KEY: "sk-tfgujfpskkcjhtnhfhbvegnrthmmyebrzodvizydvdjmzfnl"
      AI_API_BASE_URL: "https://api.siliconflow.cn/v1"
      AI_MODEL_ID: "deepseek-ai/DeepSeek-V3"
      
      # 其他配置
      USE_POSTGRES: "true"
      PORT: 8001
    ports:
      - "8001:8001"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - mastea_network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:
    driver: local

networks:
  mastea_network:
    driver: bridge

#!/bin/bash
# Mastea Flask Linux部署包创建脚本
# 在Windows Docker环境中创建Linux服务器部署包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
PACKAGE_NAME="mastea-deployment-$(date +%Y%m%d-%H%M%S)"
TEMP_DIR="/tmp/${PACKAGE_NAME}"
INCLUDE_IMAGES=${1:-false}

log_info "=== Mastea Flask Linux部署包创建工具 ==="
log_info "目标: Linux服务器部署"
log_info "包名: ${PACKAGE_NAME}"

# 创建临时目录
log_info "创建临时目录..."
mkdir -p "${TEMP_DIR}/mastea-flask"
PACKAGE_DIR="${TEMP_DIR}/mastea-flask"

# 复制必要文件
log_info "复制应用文件..."
FILES_TO_COPY=(
    "fastapi_app"
    "migrations"
    "alembic"
    "requirements.txt"
    "Dockerfile"
    "docker-compose.yml"
    ".env.docker"
    "scripts"
    "run_fastapi.py"
)

for file in "${FILES_TO_COPY[@]}"; do
    if [ -e "$file" ]; then
        log_info "  复制: $file"
        cp -r "$file" "$PACKAGE_DIR/"
    else
        log_warning "文件不存在: $file"
    fi
done

# 复制alembic.ini到根目录
if [ -f "migrations/alembic.ini" ]; then
    cp "migrations/alembic.ini" "$PACKAGE_DIR/alembic.ini"
    log_info "  复制: alembic.ini"
fi

# 创建生产环境配置
log_info "创建生产环境配置..."
cp ".env.docker" "$PACKAGE_DIR/.env"

# 导出Docker镜像（可选）
if [ "$INCLUDE_IMAGES" = "true" ]; then
    log_info "导出Docker镜像..."
    docker save mastea-fastapi:latest -o "$PACKAGE_DIR/mastea-fastapi-image.tar"
    if [ $? -eq 0 ]; then
        log_success "镜像已导出: mastea-fastapi-image.tar"
    else
        log_warning "镜像导出失败"
    fi
fi

# 创建Linux服务器部署说明
log_info "创建部署说明..."
cat > "$PACKAGE_DIR/README.md" << 'EOF'
# Mastea Flask Linux服务器部署包

## 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+, 或其他主流发行版)
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少5GB可用空间
- **网络**: 需要访问互联网下载镜像

## 快速部署

### 1. 上传部署包到服务器

```bash
# 从本地上传到服务器
scp mastea-deployment-*.tar.gz root@**************:/opt/

# 在服务器上解压
ssh root@**************
cd /opt
tar -xzf mastea-deployment-*.tar.gz
cd mastea-flask
```

### 2. 安装Docker环境（如果未安装）

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y docker.io docker-compose

# CentOS/RHEL
sudo yum install -y docker docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

### 3. 部署应用

```bash
# 方式1: 使用一键部署脚本（推荐）
chmod +x quick-deploy.sh
sudo ./quick-deploy.sh

# 方式2: 手动部署
sudo docker-compose up -d
sleep 30
sudo docker-compose exec -T fastapi_app bash -c "cd migrations && alembic upgrade heads"
```

### 4. 验证部署

```bash
# 检查容器状态
sudo docker-compose ps

# 检查应用健康状态
curl http://localhost:8001/api/v1/health

# 查看日志
sudo docker-compose logs -f fastapi_app
```

## 访问地址

部署成功后，可通过以下地址访问：

- **应用首页**: http://**************:8001
- **API文档**: http://**************:8001/api/v1/docs
- **管理界面**: http://**************:8001/dashboard_enhanced
- **健康检查**: http://**************:8001/api/v1/health

## 服务管理

```bash
# 查看服务状态
sudo docker-compose ps

# 查看实时日志
sudo docker-compose logs -f

# 重启服务
sudo docker-compose restart

# 停止服务
sudo docker-compose down

# 更新应用
sudo docker-compose pull
sudo docker-compose up -d
```

## 配置说明

### 环境变量配置 (.env)

主要配置项：
- `DATABASE_URL`: 数据库连接字符串
- `SECRET_KEY`: 应用密钥
- `WECHAT_APP_ID`: 微信小程序ID
- `AI_API_KEY`: AI服务API密钥

### 端口配置

默认端口映射：
- FastAPI应用: 8001
- PostgreSQL: 5432

如需修改端口，编辑 `docker-compose.yml` 文件。

## 数据备份

```bash
# 备份数据库
sudo docker-compose exec postgres pg_dump -U mastea mastea_prod > backup_$(date +%Y%m%d).sql

# 恢复数据库
sudo docker-compose exec -T postgres psql -U mastea mastea_prod < backup_20240101.sql
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   sudo netstat -tulpn | grep :8001
   # 修改docker-compose.yml中的端口映射
   ```

2. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   sudo docker stats
   ```

3. **磁盘空间不足**
   ```bash
   # 清理Docker资源
   sudo docker system prune -a
   ```

4. **容器启动失败**
   ```bash
   # 查看详细日志
   sudo docker-compose logs fastapi_app
   ```

## 安全配置

### 防火墙设置

```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 8001/tcp
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=8001/tcp
sudo firewall-cmd --reload
```

### SSL/HTTPS配置

建议使用Nginx反向代理配置SSL：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 监控和维护

### 日志管理

```bash
# 查看应用日志
sudo docker-compose logs --tail=100 fastapi_app

# 查看数据库日志
sudo docker-compose logs --tail=100 postgres

# 设置日志轮转
sudo docker-compose logs --follow --tail=0 > /var/log/mastea.log &
```

### 性能监控

```bash
# 查看容器资源使用
sudo docker stats

# 查看系统资源
htop
df -h
```

## 技术支持

- **文档**: 查看项目DEPLOYMENT.md
- **问题反馈**: 联系开发团队
- **服务器**: **************
- **SSH**: root@**************

---
**注意**: 本部署包专为Linux服务器环境设计，已在Ubuntu 20.04和CentOS 8上测试验证。
EOF

# 创建Linux一键部署脚本
log_info "创建一键部署脚本..."
cat > "$PACKAGE_DIR/quick-deploy.sh" << 'EOF'
#!/bin/bash
# Mastea Flask Linux服务器一键部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "=== Mastea Flask Linux服务器一键部署 ==="

# 检查运行权限
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root权限运行此脚本"
    log_info "使用: sudo $0"
    exit 1
fi

# 检查Docker
log_info "检查Docker环境..."
if ! command -v docker &> /dev/null; then
    log_warning "Docker未安装，正在安装..."
    
    # 检测系统类型
    if [ -f /etc/debian_version ]; then
        # Ubuntu/Debian
        apt update
        apt install -y docker.io docker-compose
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        yum install -y docker docker-compose
    else
        log_error "不支持的系统类型，请手动安装Docker"
        exit 1
    fi
    
    systemctl start docker
    systemctl enable docker
    log_success "Docker安装完成"
else
    log_success "Docker已安装"
fi

# 检查Docker Compose
if ! command -v docker-compose &> /dev/null; then
    log_warning "Docker Compose未安装，正在安装..."
    curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    log_success "Docker Compose安装完成"
else
    log_success "Docker Compose已安装"
fi

# 加载Docker镜像（如果存在）
if [ -f "mastea-fastapi-image.tar" ]; then
    log_info "加载Docker镜像..."
    docker load -i mastea-fastapi-image.tar
    log_success "Docker镜像加载完成"
fi

# 停止现有服务
log_info "停止现有服务..."
docker-compose down --remove-orphans 2>/dev/null || true

# 启动服务
log_info "启动Docker服务..."
docker-compose up -d

# 等待服务就绪
log_info "等待服务启动..."
sleep 30

# 检查服务状态
log_info "检查服务状态..."
if ! docker-compose ps | grep -q "Up"; then
    log_error "服务启动失败"
    docker-compose logs
    exit 1
fi

# 运行数据库迁移
log_info "运行数据库迁移..."
docker-compose exec -T fastapi_app bash -c "cd migrations && alembic upgrade heads"

if [ $? -eq 0 ]; then
    log_success "数据库迁移完成"
else
    log_warning "数据库迁移可能失败，请检查日志"
fi

# 配置防火墙
log_info "配置防火墙..."
if command -v ufw &> /dev/null; then
    ufw allow 8001/tcp 2>/dev/null || true
elif command -v firewall-cmd &> /dev/null; then
    firewall-cmd --permanent --add-port=8001/tcp 2>/dev/null || true
    firewall-cmd --reload 2>/dev/null || true
fi

# 显示部署结果
log_success "=== 部署完成！ ==="
echo ""
log_info "服务状态:"
docker-compose ps

echo ""
log_info "访问地址:"
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || hostname -I | awk '{print $1}')
log_success "  应用首页: http://${SERVER_IP}:8001"
log_success "  API文档: http://${SERVER_IP}:8001/api/v1/docs"
log_success "  管理界面: http://${SERVER_IP}:8001/dashboard_enhanced"
log_success "  健康检查: http://${SERVER_IP}:8001/api/v1/health"

echo ""
log_info "常用命令:"
log_info "  查看日志: docker-compose logs -f"
log_info "  重启服务: docker-compose restart"
log_info "  停止服务: docker-compose down"

echo ""
log_info "验证部署:"
sleep 5
if curl -f http://localhost:8001/api/v1/health &>/dev/null; then
    log_success "应用健康检查通过！"
else
    log_warning "应用可能还在启动中，请稍后手动验证"
fi
EOF

# 设置脚本权限
chmod +x "$PACKAGE_DIR/quick-deploy.sh"

# 创建tar包
log_info "创建部署包..."
cd "$TEMP_DIR"
tar -czf "${PACKAGE_NAME}.tar.gz" mastea-flask/

# 移动到当前目录
mv "${PACKAGE_NAME}.tar.gz" /app/
cd /app

# 清理临时目录
rm -rf "$TEMP_DIR"

# 显示结果
PACKAGE_SIZE=$(du -h "${PACKAGE_NAME}.tar.gz" | cut -f1)
log_success "=== Linux部署包创建完成 ==="
log_success "文件: ${PACKAGE_NAME}.tar.gz"
log_success "大小: ${PACKAGE_SIZE}"
echo ""
log_info "部署到Linux服务器的步骤:"
log_info "1. 上传: scp ${PACKAGE_NAME}.tar.gz root@**************:/opt/"
log_info "2. 解压: tar -xzf ${PACKAGE_NAME}.tar.gz"
log_info "3. 部署: cd mastea-flask && sudo ./quick-deploy.sh"
echo ""
log_info "目标服务器: **************"
log_info "SSH用户: root"

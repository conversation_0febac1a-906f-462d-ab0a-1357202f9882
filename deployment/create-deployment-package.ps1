# Mastea Flask 部署包创建脚本
# PowerShell版本

param(
    [string]$OutputPath = "mastea-deployment-$(Get-Date -Format 'yyyyMMdd-HHmmss').tar.gz",
    [switch]$IncludeImages = $false
)

Write-Host "=== Mastea Flask 部署包创建工具 ===" -ForegroundColor Green

# 检查必要工具
Write-Host "检查必要工具..." -ForegroundColor Blue
if (!(Get-Command tar -ErrorAction SilentlyContinue)) {
    Write-Error "tar 命令未找到，请确保已安装"
    exit 1
}

if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Error "Docker 未安装或未在PATH中"
    exit 1
}

# 创建临时目录
$TempDir = New-TemporaryFile | ForEach-Object { Remove-Item $_; New-Item -ItemType Directory -Path $_ }
$PackageDir = Join-Path $TempDir "mastea-flask"
New-Item -ItemType Directory -Path $PackageDir -Force | Out-Null

Write-Host "创建部署包目录: $PackageDir" -ForegroundColor Blue

# 复制必要文件
Write-Host "复制应用文件..." -ForegroundColor Blue
$FilesToCopy = @(
    "fastapi_app",
    "migrations", 
    "alembic",
    "requirements.txt",
    "Dockerfile",
    "docker-compose.yml",
    ".env.docker",
    "scripts",
    "run_fastapi.py"
)

foreach ($file in $FilesToCopy) {
    if (Test-Path $file) {
        Write-Host "  复制: $file" -ForegroundColor Gray
        Copy-Item -Path $file -Destination $PackageDir -Recurse -Force
    } else {
        Write-Warning "文件不存在: $file"
    }
}

# 复制alembic.ini到根目录
if (Test-Path "migrations\alembic.ini") {
    Copy-Item -Path "migrations\alembic.ini" -Destination "$PackageDir\alembic.ini" -Force
    Write-Host "  复制: alembic.ini" -ForegroundColor Gray
}

# 创建.env文件
Write-Host "创建生产环境配置..." -ForegroundColor Blue
Copy-Item -Path ".env.docker" -Destination "$PackageDir\.env" -Force

# 导出Docker镜像（可选）
if ($IncludeImages) {
    Write-Host "导出Docker镜像..." -ForegroundColor Blue
    $ImagePath = Join-Path $PackageDir "mastea-fastapi-image.tar"
    docker save mastea-fastapi:latest -o $ImagePath
    if ($LASTEXITCODE -eq 0) {
        Write-Host "  镜像已导出: mastea-fastapi-image.tar" -ForegroundColor Gray
    } else {
        Write-Warning "镜像导出失败"
    }
}

# 创建部署说明
Write-Host "创建部署说明..." -ForegroundColor Blue
@"
# Mastea Flask 部署包

## 快速部署

1. 解压部署包:
   ```bash
   tar -xzf $OutputPath
   cd mastea-flask
   ```

2. 启动服务:
   ```bash
   # 方式1: 使用部署脚本
   chmod +x scripts/*.sh
   ./scripts/deploy.sh
   
   # 方式2: 手动启动
   docker-compose up -d
   ```

3. 运行数据库迁移:
   ```bash
   docker-compose exec fastapi_app bash -c "cd migrations && alembic upgrade heads"
   ```

4. 访问应用:
   - 应用首页: http://服务器IP:8001
   - API文档: http://服务器IP:8001/api/v1/docs
   - 管理界面: http://服务器IP:8001/dashboard_enhanced

## 服务管理

- 查看状态: `docker-compose ps`
- 查看日志: `docker-compose logs -f`
- 重启服务: `docker-compose restart`
- 停止服务: `docker-compose down`

## 环境要求

- Docker 20.10+
- Docker Compose 1.29+
- 2GB+ 内存
- 5GB+ 磁盘空间

## 配置说明

主要配置文件:
- `.env`: 环境变量配置
- `docker-compose.yml`: 容器编排配置
- `Dockerfile`: 应用镜像配置

## 故障排除

1. 端口冲突: 修改docker-compose.yml中的端口映射
2. 内存不足: 增加服务器内存或调整容器资源限制
3. 数据库连接失败: 检查PostgreSQL容器状态

## 技术支持

- 项目文档: 查看DEPLOYMENT.md
- 问题反馈: 联系开发团队

---
部署包创建时间: $(Get-Date)
包含镜像: $IncludeImages
"@ | Out-File -FilePath "$PackageDir\README.md" -Encoding UTF8

# 创建快速部署脚本
@"
#!/bin/bash
# 快速部署脚本

set -e

echo "=== Mastea Flask 快速部署 ==="

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装"
    exit 1
fi

# 启动服务
echo "启动Docker服务..."
docker-compose up -d

# 等待服务就绪
echo "等待服务启动..."
sleep 30

# 运行数据库迁移
echo "运行数据库迁移..."
docker-compose exec -T fastapi_app bash -c "cd migrations && alembic upgrade heads"

# 显示状态
echo "部署完成！"
echo "服务状态:"
docker-compose ps

echo ""
echo "访问地址:"
echo "  应用首页: http://localhost:8001"
echo "  API文档: http://localhost:8001/api/v1/docs"
echo "  管理界面: http://localhost:8001/dashboard_enhanced"
"@ | Out-File -FilePath "$PackageDir\quick-deploy.sh" -Encoding UTF8

# 创建tar包
Write-Host "创建tar包..." -ForegroundColor Blue
$CurrentDir = Get-Location
Set-Location $TempDir
tar -czf $OutputPath mastea-flask/
Move-Item $OutputPath $CurrentDir -Force
Set-Location $CurrentDir

# 清理临时目录
Remove-Item $TempDir -Recurse -Force

# 显示结果
$PackageSize = (Get-Item $OutputPath).Length / 1MB
Write-Host "=== 部署包创建完成 ===" -ForegroundColor Green
Write-Host "文件: $OutputPath" -ForegroundColor Yellow
Write-Host "大小: $([math]::Round($PackageSize, 2)) MB" -ForegroundColor Yellow
Write-Host ""
Write-Host "使用方法:" -ForegroundColor Cyan
Write-Host "1. 上传到服务器: scp $OutputPath user@server:/path/" -ForegroundColor Gray
Write-Host "2. 解压: tar -xzf $OutputPath" -ForegroundColor Gray
Write-Host "3. 部署: cd mastea-flask && ./quick-deploy.sh" -ForegroundColor Gray

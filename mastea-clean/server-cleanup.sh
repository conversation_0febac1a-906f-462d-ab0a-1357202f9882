#!/bin/bash
# 服务器清理脚本 - 清理所有旧的Mastea相关文件和容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "=== Mastea 服务器清理脚本 ==="

# 警告用户
echo "⚠️  警告：此脚本将删除所有Mastea相关的容器、镜像、卷和文件！"
echo "这包括："
echo "  - 所有mastea相关的Docker容器"
echo "  - 所有mastea相关的Docker镜像"
echo "  - 所有mastea相关的Docker卷"
echo "  - /opt/mastea* 目录"
echo ""
read -p "确定要继续吗？(输入 'YES' 确认): " -r
if [[ ! $REPLY == "YES" ]]; then
    log_info "操作已取消"
    exit 0
fi

# 1. 停止所有mastea相关容器
log_info "停止所有mastea相关容器..."
docker ps -a | grep mastea | awk '{print $1}' | xargs -r docker stop 2>/dev/null || true
log_success "容器已停止"

# 2. 删除所有mastea相关容器
log_info "删除所有mastea相关容器..."
docker ps -a | grep mastea | awk '{print $1}' | xargs -r docker rm 2>/dev/null || true
log_success "容器已删除"

# 3. 删除所有mastea相关镜像
log_info "删除所有mastea相关镜像..."
docker images | grep mastea | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true
log_success "镜像已删除"

# 4. 删除所有mastea相关卷
log_info "删除所有mastea相关卷..."
docker volume ls | grep mastea | awk '{print $2}' | xargs -r docker volume rm 2>/dev/null || true
log_success "卷已删除"

# 5. 删除所有mastea相关网络
log_info "删除所有mastea相关网络..."
docker network ls | grep mastea | awk '{print $1}' | xargs -r docker network rm 2>/dev/null || true
log_success "网络已删除"

# 6. 清理Docker系统
log_info "清理Docker系统..."
docker system prune -f
log_success "Docker系统清理完成"

# 7. 删除mastea相关目录
log_info "删除mastea相关目录..."
rm -rf /opt/mastea*
log_success "目录已删除"

# 8. 显示清理结果
log_success "=== 清理完成！ ==="
echo ""
log_info "已清理的内容："
log_success "  ✓ 所有mastea相关容器"
log_success "  ✓ 所有mastea相关镜像"
log_success "  ✓ 所有mastea相关卷"
log_success "  ✓ 所有mastea相关网络"
log_success "  ✓ /opt/mastea* 目录"
echo ""
log_info "服务器已准备好接受新的部署"

# 9. 显示当前Docker状态
echo ""
log_info "当前Docker状态："
echo "容器："
docker ps -a
echo ""
echo "镜像："
docker images
echo ""
echo "卷："
docker volume ls

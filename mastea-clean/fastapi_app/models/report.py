import uuid
import json
from datetime import datetime
from sqlalchemy import String, DateTime, Text, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from fastapi_app.core.database import Base
from typing import List
from .user import User

def generate_uuid_str():
    return str(uuid.uuid4())

class ConstitutionReport(Base):
    __tablename__ = 'constitution_reports'

    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=generate_uuid_str)
    user_id: Mapped[str] = mapped_column(String(36), ForeignKey('users.id'), nullable=False, index=True)
    report_data_json: Mapped[str] = mapped_column(Text, nullable=False)
    title: Mapped[str] = mapped_column(String(255), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user: Mapped["User"] = relationship(back_populates="constitution_reports")
    derived_complaint_reports: Mapped[List["ComplaintReport"]] = relationship(back_populates="base_constitution_report")
    
    def to_list_dict(self):
        """Convert to dictionary format for list display"""
        return {
            "id": self.id,
            "title": self.title or "体质评估报告",
            "created_at": self.created_at.isoformat() if self.created_at else datetime.utcnow().isoformat()
        }

class ComplaintReport(Base):
    __tablename__ = 'complaint_reports'

    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=generate_uuid_str)
    user_id: Mapped[str] = mapped_column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    base_constitution_report_id: Mapped[str] = mapped_column(String(36), ForeignKey('constitution_reports.id'), nullable=True, index=True)
    report_data_json: Mapped[str] = mapped_column(Text, nullable=False)
    title: Mapped[str] = mapped_column(String(255), nullable=False, default="综合主诉评估报告")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user: Mapped["User"] = relationship(back_populates="complaint_reports")
    base_constitution_report: Mapped["ConstitutionReport"] = relationship(back_populates="derived_complaint_reports")
    
    def to_list_dict(self):
        """Convert to dictionary format for list display"""
        return {
            "id": self.id,
            "title": self.title or "综合主诉评估报告",
            "created_at": self.created_at.isoformat() if self.created_at else datetime.utcnow().isoformat()
        } 
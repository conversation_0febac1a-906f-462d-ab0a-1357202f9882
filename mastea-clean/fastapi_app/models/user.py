import uuid
from datetime import datetime
from sqlalchemy import String, DateTime, <PERSON><PERSON><PERSON>, Float, Integer
from sqlalchemy.orm import Mapped, mapped_column, relationship
from fastapi_app.core.database import Base
from typing import List, TYPE_CHECKING, Dict, Any, Optional

if TYPE_CHECKING:
    from .address import Address
    from .conversation import Conversation
    from .report import ConstitutionReport, ComplaintReport
    from .order import Order
    from .cart import CartItem

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class User(Base):
    __tablename__ = 'users'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    username: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)
    email: Mapped[str] = mapped_column(String(120), unique=True, nullable=True)
    password_hash: Mapped[str] = mapped_column(String(128), nullable=True)  # 微信登录用户可能没有密码
    mobile: Mapped[str] = mapped_column(String(20), unique=True, nullable=True)

    # 微信登录相关字段
    wechat_openid: Mapped[Optional[str]] = mapped_column(String(64), unique=True, nullable=True, index=True)
    wechat_unionid: Mapped[Optional[str]] = mapped_column(String(64), unique=True, nullable=True, index=True)
    wechat_nickname: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    wechat_avatar: Mapped[Optional[str]] = mapped_column(String(256), nullable=True)
    wechat_authorized: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否已授权获取用户信息
    wechat_auth_time: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)  # 授权时间
    is_wechat_user: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否为微信用户
    last_login_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)  # 最后登录时间
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)  # 用户是否激活

    # 扩展用户信息字段（基于前端需求）
    real_name: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    address: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    zip_code: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    avatar_url: Mapped[Optional[str]] = mapped_column(String(256), nullable=True)
    is_member: Mapped[bool] = mapped_column(Boolean, default=False)
    membership_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    membership_end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    gender: Mapped[str] = mapped_column(String(10), nullable=True)
    age: Mapped[int] = mapped_column(Integer, nullable=True)
    height: Mapped[float] = mapped_column(Float, nullable=True)
    weight: Mapped[float] = mapped_column(Float, nullable=True)
    is_pregnant: Mapped[bool] = mapped_column(Boolean, default=False)
    is_preparing_pregnancy: Mapped[bool] = mapped_column(Boolean, default=False)

    orders: Mapped[List["Order"]] = relationship(back_populates="user")
    cart_items: Mapped[List["CartItem"]] = relationship(back_populates="user")
    addresses: Mapped[List["Address"]] = relationship(back_populates="user", cascade="all, delete-orphan")
    conversations: Mapped[List["Conversation"]] = relationship(back_populates="user", cascade="all, delete-orphan")
    constitution_reports: Mapped[List["ConstitutionReport"]] = relationship(back_populates="user")
    complaint_reports: Mapped[List["ComplaintReport"]] = relationship(back_populates="user")

    @property
    def phone(self):
        """为了兼容性，返回mobile作为phone"""
        return self.mobile

    @property
    def created_timestamp(self):
        return self.created_at.timestamp()
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'mobile': self.mobile,
            'gender': self.gender,
            'age': self.age,
            'height': self.height,
            'weight': self.weight,
            'is_pregnant': self.is_pregnant,
            'is_preparing_pregnancy': self.is_preparing_pregnancy,
            'status': 0,  # 临时固定值
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            # 新增字段
            'real_name': self.real_name,
            'address': self.address,
            'zip_code': self.zip_code,
            'avatar_url': self.avatar_url or self.wechat_avatar,  # 优先使用自定义头像，否则使用微信头像
            'is_member': self.is_member,
            'wechat_openid': self.wechat_openid,
            'wechat_unionid': self.wechat_unionid,
            'wechat_nickname': self.wechat_nickname,
            'wechat_authorized': self.wechat_authorized,
            'is_wechat_user': self.is_wechat_user,
            'last_login_at': self.last_login_at.isoformat() if self.last_login_at else None,
            'is_active': self.is_active
        }

        # 会员期限信息
        if self.is_member and self.membership_start_date and self.membership_end_date:
            result['membership_period'] = {
                'start_date': self.membership_start_date.strftime('%Y/%m/%d'),
                'end_date': self.membership_end_date.strftime('%Y/%m/%d')
            }

        return result
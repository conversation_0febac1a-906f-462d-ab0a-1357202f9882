"""
增强的购物车服务模块
整合k_api的购物车逻辑到主后端项目
"""

import logging
from typing import List, Dict, Any, Tuple, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from fastapi import HTTPException

from fastapi_app.models.cart import CartItem
from fastapi_app.models.goods import Goods
from fastapi_app.models.user import User
from fastapi_app.crud import cart as crud_cart

logger = logging.getLogger(__name__)

class CartService:
    """
    增强的购物车服务类
    整合k_api的购物车业务逻辑
    """
    
    @staticmethod
    def get_cart(db: Session, user_id: str) -> Dict[str, Any]:
        """
        获取用户购物车
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            购物车数据字典
        """
        try:
            # 获取用户的购物车项
            cart_items = db.query(CartItem).filter(
                CartItem.user_id == user_id
            ).all()
            
            items = []
            total_price = 0.0
            total_quantity = 0
            
            for cart_item in cart_items:
                # 获取商品信息
                goods = db.query(Goods).filter(Goods.id == cart_item.goods_id).first()
                if not goods:
                    logger.warning(f"购物车中的商品不存在: {cart_item.goods_id}")
                    continue
                
                item_data = {
                    "id": cart_item.id,
                    "productId": cart_item.goods_id,
                    "productName": goods.name,
                    "price": float(goods.price),
                    "originalPrice": float(goods.original_price) if goods.original_price else None,
                    "quantity": cart_item.quantity,
                    "image": goods.image_url or "",
                    "isVip": getattr(goods, 'is_vip', False),
                    "stock": goods.stock,
                    "maxPurchase": getattr(goods, 'max_purchase', None),
                    "selected": getattr(cart_item, 'selected', True),
                    "productType": getattr(cart_item, 'product_type', 0)
                }
                
                items.append(item_data)
                total_quantity += cart_item.quantity
                
                # 只计算选中商品的总价
                if getattr(cart_item, 'selected', True):
                    total_price += float(goods.price) * cart_item.quantity
            
            return {
                "items": items,
                "totalPrice": total_price,
                "totalQuantity": total_quantity
            }
            
        except Exception as e:
            logger.error(f"获取购物车失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取购物车失败: {str(e)}")
    
    @staticmethod
    def add_cart_item(
        db: Session, 
        user_id: str, 
        product_id: str, 
        quantity: int = 1,
        product_type: int = 0
    ) -> Tuple[str, int]:
        """
        添加商品到购物车
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            product_id: 商品ID
            quantity: 数量
            product_type: 商品类型
            
        Returns:
            (购物车项ID, 购物车总数量)
        """
        try:
            # 验证商品是否存在
            goods = db.query(Goods).filter(Goods.id == product_id).first()
            if not goods:
                raise ValueError("商品不存在")
            
            # 检查库存
            if goods.stock < quantity:
                raise ValueError(f"库存不足，当前库存: {goods.stock}")
            
            # 检查是否已存在相同商品
            existing_item = db.query(CartItem).filter(
                and_(
                    CartItem.user_id == user_id,
                    CartItem.goods_id == product_id
                )
            ).first()
            
            if existing_item:
                # 更新数量
                new_quantity = existing_item.quantity + quantity
                if goods.stock < new_quantity:
                    raise ValueError(f"库存不足，当前库存: {goods.stock}")
                
                existing_item.quantity = new_quantity
                cart_item_id = existing_item.id
            else:
                # 创建新的购物车项
                cart_item = CartItem(
                    user_id=user_id,
                    goods_id=product_id,
                    quantity=quantity
                )
                db.add(cart_item)
                db.flush()  # 获取ID
                cart_item_id = cart_item.id
            
            db.commit()
            
            # 计算购物车总数量
            total_quantity = db.query(CartItem).filter(
                CartItem.user_id == user_id
            ).count()
            
            logger.info(f"添加商品到购物车成功: user_id={user_id}, product_id={product_id}, quantity={quantity}")
            return cart_item_id, total_quantity
            
        except ValueError:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"添加商品到购物车失败: {e}")
            raise HTTPException(status_code=500, detail=f"添加商品到购物车失败: {str(e)}")
    
    @staticmethod
    def update_cart_item(
        db: Session, 
        user_id: str, 
        cart_item_id: str, 
        quantity: int
    ) -> Tuple[Dict[str, Any], float, int]:
        """
        更新购物车商品数量
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            cart_item_id: 购物车项ID
            quantity: 新数量
            
        Returns:
            (商品信息, 购物车总价, 购物车总数量)
        """
        try:
            # 查找购物车项
            cart_item = db.query(CartItem).filter(
                and_(
                    CartItem.id == cart_item_id,
                    CartItem.user_id == user_id
                )
            ).first()
            
            if not cart_item:
                raise ValueError("购物车项不存在")
            
            # 获取商品信息
            goods = db.query(Goods).filter(Goods.id == cart_item.goods_id).first()
            if not goods:
                raise ValueError("商品不存在")
            
            # 检查库存
            if goods.stock < quantity:
                raise ValueError(f"库存不足，当前库存: {goods.stock}")
            
            # 更新数量
            cart_item.quantity = quantity
            db.commit()
            
            # 计算购物车统计信息
            cart_data = CartService.get_cart(db, user_id)
            
            item_info = {
                "id": cart_item_id,
                "quantity": quantity,
                "price": float(goods.price)
            }
            
            logger.info(f"更新购物车商品数量成功: cart_item_id={cart_item_id}, quantity={quantity}")
            return item_info, cart_data["totalPrice"], cart_data["totalQuantity"]
            
        except ValueError:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"更新购物车商品数量失败: {e}")
            raise HTTPException(status_code=500, detail=f"更新购物车商品数量失败: {str(e)}")
    
    @staticmethod
    def remove_cart_items(
        db: Session, 
        user_id: str, 
        cart_item_ids: List[str]
    ) -> Tuple[float, int]:
        """
        删除购物车商品
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            cart_item_ids: 要删除的购物车项ID列表
            
        Returns:
            (购物车总价, 购物车总数量)
        """
        try:
            # 删除指定的购物车项
            deleted_count = db.query(CartItem).filter(
                and_(
                    CartItem.id.in_(cart_item_ids),
                    CartItem.user_id == user_id
                )
            ).delete(synchronize_session=False)
            
            if deleted_count == 0:
                raise ValueError("没有找到要删除的购物车项")
            
            db.commit()
            
            # 计算删除后的购物车统计信息
            cart_data = CartService.get_cart(db, user_id)
            
            logger.info(f"删除购物车商品成功: user_id={user_id}, deleted_count={deleted_count}")
            return cart_data["totalPrice"], cart_data["totalQuantity"]
            
        except ValueError:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"删除购物车商品失败: {e}")
            raise HTTPException(status_code=500, detail=f"删除购物车商品失败: {str(e)}")
    
    @staticmethod
    def clear_cart(db: Session, user_id: str) -> None:
        """
        清空购物车
        
        Args:
            db: 数据库会话
            user_id: 用户ID
        """
        try:
            db.query(CartItem).filter(CartItem.user_id == user_id).delete()
            db.commit()
            
            logger.info(f"清空购物车成功: user_id={user_id}")
            
        except Exception as e:
            db.rollback()
            logger.error(f"清空购物车失败: {e}")
            raise HTTPException(status_code=500, detail=f"清空购物车失败: {str(e)}")
    
    @staticmethod
    def update_cart_item_selection(
        db: Session, 
        user_id: str, 
        cart_item_ids: List[str], 
        selected: bool
    ) -> Tuple[List[str], float]:
        """
        更新购物车商品选择状态
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            cart_item_ids: 购物车项ID列表，空列表表示全部
            selected: 选择状态
            
        Returns:
            (选中商品ID列表, 选中商品总价)
        """
        try:
            # 构建查询条件
            query = db.query(CartItem).filter(CartItem.user_id == user_id)
            
            if cart_item_ids:
                query = query.filter(CartItem.id.in_(cart_item_ids))
            
            # 更新选择状态（注意：这里需要确保CartItem模型有selected字段）
            cart_items = query.all()
            for item in cart_items:
                if hasattr(item, 'selected'):
                    item.selected = selected
            
            db.commit()
            
            # 计算选中商品的总价和ID列表
            selected_items = []
            selected_total_price = 0.0
            
            all_cart_items = db.query(CartItem).filter(CartItem.user_id == user_id).all()
            for cart_item in all_cart_items:
                if getattr(cart_item, 'selected', True):
                    selected_items.append(cart_item.id)
                    
                    # 获取商品价格
                    goods = db.query(Goods).filter(Goods.id == cart_item.goods_id).first()
                    if goods:
                        selected_total_price += float(goods.price) * cart_item.quantity
            
            logger.info(f"更新购物车选择状态成功: user_id={user_id}, selected={selected}")
            return selected_items, selected_total_price
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新购物车选择状态失败: {e}")
            raise HTTPException(status_code=500, detail=f"更新购物车选择状态失败: {str(e)}")

# 创建全局实例
cart_service = CartService()

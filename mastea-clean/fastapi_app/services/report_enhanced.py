"""
增强的体质报告服务模块
整合k_api的体质报告逻辑到主后端项目
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy import desc, func
from sqlalchemy.orm import Session
from fastapi import HTTPException

from fastapi_app.models.report import ConstitutionReport, ComplaintReport
from fastapi_app.models.user import User
from fastapi_app.crud import report as crud_report

logger = logging.getLogger(__name__)

# 时间格式化字符串
DATE_FORMAT = "%Y-%m-%d"

class ReportService:
    """
    增强的体质报告服务类
    整合k_api的报告业务逻辑
    """
    
    @staticmethod
    async def get_report_overview(db: Session, user_id: str, limit: int = 5) -> Dict[str, Any]:
        """
        获取用户体质报告总览数据
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            limit: 历史报告返回数量
            
        Returns:
            包含报告总览数据的字典
        """
        try:
            # 查询用户最近半年的报告
            half_year_ago = datetime.now() - timedelta(days=180)
            
            # 获取用户的所有体质报告，按日期降序排序
            reports = db.query(ConstitutionReport).filter(
                ConstitutionReport.user_id == user_id,
                ConstitutionReport.created_at >= half_year_ago
            ).order_by(desc(ConstitutionReport.created_at)).all()
            
            # 如果没有报告，返回空数据
            if not reports:
                return {
                    "half_year_avg_score": None,
                    "latest_constitution_type": None,
                    "chart_data": [],
                    "date_range": "",
                    "report_history": [],
                    "has_completed_test": False
                }
            
            # 计算半年平均分数（使用总分或主要体质分数）
            avg_score = sum(getattr(report, 'total_score', 80) for report in reports) / len(reports)
            
            # 获取最近一次报告的体质类型
            latest_report = reports[0]
            main_constitution_type = getattr(latest_report, 'primary_constitution', '平和质')
            
            # 构建图表数据
            chart_data = []
            for report in reports:
                chart_data.append({
                    "date": report.created_at.strftime(DATE_FORMAT),
                    "score": getattr(report, 'total_score', 80)
                })
            
            # 设置日期范围
            date_range = ""
            if reports:
                oldest_date = reports[-1].created_at.strftime(DATE_FORMAT)
                latest_date = reports[0].created_at.strftime(DATE_FORMAT)
                date_range = f"{oldest_date} ~ {latest_date}"
            
            # 构建历史报告列表
            report_history = []
            for report in reports[:limit]:
                report_history.append({
                    "id": report.id,
                    "date": report.created_at.strftime(DATE_FORMAT),
                    "score": getattr(report, 'total_score', 80),
                    "overall": {
                        "BCType": getattr(report, 'primary_constitution', '平和质'),
                        "mainSymptoms": getattr(report, 'main_symptoms', '无明显症状')
                    }
                })
            
            return {
                "half_year_avg_score": round(avg_score),
                "latest_constitution_type": main_constitution_type,
                "chart_data": chart_data,
                "date_range": date_range,
                "report_history": report_history,
                "has_completed_test": True
            }
            
        except Exception as e:
            logger.error(f"获取报告总览失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取报告总览失败: {str(e)}")
    
    @staticmethod
    async def get_report_detail(db: Session, report_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取用户体质报告详情数据
        
        Args:
            db: 数据库会话
            report_id: 报告ID
            user_id: 用户ID，用于验证权限
            
        Returns:
            包含报告详情数据的字典，如果报告不存在返回None
        """
        try:
            # 查询指定报告
            report = db.query(ConstitutionReport).filter(
                ConstitutionReport.id == report_id
            ).first()
            
            # 如果报告不存在
            if not report:
                return None
            
            # 验证报告所属用户
            if report.user_id != user_id:
                return None
            
            # 获取用户信息
            user = db.query(User).filter(User.id == user_id).first()
            user_name = user.username if user else ""
            
            # 构建体质指数数组（用于雷达图）
            bc_index = []
            constitution_scores = getattr(report, 'constitution_scores', {})
            if isinstance(constitution_scores, dict):
                for constitution_type, score in constitution_scores.items():
                    bc_index.append({
                        "name": constitution_type,
                        "value": score
                    })
            
            # 获取主体质和兼有体质
            primary_constitution = getattr(report, 'primary_constitution', '平和质')
            secondary_constitution = getattr(report, 'secondary_constitution', '')
            
            # 构建报告详情响应
            return {
                "id": report.id,
                "date": report.created_at.strftime(DATE_FORMAT),
                "user_name": user_name,
                "score": getattr(report, 'total_score', 80),
                "overall": {
                    "BCType": primary_constitution,
                    "mainSymptoms": getattr(report, 'main_symptoms', '无明显症状'),
                    "text": getattr(report, 'summary', '体质分析完成')
                },
                "analysis": {
                    "mainBC": primary_constitution,
                    "secondaryBC": secondary_constitution,
                    "BCIndex": bc_index
                },
                "definition": {
                    "definition": getattr(report, 'constitution_definition', ''),
                    "bodyShape": getattr(report, 'body_characteristics', ''),
                    "commonSymptoms": getattr(report, 'common_symptoms', ''),
                    "psychology": getattr(report, 'psychological_characteristics', ''),
                    "diseases": getattr(report, 'susceptible_diseases', ''),
                    "environment": getattr(report, 'environmental_adaptation', '')
                },
                "dailyTips": {
                    "term": getattr(report, 'season', ''),
                    "intro": getattr(report, 'health_advice', ''),
                    "food": getattr(report, 'dietary_advice', ''),
                    "sleep": getattr(report, 'sleep_advice', ''),
                    "exercise": getattr(report, 'exercise_advice', ''),
                    "mood": getattr(report, 'emotional_advice', '')
                }
            }
            
        except Exception as e:
            logger.error(f"获取报告详情失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取报告详情失败: {str(e)}")
    
    @staticmethod
    async def get_tea_recommend(db: Session, report_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取基于体质报告的茶品推荐
        
        Args:
            db: 数据库会话
            report_id: 报告ID
            user_id: 用户ID，用于验证权限
            
        Returns:
            包含茶品推荐数据的字典，如果报告不存在返回None
        """
        try:
            # 查询指定报告
            report = db.query(ConstitutionReport).filter(
                ConstitutionReport.id == report_id
            ).first()
            
            # 如果报告不存在
            if not report:
                return None
            
            # 验证报告所属用户
            if report.user_id != user_id:
                return None
            
            # 获取推荐茶品（这里需要根据实际的茶品推荐逻辑来实现）
            recommended_teas = getattr(report, 'recommended_teas', [])
            
            # 如果报告中没有推荐茶品，根据体质类型生成默认推荐
            if not recommended_teas:
                primary_constitution = getattr(report, 'primary_constitution', '平和质')
                recommended_teas = ReportService._get_default_tea_recommendations(primary_constitution)
            
            return {
                "reportId": report_id,
                "constitutionType": getattr(report, 'primary_constitution', '平和质'),
                "recommendedTeas": recommended_teas,
                "recommendationReason": getattr(report, 'recommendation_reason', '根据您的体质特点推荐以下茶品'),
                "usage": getattr(report, 'tea_usage_advice', '建议每日饮用1-2次，每次3-5克')
            }
            
        except Exception as e:
            logger.error(f"获取茶品推荐失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取茶品推荐失败: {str(e)}")
    
    @staticmethod
    def _get_default_tea_recommendations(constitution_type: str) -> List[Dict[str, Any]]:
        """
        根据体质类型获取默认茶品推荐
        
        Args:
            constitution_type: 体质类型
            
        Returns:
            推荐茶品列表
        """
        # 默认茶品推荐映射
        tea_recommendations = {
            "平和质": [
                {"name": "绿茶", "reason": "清热解毒，适合平和体质日常饮用"},
                {"name": "乌龙茶", "reason": "半发酵茶，性质温和"}
            ],
            "气虚质": [
                {"name": "红茶", "reason": "温性茶品，有助于补气"},
                {"name": "普洱熟茶", "reason": "温胃养气，适合气虚体质"}
            ],
            "阳虚质": [
                {"name": "红茶", "reason": "温阳散寒"},
                {"name": "生姜茶", "reason": "温中散寒，补阳气"}
            ],
            "阴虚质": [
                {"name": "绿茶", "reason": "清热生津，滋阴润燥"},
                {"name": "白茶", "reason": "性凉，有助于滋阴"}
            ],
            "痰湿质": [
                {"name": "普洱生茶", "reason": "去脂化痰"},
                {"name": "乌龙茶", "reason": "消脂去腻，化痰湿"}
            ],
            "湿热质": [
                {"name": "绿茶", "reason": "清热利湿"},
                {"name": "苦丁茶", "reason": "清热解毒，利湿"}
            ],
            "血瘀质": [
                {"name": "红茶", "reason": "活血化瘀"},
                {"name": "玫瑰花茶", "reason": "理气活血"}
            ],
            "气郁质": [
                {"name": "花茶", "reason": "疏肝理气"},
                {"name": "茉莉花茶", "reason": "理气解郁"}
            ],
            "特禀质": [
                {"name": "白茶", "reason": "性质温和，不易过敏"},
                {"name": "淡绿茶", "reason": "清淡温和"}
            ]
        }
        
        return tea_recommendations.get(constitution_type, [
            {"name": "绿茶", "reason": "适合大多数体质的温和茶品"}
        ])

# 创建全局实例
report_service = ReportService()

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from fastapi_app.core.database import get_db
from fastapi_app.core.security import get_current_user, get_user_by_id_or_token
from fastapi_app.models.user import User as UserModel
from fastapi_app.crud import chat as crud_chat
from fastapi_app.schemas import chat as schemas_chat, conversation as schemas_conv
from fastapi_app.services.ai_chat_service import get_ai_response_stream, load_prompt
import asyncio
from fastapi.responses import StreamingResponse
import logging

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/conversations")
def get_conversations(
    user_id: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(get_current_user)
):
    """
    获取用户的对话列表
    支持以下认证方式：
    1. 通过token认证
    2. 通过query参数user_id
    """
    target_user_id = None
    if current_user:
        target_user_id = current_user.id
        logger.info(f"使用当前认证用户ID获取对话: {target_user_id}")
    elif user_id:
        target_user_id = user_id
        logger.info(f"使用查询参数user_id获取对话: {target_user_id}")
    else:
        # 默认使用admin_user_id
        target_user_id = "admin_user_id"
        logger.info(f"未提供用户ID，使用默认admin_user_id获取对话")
    
    conversations = crud_chat.get_conversations(db, user_id=target_user_id)
    
    # 处理每个对话的timestamp
    result = []
    for conv in conversations:
        result.append({
            "id": conv.id,
            "user_id": conv.user_id,
            "title": conv.title,
            "created_at": int(conv.created_at.timestamp() * 1000) if conv.created_at else None,
            "updated_at": int(conv.updated_at.timestamp() * 1000) if conv.updated_at else None
        })
    
    return {
        "code": 0,
        "message": "获取成功",
        "data": result
    }

@router.post("/conversations")
def create_conversation(
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(get_current_user),
    user_id: Optional[str] = Query(None),
    title: Optional[str] = Query(None, description="对话标题")
):
    """
    创建新对话 - 支持多种认证方式
    """
    # 前端请求固定处理逻辑
    # 如果没有token或user_id，使用admin_user_id
    # 这是为了解决前端点击"新建对话"按钮时的401错误
    target_user_id = "admin_user_id"  # 默认值

    # 如果有认证用户，使用认证用户ID
    if current_user:
        target_user_id = current_user.id
        logger.info(f"使用认证用户ID创建对话: {target_user_id}")
    # 如果提供了user_id参数，使用这个参数
    elif user_id:
        target_user_id = user_id
        logger.info(f"使用查询参数user_id创建对话: {target_user_id}")
    else:
        logger.info(f"未提供认证信息，使用默认admin_user_id创建对话")
    
    try:
        # 如果提供了标题，使用提供的标题，否则使用默认标题
        conversation_title = title or "新对话"
        
        # 创建新对话
        conversation = crud_chat.create_conversation(
            db, 
            user_id=target_user_id, 
            title=conversation_title
        )
        
        # 返回与Flask版本兼容的格式
        logger.info(f"成功创建对话: ID={conversation.id}, 用户ID={target_user_id}")
        return {
            "id": conversation.id,
            "user_id": conversation.user_id,
            "title": conversation.title,
            "created_at": int(conversation.created_at.timestamp() * 1000),
            "updated_at": int(conversation.updated_at.timestamp() * 1000)
        }
    except Exception as e:
        logger.error(f"创建对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建对话失败: {str(e)}")

@router.get("/history/{conversation_id}")
def get_chat_history(
    conversation_id: str,
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(get_current_user),
    user_id: Optional[str] = Query(None)
):
    """
    Get the chat history for a specific conversation.
    """
    # TODO: Check if the user has access to this conversation
    messages = crud_chat.get_chat_history(db, conversation_id=conversation_id)
    
    # 手动处理每个消息的timestamp
    result = []
    for msg in messages:
        result.append({
            "id": msg.id,
            "conversation_id": msg.conversation_id,
            "user_id": msg.user_id,
            "message": msg.message,
            "is_user": msg.is_user,
            "timestamp": int(msg.timestamp.timestamp() * 1000) if msg.timestamp else None
        })
    
    return {
        "code": 0,
        "message": "获取成功",
        "data": result
    }

@router.post("/send")
def send_message(
    *,
    item: schemas_chat.ChatMessageCreate,
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(get_current_user),
    user_id: Optional[str] = Query(None)
):
    """
    Send a message. If conversation_id is not provided, a new one is created.
    This endpoint will only save the user's message.
    A separate endpoint or a background task will handle the AI's response.
    """
    target_user_id = None
    if current_user:
        target_user_id = current_user.id
        logger.info(f"使用当前认证用户ID: {target_user_id}")
    elif user_id:
        target_user_id = user_id
        logger.info(f"使用查询参数user_id: {target_user_id}")
    elif item.user_id:
        target_user_id = item.user_id
        logger.info(f"使用请求体中的user_id: {target_user_id}")
    else:
        logger.error("未提供有效用户ID")
        raise HTTPException(status_code=401, detail="未提供有效用户ID")
        
    conversation_id = item.conversation_id
    if not conversation_id:
        # Create a new conversation if one doesn't exist
        # The title could be the first few words of the message
        title = item.message[:20] + "..." if len(item.message) > 20 else item.message
        conversation = crud_chat.create_conversation(db, user_id=target_user_id, title=title)
        conversation_id = conversation.id

    # TODO: Check if the user has access to this conversation
    
    # Save user's message
    user_message = crud_chat.create_chat_message(
        db, 
        user_id=target_user_id, 
        conversation_id=conversation_id, 
        message=item.message, 
        is_user=True
    )
    
    # 返回与Flask版本兼容的格式
    return {
        "message": user_message.message,
        "id": user_message.id,
        "conversation_id": user_message.conversation_id,
        "is_user": user_message.is_user,
        "timestamp": int(user_message.timestamp.timestamp() * 1000) if user_message.timestamp else None
    }

async def fake_ai_response_generator(db: Session, user_id: str, conversation_id: str):
    """
    A fake async generator that simulates a streaming AI response.
    """
    await asyncio.sleep(1) # Simulate initial thinking time
    
    full_response = "你好！这是一个模拟的AI回答。我将逐句地将内容发送给您。"
    ai_message = ""
    words = full_response.split("。")
    for i, word in enumerate(words):
        if not word: continue
        chunk = word + "。"
        ai_message += chunk
        yield chunk
        await asyncio.sleep(0.5) # Simulate time between sentences

    # Save the full AI response to the database
    crud_chat.create_chat_message(
        db,
        user_id=user_id,
        conversation_id=conversation_id,
        message=ai_message,
        is_user=False
    )

@router.post("/send_stream")
async def send_message_stream(
    *,
    item: schemas_chat.ChatMessageCreate = Body(...),
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(get_current_user),
    user_id: Optional[str] = Query(None)
):
    """
    Send a message and get a streaming response from the AI.
    Handles different task types like 'constitution_assessment'.
    """
    # 调试日志
    logger.info(f"收到消息请求：用户ID={item.user_id or user_id}，消息长度={len(item.message)}")
    if current_user:
        logger.info(f"当前经过认证的用户: {current_user.id} ({current_user.username})")
    else:
        logger.warning(f"无认证用户，尝试使用query参数: {user_id}")
        
    target_user_id = None
    if current_user:
        target_user_id = current_user.id
        logger.info(f"使用当前认证用户ID: {target_user_id}")
    elif user_id:
        target_user_id = user_id
        logger.info(f"使用查询参数user_id: {target_user_id}")
    elif item.user_id:  # 从请求体获取user_id
        target_user_id = item.user_id
        logger.info(f"使用请求体中的user_id: {target_user_id}")
    else:
        logger.error("未提供有效用户ID")
        raise HTTPException(status_code=401, detail="未提供有效用户ID")
        
    conversation_id = item.conversation_id
    if not conversation_id:
        title = item.message[:20] + "..." if len(item.message) > 20 else item.message
        # 如果是主诉评估，使用更明确的标题
        if item.task_type == "chief_complaint":
            title = "主诉评估对话"
        conversation = crud_chat.create_conversation(db, user_id=target_user_id, title=title)
        conversation_id = conversation.id

    # Save user's message to the database
    user_message = crud_chat.create_chat_message(
        db, 
        user_id=target_user_id, 
        conversation_id=conversation_id, 
        message=item.message, 
        is_user=True
    )
    
    # 准备用户消息对象，用于在流式响应中发送
    user_message_data = {
        "id": user_message.id,
        "conversation_id": user_message.conversation_id,
        "user_id": user_message.user_id,
        "message": user_message.message,
        "is_user": user_message.is_user,
        "timestamp": int(user_message.timestamp.timestamp() * 1000) if user_message.timestamp else None
    }

    # Get the chat history for context
    history = crud_chat.get_chat_history(db, conversation_id=conversation_id)
    
    # Check if this is the start of a specific task
    initial_prompt = None
    if item.task_type and len(history) <= 1: # <=1 because user message was just added
        prompt_name = ""
        if item.task_type == "constitution_assessment":
            prompt_name = "体质报告AI任务"
        elif item.task_type == "chief_complaint":
            prompt_name = "综合主诉报告AI任务"
        
        if prompt_name:
            initial_prompt = load_prompt(prompt_name)

    # Return a streaming response from the AI service
    logger.info(f"开始生成AI响应流，用户：{target_user_id}，对话：{conversation_id}")
    
    # 添加完整的SSE响应头部
    headers = {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
        "Connection": "keep-alive",
        "Transfer-Encoding": "chunked",
        "X-Accel-Buffering": "no",  # 禁用Nginx缓冲
        "X-Content-Type-Options": "nosniff",
        "Access-Control-Allow-Origin": "*"  # CORS支持
    }
    
    return StreamingResponse(
        get_ai_response_stream(
            db=db, 
            user_id=target_user_id, 
            conversation_id=conversation_id, 
            history=history,
            initial_prompt=initial_prompt,
            user_message_data=user_message_data,  # 添加用户消息数据
            task_type=item.task_type  # 传递任务类型
        ),
        media_type="text/event-stream",
        headers=headers
    )

# 添加一个专门用于前端的创建对话API，不需要认证
@router.post("/frontend/new-conversation")
def create_frontend_conversation(
    db: Session = Depends(get_db),
    title: Optional[str] = Query(None, description="对话标题")
):
    """
    专门为前端提供的创建新对话API，不需要认证，直接使用admin_user_id
    """
    try:
        # 使用默认admin_user_id
        target_user_id = "admin_user_id"
        logger.info(f"前端请求创建新对话，使用默认admin_user_id")
        
        # 创建新对话
        conversation_title = title or "新对话"
        conversation = crud_chat.create_conversation(
            db, 
            user_id=target_user_id, 
            title=conversation_title
        )
        
        # 返回与Flask版本兼容的格式
        logger.info(f"成功创建前端对话: ID={conversation.id}")
        return {
            "id": conversation.id,
            "user_id": conversation.user_id,
            "title": conversation.title,
            "created_at": int(conversation.created_at.timestamp() * 1000),
            "updated_at": int(conversation.updated_at.timestamp() * 1000)
        }
    except Exception as e:
        logger.error(f"创建前端对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建对话失败: {str(e)}") 
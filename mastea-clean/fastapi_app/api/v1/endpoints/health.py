from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
from fastapi_app.core.database import get_db
import time

router = APIRouter()

@router.get("/health")
def health_check(db: Session = Depends(get_db)):
    """
    健康检查端点
    检查应用和数据库连接状态
    """
    try:
        # 检查数据库连接
        db.execute(text("SELECT 1"))
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "database": "connected",
            "version": "1.0.0"
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "timestamp": time.time(),
            "database": "disconnected",
            "error": str(e),
            "version": "1.0.0"
        }
from sqlalchemy.orm import Session
from fastapi_app.models import cart as models_cart
from fastapi_app.models import goods as models_goods
from fastapi_app.schemas import cart as schemas_cart
from sqlalchemy.orm import joinedload

def get_cart_items(db: Session, user_id: str):
    return db.query(models_cart.CartItem).filter(models_cart.CartItem.user_id == user_id).all()

def add_item_to_cart(db: Session, user_id: str, item: schemas_cart.CartItemCreate):
    # Check if the item already exists in the cart
    db_item = db.query(models_cart.CartItem).filter(
        models_cart.CartItem.user_id == user_id,
        models_cart.CartItem.goods_id == item.goods_id,
        models_cart.CartItem.sku_id == item.sku_id
    ).first()

    if db_item:
        # If it exists, just update the count
        db_item.count += item.count
    else:
        # If not, create a new cart item
        # We need to fetch product details first
        goods_info = None
        image_url = None
        if item.sku_id:
            goods_sku_info = db.query(models_goods.GoodsSKU).options(joinedload(models_goods.GoodsSKU.goods)).filter(models_goods.GoodsSKU.id == item.sku_id).first()
            if goods_sku_info:
                goods_info = goods_sku_info
                image_url = goods_sku_info.goods.image
        else:
            goods_regular_info = db.query(models_goods.Goods).filter(models_goods.Goods.id == item.goods_id).first()
            if goods_regular_info:
                goods_info = goods_regular_info
                image_url = goods_regular_info.image
        
        if not goods_info:
            return None # Or raise an exception

        db_item = models_cart.CartItem(
            user_id=user_id,
            goods_id=item.goods_id,
            sku_id=item.sku_id,
            name=goods_info.name,
            price=goods_info.price,
            image=image_url,
            count=item.count,
            checked=True
        )
        db.add(db_item)
    
    db.commit()
    db.refresh(db_item)
    return db_item

def update_cart_item(db: Session, cart_item_id: str, item_update: schemas_cart.CartItemUpdate):
    db_item = db.query(models_cart.CartItem).filter(models_cart.CartItem.id == cart_item_id).first()
    if not db_item:
        return None
    
    update_data = item_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_item, key, value)
        
    db.commit()
    db.refresh(db_item)
    return db_item

def remove_item_from_cart(db: Session, cart_item_id: str):
    db_item = db.query(models_cart.CartItem).filter(models_cart.CartItem.id == cart_item_id).first()
    if db_item:
        db.delete(db_item)
        db.commit()
    return db_item 
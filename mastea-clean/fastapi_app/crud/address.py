from sqlalchemy.orm import Session
from fastapi_app.models import address as models_address
from fastapi_app.schemas import address as schemas_address

def get_addresses(db: Session, user_id: str):
    return db.query(models_address.Address).filter(models_address.Address.user_id == user_id).all()

def create_address(db: Session, user_id: str, address: schemas_address.AddressCreate):
    db_address = models_address.Address(**address.model_dump(), user_id=user_id)
    db.add(db_address)
    db.commit()
    db.refresh(db_address)
    return db_address

def update_address(db: Session, address_id: str, address_update: schemas_address.AddressUpdate):
    db_address = db.query(models_address.Address).filter(models_address.Address.id == address_id).first()
    if not db_address:
        return None
    
    update_data = address_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_address, key, value)
        
    db.commit()
    db.refresh(db_address)
    return db_address

def delete_address(db: Session, address_id: str):
    db_address = db.query(models_address.Address).filter(models_address.Address.id == address_id).first()
    if db_address:
        db.delete(db_address)
        db.commit()
    return db_address 
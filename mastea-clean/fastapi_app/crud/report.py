import json
from sqlalchemy.orm import Session
from fastapi_app.models import report as models_report
from fastapi_app.schemas import report as schemas_report
import uuid
from datetime import datetime

def get_report_by_id(db: Session, report_id: str, report_type: str):
    if report_type == 'constitution':
        return db.query(models_report.ConstitutionReport).filter(models_report.ConstitutionReport.id == report_id).first()
    elif report_type == 'complaint':
        return db.query(models_report.ComplaintReport).filter(models_report.ComplaintReport.id == report_id).first()
    return None

def get_constitution_reports_by_user(db: Session, user_id: str):
    return db.query(models_report.ConstitutionReport).filter(models_report.ConstitutionReport.user_id == user_id).order_by(models_report.ConstitutionReport.created_at.desc()).all()

def get_all_reports_by_user(db: Session, user_id: str):
    constitution_reports = db.query(models_report.ConstitutionReport).filter(models_report.ConstitutionReport.user_id == user_id).all()
    complaint_reports = db.query(models_report.ComplaintReport).filter(models_report.ComplaintReport.user_id == user_id).all()
    return constitution_reports, complaint_reports

def create_constitution_report(db: Session, user_id: str, report_data: dict):
    """Creates a constitution report from a dictionary."""
    report_json = json.dumps(report_data, ensure_ascii=False)
    db_report = models_report.ConstitutionReport(
        id=str(uuid.uuid4()),
        user_id=user_id,
        title=report_data.get("title", "体质评估报告"),
        report_data_json=report_json,
        created_at=datetime.utcnow()
    )
    db.add(db_report)
    db.commit()
    db.refresh(db_report)
    return db_report

def create_complaint_report(db: Session, user_id: str, report_data: dict, title: str = None, base_constitution_report_id: str = None):
    """Creates a complaint report from a dictionary."""
    report_json = json.dumps(report_data, ensure_ascii=False)
    db_report = models_report.ComplaintReport(
        id=str(uuid.uuid4()),
        user_id=user_id,
        title=title or "综合主诉评估报告",
        base_constitution_report_id=base_constitution_report_id,
        report_data_json=report_json,
        created_at=datetime.utcnow()
    )
    db.add(db_report)
    db.commit()
    db.refresh(db_report)
    return db_report

def create_complaint_report_from_schema(db: Session, user_id: str, report: schemas_report.ComplaintReportCreate):
    """Creates a complaint report from a Pydantic schema (original function)."""
    report_data_str = json.dumps(report.report_data, ensure_ascii=False)
    db_report = models_report.ComplaintReport(
        user_id=user_id,
        title=report.title,
        base_constitution_report_id=report.base_constitution_report_id,
        report_data_json=report_data_str
    )
    db.add(db_report)
    db.commit()
    db.refresh(db_report)
    return db_report 
from sqlalchemy.orm import Session
from fastapi_app.models import conversation as models_conv, chat as models_chat
from fastapi_app.schemas import chat as schemas_chat
from datetime import datetime

def get_conversations(db: Session, user_id: str):
    return db.query(models_conv.Conversation).filter(models_conv.Conversation.user_id == str(user_id)).order_by(models_conv.Conversation.updated_at.desc()).all()

def get_conversation_by_id(db: Session, conversation_id: str):
    return db.query(models_conv.Conversation).filter(models_conv.Conversation.id == conversation_id).first()

def create_conversation(db: Session, user_id: str, title: str = "新对话"):
    db_conv = models_conv.Conversation(user_id=str(user_id), title=title)
    db.add(db_conv)
    db.commit()
    db.refresh(db_conv)
    return db_conv

def get_chat_history(db: Session, conversation_id: str):
    return db.query(models_chat.ChatMessage).filter(models_chat.ChatMessage.conversation_id == conversation_id).order_by(models_chat.ChatMessage.timestamp.asc()).all()

def create_chat_message(db: Session, user_id: str, conversation_id: str, message: str, is_user: bool):
    import logging
    logger = logging.getLogger(__name__)
    
    # 处理admin用户场景
    if user_id == "admin_user_id":
        from fastapi_app.models.user import User
        from sqlalchemy import text
        # 首先检查用户表是否有admin用户
        admin_user = db.query(User).filter(User.id == user_id).first()
        
        if not admin_user:
            # 检查是否存在不完整的记录
            result = db.execute(text("SELECT 1 FROM users WHERE id = 'admin_user_id'")).fetchone()
            if result:
                # 尝试修复不完整记录
                logger.warning("发现不完整的admin用户记录，尝试修复")
                try:
                    db.execute(text("DELETE FROM users WHERE id = 'admin_user_id'"))
                    db.commit()
                    logger.info("已删除不完整的admin用户记录")
                except Exception as e:
                    db.rollback()
                    logger.error(f"尝试删除不完整admin用户记录失败: {str(e)}")
                    
            # 创建新的完整admin用户记录
            logger.info("创建新的admin用户记录")
            admin_user = User(
                id="admin_user_id",
                username="admin",
                email="<EMAIL>",
                mobile="admin",  # 确保mobile字段有值
                password_hash="not_used_for_admin"
            )
            db.add(admin_user)
            try:
                db.commit()
                db.refresh(admin_user)
                logger.info("Admin用户成功创建，ID: admin_user_id")
            except Exception as e:
                db.rollback()
                logger.error(f"创建admin用户失败: {str(e)}")
                # 尝试设置为null处理外键约束
                user_id = None
                logger.warning(f"将使用null作为user_id以避免外键约束")
    
    # 创建消息对象
    db_message = models_chat.ChatMessage(
        user_id=str(user_id) if user_id else None,
        conversation_id=conversation_id,
        message=message,
        is_user=is_user
    )
    db.add(db_message)
    
    # 更新对话的时间戳
    try:
        db.query(models_conv.Conversation).filter(models_conv.Conversation.id == conversation_id).update(
            {"updated_at": datetime.now()}
        )
    except Exception as e:
        logger.warning(f"更新对话时间戳失败: {str(e)}")
    
    # 提交消息
    try:
        db.commit()
        db.refresh(db_message)
        return db_message
    except Exception as e:
        db.rollback()
        logger.error(f"保存聊天消息失败: {str(e)}")
        
        # 如果是外键约束错误，创建临时消息对象(不持久化)
        logger.info("创建临时聊天消息对象")
        temp_message = models_chat.ChatMessage(
            id=models_chat.generate_uuid(),
            user_id=None,  # 不指定用户ID以避免外键约束
            conversation_id=conversation_id,
            message=message,
            is_user=is_user,
            timestamp=datetime.now()
        )
        return temp_message
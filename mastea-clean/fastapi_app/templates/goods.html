<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品浏览 - 麦斯特养生茶</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .search-bar {
            text-align: center;
            margin-bottom: 20px;
        }
        .search-bar input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 300px;
            margin-right: 10px;
        }
        .search-bar button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .goods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .goods-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .goods-card:hover {
            transform: translateY(-5px);
        }
        .goods-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .goods-price {
            font-size: 20px;
            color: #e74c3c;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .goods-original-price {
            font-size: 14px;
            color: #999;
            text-decoration: line-through;
            margin-left: 10px;
        }
        .goods-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .goods-category {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            display: inline-block;
            margin-bottom: 15px;
        }
        .add-to-cart-btn {
            width: 100%;
            padding: 12px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.2s;
        }
        .add-to-cart-btn:hover {
            background: #218838;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error {
            text-align: center;
            padding: 50px;
            color: #e74c3c;
        }
        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .stats {
            text-align: center;
            margin-bottom: 20px;
            color: #666;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <a href="/dashboard_enhanced" class="back-link">← 返回主面板</a>
    
    <div class="header">
        <h1>🍃 麦斯特养生茶商品</h1>
        <p>精选优质茶叶，为您的健康保驾护航</p>
    </div>

    <div class="search-bar">
        <input type="text" id="search-input" placeholder="搜索商品..." />
        <button onclick="searchGoods()">搜索</button>
        <button onclick="loadAllGoods()">显示全部</button>
    </div>

    <div class="stats" id="goods-stats">
        正在加载商品信息...
    </div>

    <div id="loading" class="loading" style="display: none;">
        正在加载商品...
    </div>

    <div id="error" class="error" style="display: none;"></div>

    <div class="goods-grid" id="goods-grid"></div>

    <script>
        // 全局变量
        let allGoods = [];
        let isLoggedIn = false;
        let authToken = null;

        // 获取认证头
        function getAuthHeaders() {
            const headers = { 'Content-Type': 'application/json' };
            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }
            return headers;
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('error').textContent = message;
            document.getElementById('error').style.display = 'block';
            document.getElementById('loading').style.display = 'none';
        }

        // 隐藏错误信息
        function hideError() {
            document.getElementById('error').style.display = 'none';
        }

        // 显示加载状态
        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            if (show) {
                hideError();
            }
        }

        // 更新统计信息
        function updateStats(goods) {
            const statsEl = document.getElementById('goods-stats');
            if (goods && goods.length > 0) {
                const totalGoods = goods.length;
                const avgPrice = (goods.reduce((sum, g) => sum + (g.price || 0), 0) / totalGoods).toFixed(2);
                statsEl.textContent = `共 ${totalGoods} 件商品，平均价格 ¥${avgPrice}`;
            } else {
                statsEl.textContent = '暂无商品数据';
            }
        }

        // 渲染商品列表
        function renderGoods(goods) {
            const container = document.getElementById('goods-grid');
            
            if (!goods || goods.length === 0) {
                container.innerHTML = '<div class="no-data">暂无商品数据</div>';
                updateStats([]);
                return;
            }

            const goodsHtml = goods.map(goods => `
                <div class="goods-card">
                    <div class="goods-title">${goods.name || '未知商品'}</div>
                    <div class="goods-category">${goods.category_name || '未分类'}</div>
                    <div class="goods-price">
                        ¥${goods.price || '0.00'}
                        ${goods.original_price ? `<span class="goods-original-price">¥${goods.original_price}</span>` : ''}
                    </div>
                    <div class="goods-description">${goods.description || '暂无描述'}</div>
                    <button class="add-to-cart-btn" onclick="addToCart('${goods.id}')">
                        🛒 加入购物车
                    </button>
                </div>
            `).join('');

            container.innerHTML = goodsHtml;
            updateStats(goods);
        }

        // 加载所有商品
        async function loadAllGoods() {
            showLoading(true);
            hideError();

            try {
                const response = await fetch('/api/v1/goods', {
                    headers: getAuthHeaders()
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('API响应:', result);

                // 处理不同的响应格式
                let goods = [];
                if (result.data && result.data.items) {
                    goods = result.data.items;
                } else if (result.data && Array.isArray(result.data)) {
                    goods = result.data;
                } else if (Array.isArray(result)) {
                    goods = result;
                } else {
                    console.warn('未知的响应格式:', result);
                    goods = [];
                }

                allGoods = goods;
                renderGoods(goods);
                showLoading(false);

            } catch (error) {
                console.error('加载商品失败:', error);
                showError('加载商品失败: ' + error.message);
                showLoading(false);
            }
        }

        // 搜索商品
        async function searchGoods() {
            const keyword = document.getElementById('search-input').value.trim();
            
            if (!keyword) {
                renderGoods(allGoods);
                return;
            }

            const filteredGoods = allGoods.filter(goods => 
                goods.name.toLowerCase().includes(keyword.toLowerCase()) ||
                (goods.description && goods.description.toLowerCase().includes(keyword.toLowerCase())) ||
                (goods.category_name && goods.category_name.toLowerCase().includes(keyword.toLowerCase()))
            );

            renderGoods(filteredGoods);
        }

        // 添加到购物车
        async function addToCart(goodsId) {
            try {
                const data = {
                    goods_id: goodsId,
                    count: 1
                };

                const response = await fetch('/api/v1/cart', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    alert('已添加到购物车！');
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    alert('添加失败: ' + (errorData.message || response.statusText));
                }
            } catch (error) {
                console.error('添加到购物车失败:', error);
                alert('添加失败: ' + error.message);
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有保存的认证token
            const savedToken = localStorage.getItem('authToken');
            if (savedToken) {
                authToken = savedToken;
                isLoggedIn = true;
            }

            // 绑定搜索框回车事件
            document.getElementById('search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchGoods();
                }
            });

            // 加载商品
            loadAllGoods();
        });
    </script>
</body>
</html>
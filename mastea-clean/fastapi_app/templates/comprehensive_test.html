<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastAPI 后端功能综合测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            padding: 25px;
            border-left: 5px solid #3498db;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        
        .function-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .function-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        .function-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .function-card h3 {
            color: #3498db;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .input-group input, .input-group select, .input-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .input-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .user-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .user-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .function-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 FastAPI 后端功能综合测试</h1>
        
        <!-- 当前用户信息 -->
        <div class="user-info">
            <h3>👤 当前测试用户信息</h3>
            <div class="form-row">
                <div class="input-group">
                    <label for="currentUserId">用户ID:</label>
                    <input type="text" id="currentUserId" value="test_user_123">
                </div>
                <div class="input-group">
                    <label for="authToken">认证Token (可选):</label>
                    <input type="text" id="authToken" placeholder="留空使用测试模式">
                </div>
            </div>
        </div>

        <!-- 1. 用户管理 -->
        <div class="section">
            <h2>👥 用户管理</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>用户注册</h3>
                    <div class="input-group">
                        <label>用户名:</label>
                        <input type="text" id="regUsername" value="testuser">
                    </div>
                    <div class="input-group">
                        <label>邮箱:</label>
                        <input type="email" id="regEmail" value="<EMAIL>">
                    </div>
                    <div class="input-group">
                        <label>手机:</label>
                        <input type="text" id="regMobile" value="13800138000">
                    </div>
                    <div class="input-group">
                        <label>密码:</label>
                        <input type="password" id="regPassword" value="123456">
                    </div>
                    <button class="btn btn-success" onclick="registerUser()">注册用户</button>
                    <div class="result" id="registerResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>用户登录</h3>
                    <div class="input-group">
                        <label>用户名/邮箱:</label>
                        <input type="text" id="loginUsername" value="testuser">
                    </div>
                    <div class="input-group">
                        <label>密码:</label>
                        <input type="password" id="loginPassword" value="123456">
                    </div>
                    <button class="btn btn-success" onclick="loginUser()">登录</button>
                    <div class="result" id="loginResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>获取用户信息</h3>
                    <button class="btn" onclick="getUserInfo()">获取当前用户信息</button>
                    <div class="result" id="userInfoResult"></div>
                </div>
            </div>
        </div>

        <!-- 2. 体质报告管理 -->
        <div class="section">
            <h2>📊 体质报告管理</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>体质问卷评估</h3>
                    <div class="input-group">
                        <label>性别:</label>
                        <select id="constitutionGender">
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>年龄段:</label>
                        <select id="constitutionAge">
                            <option value="20-30">20-30</option>
                            <option value="30-40" selected>30-40</option>
                            <option value="40-50">40-50</option>
                            <option value="50-60">50-60</option>
                        </select>
                    </div>
                    <button class="btn btn-success" onclick="createConstitutionReport()">生成体质报告</button>
                    <div class="result" id="constitutionResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>体质报告列表</h3>
                    <button class="btn" onclick="getConstitutionReports()">获取体质报告列表</button>
                    <div class="result" id="constitutionListResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>体质报告详情</h3>
                    <div class="input-group">
                        <label>报告ID:</label>
                        <input type="text" id="constitutionReportId" placeholder="输入报告ID">
                    </div>
                    <button class="btn" onclick="getConstitutionReportDetail()">查看报告详情</button>
                    <div class="result" id="constitutionDetailResult"></div>
                </div>
            </div>
        </div>

        <!-- 3. 主诉评估管理 -->
        <div class="section">
            <h2>🩺 主诉评估管理</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>开启主诉评估</h3>
                    <div class="input-group">
                        <label>基础体质报告ID:</label>
                        <input type="text" id="baseReportId" placeholder="输入体质报告ID">
                    </div>
                    <button class="btn btn-success" onclick="startComplaintAssessment()">开启主诉评估</button>
                    <div class="result" id="complaintStartResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>主诉报告列表</h3>
                    <button class="btn" onclick="getComplaintReports()">获取主诉报告列表</button>
                    <div class="result" id="complaintListResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>主诉报告详情</h3>
                    <div class="input-group">
                        <label>报告ID:</label>
                        <input type="text" id="complaintReportId" placeholder="输入报告ID">
                    </div>
                    <button class="btn" onclick="getComplaintReportDetail()">查看报告详情</button>
                    <div class="result" id="complaintDetailResult"></div>
                </div>
            </div>
        </div>

        <!-- 4. AI聊天功能 -->
        <div class="section">
            <h2>🤖 AI聊天功能</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>对话管理</h3>
                    <button class="btn" onclick="getConversations()">获取对话列表</button>
                    <button class="btn btn-success" onclick="createConversation()">创建新对话</button>
                    <div class="result" id="conversationResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>发送消息</h3>
                    <div class="input-group">
                        <label>对话ID:</label>
                        <input type="text" id="conversationId" placeholder="输入对话ID">
                    </div>
                    <div class="input-group">
                        <label>消息内容:</label>
                        <textarea id="chatMessage" placeholder="输入要发送的消息"></textarea>
                    </div>
                    <button class="btn btn-success" onclick="sendChatMessage()">发送消息</button>
                    <div class="result" id="chatResult"></div>
                </div>
            </div>
        </div>

        <!-- 5. 商品管理 -->
        <div class="section">
            <h2>🛍️ 商品管理</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>商品列表</h3>
                    <div class="input-group">
                        <label>分类 (可选):</label>
                        <input type="text" id="goodsCategory" placeholder="输入分类名称">
                    </div>
                    <button class="btn" onclick="getGoods()">获取商品列表</button>
                    <div class="result" id="goodsResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>商品详情</h3>
                    <div class="input-group">
                        <label>商品ID:</label>
                        <input type="text" id="goodsId" placeholder="输入商品ID">
                    </div>
                    <button class="btn" onclick="getGoodsDetail()">查看商品详情</button>
                    <div class="result" id="goodsDetailResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>商品搜索</h3>
                    <div class="input-group">
                        <label>搜索关键词:</label>
                        <input type="text" id="searchKeyword" placeholder="输入搜索关键词">
                    </div>
                    <button class="btn" onclick="searchGoods()">搜索商品</button>
                    <div class="result" id="searchResult"></div>
                </div>
            </div>
        </div>

        <!-- 6. 购物车管理 -->
        <div class="section">
            <h2>🛒 购物车管理</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>购物车操作</h3>
                    <div class="input-group">
                        <label>商品ID:</label>
                        <input type="text" id="cartGoodsId" placeholder="输入商品ID">
                    </div>
                    <div class="input-group">
                        <label>数量:</label>
                        <input type="number" id="cartQuantity" value="1" min="1">
                    </div>
                    <button class="btn btn-success" onclick="addToCart()">添加到购物车</button>
                    <button class="btn" onclick="getCart()">查看购物车</button>
                    <button class="btn btn-warning" onclick="updateCartItem()">更新数量</button>
                    <button class="btn btn-danger" onclick="removeFromCart()">移除商品</button>
                    <div class="result" id="cartResult"></div>
                </div>
            </div>
        </div>

        <!-- 7. 收藏管理 -->
        <div class="section">
            <h2>❤️ 收藏管理</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>收藏操作</h3>
                    <div class="input-group">
                        <label>商品ID:</label>
                        <input type="text" id="favoriteGoodsId" placeholder="输入商品ID">
                    </div>
                    <button class="btn btn-success" onclick="addToFavorites()">添加收藏</button>
                    <button class="btn" onclick="getFavorites()">查看收藏列表</button>
                    <button class="btn btn-danger" onclick="removeFromFavorites()">取消收藏</button>
                    <div class="result" id="favoriteResult"></div>
                </div>
            </div>
        </div>

        <!-- 8. 地址管理 -->
        <div class="section">
            <h2>📍 地址管理</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>地址操作</h3>
                    <div class="form-row">
                        <div class="input-group">
                            <label>收货人:</label>
                            <input type="text" id="addressName" value="张三">
                        </div>
                        <div class="input-group">
                            <label>手机号:</label>
                            <input type="text" id="addressPhone" value="13800138000">
                        </div>
                    </div>
                    <div class="input-group">
                        <label>详细地址:</label>
                        <input type="text" id="addressDetail" value="北京市朝阳区某某街道某某小区">
                    </div>
                    <button class="btn btn-success" onclick="addAddress()">添加地址</button>
                    <button class="btn" onclick="getAddresses()">查看地址列表</button>
                    <div class="result" id="addressResult"></div>
                </div>
            </div>
        </div>

        <!-- 9. 订单管理 -->
        <div class="section">
            <h2>📦 订单管理</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>订单操作</h3>
                    <div class="input-group">
                        <label>收货地址ID:</label>
                        <input type="text" id="orderAddressId" placeholder="输入地址ID">
                    </div>
                    <button class="btn btn-success" onclick="createOrder()">创建订单</button>
                    <button class="btn" onclick="getOrders()">查看订单列表</button>
                    <div class="result" id="orderResult"></div>
                </div>
                
                <div class="function-card">
                    <h3>订单详情</h3>
                    <div class="input-group">
                        <label>订单ID:</label>
                        <input type="text" id="orderDetailId" placeholder="输入订单ID">
                    </div>
                    <button class="btn" onclick="getOrderDetail()">查看订单详情</button>
                    <button class="btn btn-warning" onclick="payOrder()">支付订单</button>
                    <button class="btn btn-danger" onclick="cancelOrder()">取消订单</button>
                    <div class="result" id="orderDetailResult"></div>
                </div>
            </div>
        </div>

        <!-- 10. 管理功能 -->
        <div class="section">
            <h2>⚙️ 管理功能</h2>
            <div class="function-group">
                <div class="function-card">
                    <h3>系统状态</h3>
                    <button class="btn" onclick="getSystemStatus()">获取系统状态</button>
                    <button class="btn" onclick="getApiDocs()">查看API文档</button>
                    <div class="result" id="systemResult"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentUserId = 'test_user_123';
        let authToken = '';
        let currentConversationId = '';
        let currentReportId = '';
        
        // 工具函数
        function updateCurrentUserId() {
            currentUserId = document.getElementById('currentUserId').value;
        }
        
        function updateAuthToken() {
            authToken = document.getElementById('authToken').value;
        }
        
        function getHeaders() {
            const headers = {
                'Content-Type': 'application/json'
            };
            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }
            return headers;
        }
        
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            
            // 添加状态提示
            const statusDiv = element.nextElementSibling;
            if (statusDiv && statusDiv.classList.contains('status')) {
                statusDiv.remove();
            }
            
            const status = document.createElement('div');
            status.classList.add('status');
            if (result.error) {
                status.classList.add('error');
                status.textContent = '❌ 请求失败';
            } else {
                status.classList.add('success');
                status.textContent = '✅ 请求成功';
            }
            element.parentNode.insertBefore(status, element.nextSibling);
        }
        
        async function makeRequest(url, options = {}) {
            updateCurrentUserId();
            updateAuthToken();
            
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        ...getHeaders(),
                        ...options.headers
                    }
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                return { error: error.message };
            }
        }
        
        // 用户管理函数
        async function registerUser() {
            const userData = {
                username: document.getElementById('regUsername').value,
                email: document.getElementById('regEmail').value,
                mobile: document.getElementById('regMobile').value,
                password: document.getElementById('regPassword').value
            };
            
            const result = await makeRequest('/api/v1/auth/register', {
                method: 'POST',
                body: JSON.stringify(userData)
            });
            
            displayResult('registerResult', result);
        }
        
        async function loginUser() {
            const loginData = {
                username: document.getElementById('loginUsername').value,
                password: document.getElementById('loginPassword').value
            };
            
            const result = await makeRequest('/api/v1/auth/login', {
                method: 'POST',
                body: JSON.stringify(loginData)
            });
            
            if (result.access_token) {
                document.getElementById('authToken').value = result.access_token;
                authToken = result.access_token;
            }
            
            displayResult('loginResult', result);
        }
        
        async function getUserInfo() {
            const result = await makeRequest('/api/v1/users/me');
            displayResult('userInfoResult', result);
        }
        
        // 体质报告函数
        async function createConstitutionReport() {
            const reportData = {
                user_id: currentUserId,
                answers: {
                    gender: document.getElementById('constitutionGender').value,
                    age_group: document.getElementById('constitutionAge').value,
                    q3_core_symptom: "疲劳乏力，容易感冒",
                    q4_sweating: ["白天动则大汗"],
                    q5_stool: "规律正常",
                    q6_sleep: "睡眠正常",
                    q7_season_response: ["四季适应良好"],
                    q8_diet_preference: "规律清淡",
                    q9_long_term_habits: ["作息规律"],
                    q10_body_type: "肌肉发达，体脂低",
                    q11_skin_status: "光滑红润",
                    q12_priority_conditioning: "增强体质"
                }
            };
            
            const result = await makeRequest('/api/v1/reports/constitution/calculate', {
                method: 'POST',
                body: JSON.stringify(reportData)
            });
            
            if (result.id) {
                currentReportId = result.id;
                document.getElementById('constitutionReportId').value = result.id;
                document.getElementById('baseReportId').value = result.id;
            }
            
            displayResult('constitutionResult', result);
        }
        
        async function getConstitutionReports() {
            const result = await makeRequest(`/api/v1/reports/constitution/list?user_id=${currentUserId}`);
            displayResult('constitutionListResult', result);
        }
        
        async function getConstitutionReportDetail() {
            const reportId = document.getElementById('constitutionReportId').value;
            if (!reportId) {
                alert('请输入报告ID');
                return;
            }
            
            const result = await makeRequest(`/api/v1/reports/constitution/${reportId}`);
            displayResult('constitutionDetailResult', result);
        }
        
        // 主诉评估函数
        async function startComplaintAssessment() {
            const baseReportId = document.getElementById('baseReportId').value;
            if (!baseReportId) {
                alert('请输入基础体质报告ID');
                return;
            }
            
            const data = {
                user_id: currentUserId,
                base_constitution_report_id: baseReportId
            };
            
            const result = await makeRequest('/api/v1/reports/complaint/start', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            if (result.data && result.data.conversation_id) {
                currentConversationId = result.data.conversation_id;
                document.getElementById('conversationId').value = result.data.conversation_id;
            }
            
            displayResult('complaintStartResult', result);
        }
        
        async function getComplaintReports() {
            const result = await makeRequest(`/api/v1/reports/complaint/list?user_id=${currentUserId}`);
            displayResult('complaintListResult', result);
        }
        
        async function getComplaintReportDetail() {
            const reportId = document.getElementById('complaintReportId').value;
            if (!reportId) {
                alert('请输入报告ID');
                return;
            }
            
            const result = await makeRequest(`/api/v1/reports/complaint/${reportId}`);
            displayResult('complaintDetailResult', result);
        }
        
        // AI聊天函数
        async function getConversations() {
            const result = await makeRequest(`/api/v1/chat/conversations?user_id=${currentUserId}`);
            displayResult('conversationResult', result);
        }
        
        async function createConversation() {
            const data = {
                user_id: currentUserId,
                title: "测试对话"
            };
            
            const result = await makeRequest('/api/v1/chat/conversations', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            if (result.id) {
                currentConversationId = result.id;
                document.getElementById('conversationId').value = result.id;
            }
            
            displayResult('conversationResult', result);
        }
        
        async function sendChatMessage() {
            const conversationId = document.getElementById('conversationId').value;
            const message = document.getElementById('chatMessage').value;
            
            if (!conversationId || !message) {
                alert('请输入对话ID和消息内容');
                return;
            }
            
            const data = {
                message: message,
                conversation_id: conversationId,
                user_id: currentUserId
            };
            
            const result = await makeRequest('/api/v1/chat/send', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            displayResult('chatResult', result);
        }
        
        // 商品管理函数
        async function getGoods() {
            const category = document.getElementById('goodsCategory').value;
            let url = '/api/v1/goods';
            if (category) {
                url += `?category=${encodeURIComponent(category)}`;
            }
            
            const result = await makeRequest(url);
            displayResult('goodsResult', result);
        }
        
        async function getGoodsDetail() {
            const goodsId = document.getElementById('goodsId').value;
            if (!goodsId) {
                alert('请输入商品ID');
                return;
            }
            
            const result = await makeRequest(`/api/v1/goods/${goodsId}`);
            displayResult('goodsDetailResult', result);
        }
        
        async function searchGoods() {
            const keyword = document.getElementById('searchKeyword').value;
            if (!keyword) {
                alert('请输入搜索关键词');
                return;
            }
            
            const result = await makeRequest(`/api/v1/goods/search?q=${encodeURIComponent(keyword)}`);
            displayResult('searchResult', result);
        }
        
        // 购物车函数
        async function addToCart() {
            const goodsId = document.getElementById('cartGoodsId').value;
            const quantity = document.getElementById('cartQuantity').value;
            
            if (!goodsId) {
                alert('请输入商品ID');
                return;
            }
            
            const data = {
                goods_id: goodsId,
                quantity: parseInt(quantity),
                user_id: currentUserId
            };
            
            const result = await makeRequest('/api/v1/cart/add', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            displayResult('cartResult', result);
        }
        
        async function getCart() {
            const result = await makeRequest(`/api/v1/cart?user_id=${currentUserId}`);
            displayResult('cartResult', result);
        }
        
        async function updateCartItem() {
            const goodsId = document.getElementById('cartGoodsId').value;
            const quantity = document.getElementById('cartQuantity').value;
            
            if (!goodsId) {
                alert('请输入商品ID');
                return;
            }
            
            const data = {
                goods_id: goodsId,
                quantity: parseInt(quantity),
                user_id: currentUserId
            };
            
            const result = await makeRequest('/api/v1/cart/update', {
                method: 'PUT',
                body: JSON.stringify(data)
            });
            
            displayResult('cartResult', result);
        }
        
        async function removeFromCart() {
            const goodsId = document.getElementById('cartGoodsId').value;
            
            if (!goodsId) {
                alert('请输入商品ID');
                return;
            }
            
            const result = await makeRequest(`/api/v1/cart/remove?goods_id=${goodsId}&user_id=${currentUserId}`, {
                method: 'DELETE'
            });
            
            displayResult('cartResult', result);
        }
        
        // 收藏函数
        async function addToFavorites() {
            const goodsId = document.getElementById('favoriteGoodsId').value;
            
            if (!goodsId) {
                alert('请输入商品ID');
                return;
            }
            
            const data = {
                goods_id: goodsId,
                user_id: currentUserId
            };
            
            const result = await makeRequest('/api/v1/goods/favorites', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            displayResult('favoriteResult', result);
        }
        
        async function getFavorites() {
            const result = await makeRequest(`/api/v1/goods/favorites?user_id=${currentUserId}`);
            displayResult('favoriteResult', result);
        }
        
        async function removeFromFavorites() {
            const goodsId = document.getElementById('favoriteGoodsId').value;
            
            if (!goodsId) {
                alert('请输入商品ID');
                return;
            }
            
            const result = await makeRequest(`/api/v1/goods/favorites/${goodsId}?user_id=${currentUserId}`, {
                method: 'DELETE'
            });
            
            displayResult('favoriteResult', result);
        }
        
        // 地址管理函数
        async function addAddress() {
            const data = {
                recipient_name: document.getElementById('addressName').value,
                recipient_phone: document.getElementById('addressPhone').value,
                address: document.getElementById('addressDetail').value,
                user_id: currentUserId
            };
            
            const result = await makeRequest('/api/v1/addresses', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            displayResult('addressResult', result);
        }
        
        async function getAddresses() {
            const result = await makeRequest(`/api/v1/addresses?user_id=${currentUserId}`);
            displayResult('addressResult', result);
        }
        
        // 订单管理函数
        async function createOrder() {
            const addressId = document.getElementById('orderAddressId').value;
            
            if (!addressId) {
                alert('请输入收货地址ID');
                return;
            }
            
            const data = {
                address_id: addressId,
                user_id: currentUserId
            };
            
            const result = await makeRequest('/api/v1/orders', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            displayResult('orderResult', result);
        }
        
        async function getOrders() {
            const result = await makeRequest(`/api/v1/orders?user_id=${currentUserId}`);
            displayResult('orderResult', result);
        }
        
        async function getOrderDetail() {
            const orderId = document.getElementById('orderDetailId').value;
            
            if (!orderId) {
                alert('请输入订单ID');
                return;
            }
            
            const result = await makeRequest(`/api/v1/orders/${orderId}`);
            displayResult('orderDetailResult', result);
        }
        
        async function payOrder() {
            const orderId = document.getElementById('orderDetailId').value;
            
            if (!orderId) {
                alert('请输入订单ID');
                return;
            }
            
            const result = await makeRequest(`/api/v1/orders/${orderId}/pay`, {
                method: 'POST'
            });
            
            displayResult('orderDetailResult', result);
        }
        
        async function cancelOrder() {
            const orderId = document.getElementById('orderDetailId').value;
            
            if (!orderId) {
                alert('请输入订单ID');
                return;
            }
            
            const result = await makeRequest(`/api/v1/orders/${orderId}/cancel`, {
                method: 'POST'
            });
            
            displayResult('orderDetailResult', result);
        }
        
        // 系统管理函数
        async function getSystemStatus() {
            const result = await makeRequest('/');
            displayResult('systemResult', result);
        }
        
        async function getApiDocs() {
            window.open('/api/v1/docs', '_blank');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认用户ID
            updateCurrentUserId();
            
            // 添加一些UI交互
            document.getElementById('currentUserId').addEventListener('change', updateCurrentUserId);
            document.getElementById('authToken').addEventListener('change', updateAuthToken);
        });
    </script>
</body>
</html>
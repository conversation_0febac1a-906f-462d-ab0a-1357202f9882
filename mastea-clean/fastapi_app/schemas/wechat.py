"""
微信相关的数据模式定义
整合k_api的微信数据结构到主后端项目
"""

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class WeChatLogin(BaseModel):
    """
    微信小程序登录请求模型
    
    基于2025年最新的微信小程序API规范
    包含获取用户信息所需的字段
    """
    code: str = Field(..., description="微信小程序授权码，由前端wx.login()获取")
    encryptedData: Optional[str] = Field(None, description="加密的用户信息，需要用户授权并由wx.getUserInfo()获取")
    iv: Optional[str] = Field(None, description="加密算法的初始向量，由wx.getUserInfo()获取")
    raw_data: Optional[str] = Field(None, description="原始数据字符串，由wx.getUserInfo()获取")
    signature: Optional[str] = Field(None, description="使用sha1得到的数据签名，由wx.getUserInfo()获取")
    app_version: Optional[str] = Field(None, description="小程序版本号")
    client_ip: Optional[str] = Field(None, description="客户端IP地址")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "code": "013Xjp000kQnPM1pgt300LqaLS4Xjp0W",
                "encryptedData": "1234abcd...",
                "iv": "abcd1234...",
                "raw_data": "{\"nickName\":\"微信用户\",\"gender\":0}",
                "signature": "1234567890abcdef",
                "app_version": "1.0.0"
            }
        }
    }

class WeChatUserInfo(BaseModel):
    """
    微信用户信息响应模型
    """
    id: str
    nickname: str
    avatar: Optional[str] = None
    vipLevel: int = 0
    registerDate: str
    isAuthorized: bool = False

class WeChatLoginResponse(BaseModel):
    """
    微信登录响应模型
    """
    token: str
    refreshToken: str
    expiresIn: int
    userInfo: WeChatUserInfo

class RefreshToken(BaseModel):
    """
    刷新令牌请求模型
    """
    refreshToken: str = Field(..., description="刷新令牌")

class RefreshTokenResponse(BaseModel):
    """
    刷新令牌响应模型
    """
    token: str
    expiresIn: int

class WeChatUserProfile(BaseModel):
    """
    微信用户详细信息模型
    """
    id: str
    username: str
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[str] = None
    wechat_openid: Optional[str] = None
    wechat_unionid: Optional[str] = None
    wechat_authorized: bool = False
    is_wechat_user: bool = False
    is_member: bool = False
    vip_level: int = 0
    register_date: Optional[datetime] = None
    last_login_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class WeChatAuthStatus(BaseModel):
    """
    微信授权状态模型
    """
    is_authorized: bool
    auth_time: Optional[datetime] = None
    openid: Optional[str] = None
    unionid: Optional[str] = None
    
    class Config:
        from_attributes = True

class WeChatBindRequest(BaseModel):
    """
    微信绑定请求模型
    """
    code: str = Field(..., description="微信授权码")
    user_id: Optional[str] = Field(None, description="要绑定的用户ID")

class WeChatUnbindRequest(BaseModel):
    """
    微信解绑请求模型
    """
    user_id: str = Field(..., description="要解绑的用户ID")
    confirm: bool = Field(True, description="确认解绑")

# 兼容性模型：支持k_api的数据格式
class KApiWeChatLogin(BaseModel):
    """
    k_api兼容的微信登录模型
    """
    code: str
    encryptedData: Optional[str] = None
    iv: Optional[str] = None
    raw_data: Optional[str] = None
    signature: Optional[str] = None
    app_version: Optional[str] = None
    client_ip: Optional[str] = None

class KApiUserResponse(BaseModel):
    """
    k_api兼容的用户响应模型
    """
    id: str
    nickname: str
    avatar: Optional[str] = None
    vipLevel: int = 0
    registerDate: str
    isAuthorized: bool = False

class KApiLoginResponse(BaseModel):
    """
    k_api兼容的登录响应模型
    """
    code: int = 200
    success: bool = True
    data: dict
    message: str = "登录成功"

# 错误响应模型
class WeChatErrorResponse(BaseModel):
    """
    微信相关错误响应模型
    """
    code: int
    success: bool = False
    data: Optional[dict] = None
    message: str

# 开发环境令牌生成模型
class DevTokenRequest(BaseModel):
    """
    开发环境令牌生成请求模型
    """
    use_real_user: bool = False
    custom_id: Optional[str] = None

class DevTokenResponse(BaseModel):
    """
    开发环境令牌生成响应模型
    """
    token: str
    token_type: str = "bearer"
    user_id: str
    user_nickname: Optional[str] = None

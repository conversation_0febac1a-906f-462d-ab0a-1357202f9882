from pydantic import BaseModel
from typing import Optional

class AddressBase(BaseModel):
    name: str
    mobile: str
    province: str
    city: str
    district: str
    detail: str
    is_default: bool = False

class AddressCreate(AddressBase):
    pass

class AddressUpdate(BaseModel):
    name: Optional[str] = None
    mobile: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    district: Optional[str] = None
    detail: Optional[str] = None
    is_default: Optional[bool] = None

class Address(AddressBase):
    id: str

    class Config:
        from_attributes = True 
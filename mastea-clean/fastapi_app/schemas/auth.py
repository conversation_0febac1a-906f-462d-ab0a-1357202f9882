from pydantic import BaseModel, EmailStr
from typing import Optional

class UserCreate(BaseModel):
    username: str
    password: str
    email: EmailStr
    mobile: str
    phone: Optional[str] = None
    nickname: Optional[str] = None

class UserLogin(BaseModel):
    username: str  # 可以是用户名、邮箱或手机号
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class WeChatLogin(BaseModel):
    code: str  # 微信授权码
    state: Optional[str] = None  # 防CSRF攻击的状态参数

class WeChatUserInfo(BaseModel):
    openid: str
    unionid: Optional[str] = None
    nickname: Optional[str] = None
    headimgurl: Optional[str] = None
    sex: Optional[int] = None  # 1为男性，2为女性，0为未知
    province: Optional[str] = None
    city: Optional[str] = None
    country: Optional[str] = None
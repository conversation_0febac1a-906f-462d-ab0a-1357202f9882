body { font-family: sans-serif; margin: 0; background-color: #f4f7f6; display: flex; justify-content: center; padding-top: 20px; padding-bottom: 20px; min-height: 100vh;}
.container { width: 100%; max-width: 800px; margin: auto; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
h1, h2 { color: #333; text-align: center; margin-bottom: 20px;}
#loading { text-align: center; padding: 20px; color: #555; font-style: italic; }
#error-message { color: red; text-align: center; padding: 10px; border: 1px solid red; border-radius: 4px; margin-bottom: 15px; display: none; }

/* 问卷区域 */
#questionnaire-section { display: none; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 20px; background-color: #f9f9f9;}
.question-group { margin-bottom: 20px; }
.question-group label { font-weight: bold; display: block; margin-bottom: 8px; color: #555; }
.question-group select, .question-group input[type="text"], .question-group input[type="radio"], .question-group input[type="checkbox"] { margin-right: 5px; }
.options-group label { font-weight: normal; margin-left: 5px; }
.form-actions { text-align: center; margin-top: 30px; }
#submit-questionnaire-button { padding: 12px 25px; background-color: #5cb85c; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 1em; }
#submit-questionnaire-button:hover { background-color: #4cae4c; }
#cancel-questionnaire-button { padding: 10px 20px; background-color: #f0ad4e; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 0.9em; margin-left:10px;}

/* 报告列表样式 */
#report-list-section { display: none; } /* 默认隐藏 */
#report-list, #complaint-report-list { list-style: none; padding: 0; margin-top: 20px; }
#report-list li, #complaint-report-list li {
    background-color: #e9f5ff;
    border: 1px solid #b3d7ff;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
}
#report-list li:hover, #complaint-report-list li:hover { background-color: #d0eaff; }
.report-title { font-weight: bold; color: #0056b3; }
.report-date { font-size: 0.9em; color: #555; }
.report-actions button {
    padding: 5px 10px;
    margin-left: 10px;
    cursor: pointer;
    border: none;
    border-radius: 3px;
    font-size: 0.9em;
}
.view-button { background-color: #007bff; color: white; }
.assess-button { background-color: #28a745; color: white; }
#start-new-assessment-button {
     display: block; 
     width: fit-content; 
     margin: 20px auto 0; 
     padding: 10px 20px;
     background-color: #17a2b8;
     color: white;
     border: none;
     border-radius: 5px;
     cursor: pointer;
     font-size: 1em;
 }
 #start-new-assessment-button:hover { background-color: #138496; }

/* 聊天界面样式 */
#chat-section { display: none; margin-top: 20px; border-top: 1px solid #eee; padding-top: 20px; }
#chatbox {
     min-height: 300px; 
     max-height: 50vh; 
     border: 1px solid #ccc;
     padding: 10px;
     overflow-y: auto;
     margin-bottom: 10px;
     background-color: #fff;
     display: flex;
     flex-direction: column; 
}
.message-container { display: flex; flex-direction: column; margin-bottom: 8px; }
.message { padding: 8px 12px; border-radius: 15px; max-width: 75%; word-wrap: break-word; line-height: 1.4; }
.user-message { background-color: #dcf8c6; align-self: flex-end; border-bottom-right-radius: 5px; margin-left: auto; }
.ai-message { background-color: #f1f0f0; align-self: flex-start; border-bottom-left-radius: 5px; margin-right: auto; }
.message .timestamp { font-size: 0.7em; color: #888; display: block; margin-top: 4px; }
.user-message .timestamp { text-align: right; }
.ai-message .timestamp { text_align: left; }
#input-area { display: flex; margin-top: 10px; }
#message-input { flex-grow: 1; padding: 10px; border: 1px solid #ccc; border-radius: 5px; margin-right: 10px; }
#send-button { padding: 10px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; }
#send-button:disabled { background-color: #ccc; cursor: not-allowed; }
#chat-loading { display: none; text-align: center; color: #888; margin: 10px 0; }
.system-notification {
    text-align: center; color: #777; font-style: italic; font-size: 0.9em;
    padding: 10px; margin: 5px auto; max-width: 80%;
}
/* 报告展示美化样式 */
#report-display-section { display: none; margin-top:20px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background-color: #fdfdfd; }
#report-display-section h3 { color: #0056b3; border-bottom: 1px solid #eee; padding-bottom: 10px; margin-top: 0;}
#report-display-section p, #report-display-section ul { margin-bottom: 10px; line-height: 1.6; }
#report-display-section ul { padding-left: 20px; }
#report-display-section strong { color: #333; }
.score-item { display: flex; justify-content: space-between; padding: 5px 0; border-bottom: 1px dotted #eee; }
.score-item:last-child { border-bottom: none; }
.constitution-name { font-weight:500; }
.constitution-score { font-weight: bold; color: #28a745;}
.close-report-button { display: block; margin: 20px auto 0; padding: 10px 20px; background-color: #6c757d; color:white; border:none; border-radius:5px; cursor:pointer;}
.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}
.score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #4CAF50;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
}
.report-section {
    margin-bottom: 25px;
    padding: 15px;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
.tea-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}
.tea-card {
    width: calc(50% - 10px);
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}
.tea-image {
    height: 120px;
    overflow: hidden;
}
.tea-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.tea-info {
    padding: 10px;
}
.tea-info h5 {
    margin: 0 0 5px 0;
    color: #333;
}
.tea-english-name {
    font-size: 0.8em;
    color: #666;
    margin: 0 0 8px 0;
}
.tea-description {
    font-size: 0.9em;
    color: #555;
}
.daily-advice-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}
.daily-advice-item {
    flex: 1;
    min-width: 200px;
    background: white;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.daily-advice-item h5 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}
.constitution-detail {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ddd;
}
.constitution-detail:last-child {
    border-bottom: none;
}
.recommendation-group {
    margin-bottom: 15px;
}
.recommendation-group h5 {
    margin-bottom: 8px;
    color: #333;
}
.recommendation-group ul {
    margin-top: 5px;
} 
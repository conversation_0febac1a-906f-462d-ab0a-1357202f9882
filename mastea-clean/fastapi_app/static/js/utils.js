/**
 * <PERSON><PERSON><PERSON> FastAPI 工具函数
 */

// 获取认证token
function getAuthToken() {
    return localStorage.getItem('admin_token') || localStorage.getItem('token');
}

// 获取认证请求头
function getAuthHeaders() {
    const token = getAuthToken();
    const headers = {
        'Content-Type': 'application/json'
    };
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    return headers;
}

// 创建带认证的fetch请求
async function authFetch(url, options = {}) {
    const token = getAuthToken();
    const headers = options.headers || {};
    
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    
    return fetch(url, {
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...headers
        }
    });
}

// 检查登录状态
async function checkLoginStatus() {
    const token = getAuthToken();
    if (!token) {
        return { loggedIn: false };
    }
    
    try {
        const response = await fetch('/api/v1/users/me', {
            headers: {
                'Authorization': `Bear<PERSON> ${token}`
            }
        });
        
        if (!response.ok) {
            return { loggedIn: false };
        }
        
        const data = await response.json();
        if (data && data.data) {
            return {
                loggedIn: true,
                user: data.data
            };
        }
        
        return { loggedIn: false };
    } catch (error) {
        console.error('检查登录状态失败:', error);
        return { loggedIn: false, error };
    }
}

// 导出所有工具函数
window.MasteaUtils = {
    getAuthToken,
    getAuthHeaders,
    authFetch,
    checkLoginStatus
}; 
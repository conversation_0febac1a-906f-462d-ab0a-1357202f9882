// === DOM Elements ===
const loadingDiv = document.getElementById('loading');
const errorMessageDiv = document.getElementById('error-message');
const reportListSection = document.getElementById('report-list-section');
const reportListUl = document.getElementById('report-list');
const complaintReportListUl = document.getElementById('complaint-report-list');
const startNewAssessmentButton = document.getElementById('start-new-assessment-button');

const questionnaireSection = document.getElementById('questionnaire-section');
const questionnaireForm = document.getElementById('constitution-questionnaire-form');
const submitQuestionnaireButton = document.getElementById('submit-questionnaire-button');
const cancelQuestionnaireButton = document.getElementById('cancel-questionnaire-button');

const chatSection = document.getElementById('chat-section');
const chatTitle = document.getElementById('chat-title');
const chatbox = document.getElementById('chatbox');
const chatLoading = document.getElementById('chat-loading');
const messageInput = document.getElementById('message-input');
const sendButton = document.getElementById('send-button');
const endAssessmentButton = document.getElementById('end-assessment-button');

const reportDisplaySection = document.getElementById('report-display-section');
const reportDisplayTitle = document.getElementById('report-display-title');
const reportDisplayContent = document.getElementById('report-display-content');
const closeReportDisplayButton = document.getElementById('close-report-display-button');

// === Global State ===
let currentUserId = null; 
let currentConversationId = null; // For chief complaint AI chat
let currentTaskType = null; // "constitution_assessment" (now questionnaire) or "chief_complaint" (AI chat)

// === Helper Functions ===
function showMainLoading(isLoading) { loadingDiv.style.display = isLoading ? 'block' : 'none'; }
function showChatLoading(isLoading) { chatLoading.style.display = isLoading ? 'block' : 'none'; }
function setInputDisabled(isDisabled) {
    messageInput.disabled = isDisabled;
    sendButton.disabled = isDisabled;
    if (currentConversationId && currentTaskType === 'chief_complaint') { 
        endAssessmentButton.style.display = isDisabled ? 'none' : 'block'; 
    } else {
        endAssessmentButton.style.display = 'none';
    }
    if (!isDisabled) { messageInput.focus(); }
}
function showError(message) {
    errorMessageDiv.textContent = message;
    errorMessageDiv.style.display = message ? 'block' : 'none';
    if (message) { showMainLoading(false); }
}
function addMessageToChatbox(text, isUser, timestamp = null) {
    const messageContainer = document.createElement('div');
    messageContainer.classList.add('message-container');
    const messageDiv = document.createElement('div');
    messageDiv.classList.add('message', isUser ? 'user-message' : 'ai-message');
    const contentP = document.createElement('p');
    contentP.textContent = text; contentP.style.margin = '0';
    messageDiv.appendChild(contentP);
    if (timestamp) {
         const timeSpan = document.createElement('span');
         timeSpan.classList.add('timestamp');
        try { timeSpan.textContent = new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } catch (e) { timeSpan.textContent = timestamp; }
         messageDiv.appendChild(timeSpan);
    }
    messageContainer.appendChild(messageDiv);
    chatbox.appendChild(messageContainer);
    chatbox.scrollTop = chatbox.scrollHeight;
    if (currentConversationId && currentTaskType === 'chief_complaint' && !messageInput.disabled) {
        endAssessmentButton.style.display = 'block';
    } else {
        endAssessmentButton.style.display = 'none';
    }
    return messageDiv;
}
function addSystemNotification(text, target = 'chatbox') {
    const notificationDiv = document.createElement('div');
    notificationDiv.classList.add('system-notification');
    notificationDiv.textContent = text;
    if (target === 'chatbox') {
        chatbox.appendChild(notificationDiv);
        chatbox.scrollTop = chatbox.scrollHeight;
    } else if (target === 'questionnaire') {
        // Prepend to questionnaireSection or specific area within it
        questionnaireSection.insertBefore(notificationDiv, questionnaireSection.firstChild);
    }
    if (text.includes("评估完成") || text.includes("正在连接AI") || text.includes("正在为您开启")){
         endAssessmentButton.style.display = 'none';
    }
}
function showQuestionnaire(show) {
    questionnaireSection.style.display = show ? 'block' : 'none';
    reportListSection.style.display = show ? 'none' : 'block';
    chatSection.style.display = 'none'; // Hide chat when questionnaire is up
    reportDisplaySection.style.display = 'none'; // Hide report display
    if(show) questionnaireForm.reset(); // Reset form when shown
}

// === API Interaction & Logic ===
async function ensureUserExists(userId) {
    try {
        // 尝试注册测试用户
        const username = `testuser_${userId.substring(-8)}`;
        const email = `${userId}@example.com`;
        const mobile = `138${userId.substring(-8)}`;
        
        const response = await fetch('/api/v1/auth/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                username: username,
                email: email,
                mobile: mobile,
                password: '123456'
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log("自动注册成功:", result);
            // 保存token到localStorage
            if (result.data && result.data.access_token) {
                localStorage.setItem('mastea_token', result.data.access_token);
            }
        } else {
            // 注册失败可能是因为用户已存在，这是正常的
            console.log("用户可能已存在，继续执行");
        }
    } catch (error) {
        console.warn("确保用户存在时出错:", error);
    }
}

async function initializeAssessmentPage() {
    console.log("Initializing assessment page...");
    showMainLoading(true); showError(null);
    questionnaireSection.style.display = 'none';
    reportListSection.style.display = 'none';
    chatSection.style.display = 'none';
    reportDisplaySection.style.display = 'none';
    setInputDisabled(true);

    // 先尝试获取现有用户ID
    currentUserId = localStorage.getItem('mastea_user_id');
    if (!currentUserId) {
        currentUserId = 'test_user_' + Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
        localStorage.setItem('mastea_user_id', currentUserId);
    }
    console.log("Using User ID:", currentUserId);
    
    // 确保用户在数据库中存在（自动注册测试用户）
    try {
        await ensureUserExists(currentUserId);
    } catch (error) {
        console.warn("自动创建用户失败，继续使用现有ID:", error);
    }

    try {
        if (typeof initialReportId !== 'undefined' && initialReportId && typeof initialReportType !== 'undefined' && initialReportType) {
            console.log(`Loading specific report: ${initialReportType} ${initialReportId}`);
            await viewReportDetail(initialReportId, initialReportType);
        } else {
            console.log("Loading report lists...");
            
            // 修正API路径，使用正确的 /api/v1/reports/ 路径
            const constitutionUrl = `/api/v1/reports/constitution/list?user_id=${currentUserId}`;
            const complaintUrl = `/api/v1/reports/complaint/list?user_id=${currentUserId}`;
            
            console.log(`Fetching constitution reports from: ${constitutionUrl}`);
            console.log(`Fetching complaint reports from: ${complaintUrl}`);
            
            const [constitutionResponse, complaintResponse] = await Promise.all([
                fetch(constitutionUrl),
                fetch(complaintUrl)
            ]);
            
            console.log("Constitution response status:", constitutionResponse.status);
            console.log("Complaint response status:", complaintResponse.status);
            
            if (!constitutionResponse.ok) {
                const errorData = await constitutionResponse.json().catch(() => ({}));
                console.error("Constitution API error:", errorData);
                throw new Error(errorData.detail || errorData.message || `请求基础报告失败: ${constitutionResponse.status}`);
            }
            const constitutionResult = await constitutionResponse.json();
            console.log("Constitution result:", constitutionResult);
            
            if (!complaintResponse.ok) {
                const errorData = await complaintResponse.json().catch(() => ({}));
                console.error("Complaint API error:", errorData);
                throw new Error(errorData.detail || errorData.message || `请求主诉报告失败: ${complaintResponse.status}`);
            }
            const complaintResult = await complaintResponse.json();
            console.log("Complaint result:", complaintResult);

            displayReportList(constitutionResult.data, 'constitution');
            displayReportList(complaintResult.data, 'complaint');
            
            reportListSection.style.display = 'block';
        }
    } catch (error) {
        console.error("Error loading initial data:", error);
        showError(`加载初始数据失败: ${error.message}`);
    } finally {
        showMainLoading(false);
    }
}

function displayReportList(reports, type) {
    const listUl = type === 'constitution' ? reportListUl : complaintReportListUl;
    listUl.innerHTML = '';
    if (reports && reports.length > 0) {
        reports.forEach(report => {
            const li = document.createElement('li');
            li.innerHTML = `
                <div>
                    <span class="report-title">${report.title} (${type === 'constitution' ? '基础体质' : '综合主诉'})</span>
                    <span class="report-date">创建于: ${new Date(report.created_at).toLocaleDateString()}</span>
                </div>
                <div class="report-actions">
                    <button class="view-button" data-report-id="${report.id}" data-report-type="${type}" title="查看报告详情">查看</button>
                    ${type === 'constitution' ? `<button class="assess-button" data-report-id="${report.id}" title="基于此报告开始主诉评估">主诉评估</button>` : ''}
                </div>
            `;
            li.querySelector('.view-button').addEventListener('click', () => viewReportDetail(report.id, type));
            if (type === 'constitution') {
            li.querySelector('.assess-button').addEventListener('click', () => startComplaintAssessment(report.id));
            }
            listUl.appendChild(li);
        });
    } else {
         listUl.innerHTML = `<li>您还没有历史${type === 'constitution' ? '基础体质' : '综合主诉'}报告。</li>`;
    }
}

function startNewConstitutionAssessmentViaQuestionnaire() {
    console.log("Starting new constitution assessment via questionnaire.");
    showError(null);
    showQuestionnaire(true);
    currentTaskType = "constitution_assessment";
}

cancelQuestionnaireButton.addEventListener('click', () => {
    showQuestionnaire(false);
    currentTaskType = null;
});

submitQuestionnaireButton.addEventListener('click', async () => {
    showMainLoading(true);
    showError(null);
    const answers = {
        gender: questionnaireForm.elements['gender'].value,
        age_group: questionnaireForm.elements['age_group'].value,
        q3_core_symptom: questionnaireForm.elements['q3_core_symptom'].value || null,
        q4_sweating: Array.from(questionnaireForm.elements['q4_sweating']).filter(cb => cb.checked).map(cb => cb.value),
        q5_stool: questionnaireForm.elements['q5_stool'].value || null,
        q6_sleep: questionnaireForm.elements['q6_sleep'].value || null,
        q7_season_response: Array.from(questionnaireForm.elements['q7_season_response']).filter(cb => cb.checked).map(cb => cb.value),
        q8_diet_preference: questionnaireForm.elements['q8_diet_preference'].value || null,
        q9_long_term_habits: Array.from(questionnaireForm.elements['q9_long_term_habits']).filter(cb => cb.checked).map(cb => cb.value),
        q10_body_type: questionnaireForm.elements['q10_body_type'].value || null,
        q11_skin_status: questionnaireForm.elements['q11_skin_status'].value || null,
        q12_priority_conditioning: questionnaireForm.elements['q12_priority_conditioning'].value || null,
    };
    
    const singleChoiceGroups = ['q3_core_symptom', 'q5_stool', 'q6_sleep', 'q8_diet_preference', 'q10_body_type', 'q11_skin_status', 'q12_priority_conditioning'];
    for (const groupName of singleChoiceGroups) {
        if (!answers[groupName]) {
            showError(`请完成所有问题，问题 "${document.querySelector(`input[name='${groupName}']`).closest('.question-group').querySelector('label').textContent}" 未选择。`);
            showMainLoading(false);
            return;
        }
    }

    console.log("Submitting questionnaire with answers:", answers);
    
    try {
        const url = '/api/v1/reports/constitution/calculate';
        console.log(`Submitting questionnaire to: ${url}`);
        
        // 修正提交的数据格式，使其与后端期望的匹配
        const reportData = {
            user_id: currentUserId,
            answers: answers
        };
        
        const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(reportData)
        });
        
        console.log("Questionnaire submission response status:", response.status);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Questionnaire submission error:", errorData);
            throw new Error(errorData.detail || errorData.message || `提交问卷失败: ${response.status}`);
        }
        
        const result = await response.json();
        console.log("Questionnaire submission result:", result);
        
        if (result.id) {
            addSystemNotification("基础体质报告已成功生成！", 'questionnaire');
            showQuestionnaire(false);
            await initializeAssessmentPage();
            // 查看新创建的报告详情
            await viewReportDetail(result.id, 'constitution');
        } else {
            throw new Error(result.message || result.detail || "提交问卷时返回数据格式不正确。");
        }
    } catch (error) {
        console.error("Error submitting questionnaire:", error);
        showError(`提交问卷失败: ${error.message}`);
    } finally {
        showMainLoading(false);
    }
});

function displayDetailedReport(reportData, type) {
    reportListSection.style.display = 'none';
    questionnaireSection.style.display = 'none';
    chatSection.style.display = 'none';
    reportDisplaySection.style.display = 'block';

    reportDisplayTitle.textContent = `${type === 'constitution' ? '基础体质' : '综合主诉'}报告详情 (${reportData.report_type || 'N/A'})`;
    let contentHtml = '';

    if (reportData.title) {
        contentHtml += `<div class="report-header">
            <h2>${reportData.title || '体质评估报告'}</h2>
            ${reportData.score ? `<div class="score-circle">${reportData.score}</div>` : ''}
        </div>`;
    }

    contentHtml += `<p><strong>评估日期:</strong> ${new Date(reportData.assessment_date).toLocaleString()}</p>`;
    if (reportData.user_info) {
        contentHtml += `<h3>基本信息</h3>`;
        contentHtml += `<p><strong>性别:</strong> ${reportData.user_info.gender || '未提供'}</p>`;
        contentHtml += `<p><strong>年龄段:</strong> ${reportData.user_info.age_group || '未提供'}</p>`;
        if (reportData.user_info.name) {
            contentHtml += `<p><strong>姓名:</strong> ${reportData.user_info.name}</p>`;
        }
    }

    if (type === 'constitution') {
        if (reportData.dominant_constitution) {
            contentHtml += `<h3>主要体质</h3>`;
            contentHtml += `<p><strong>名称:</strong> ${reportData.dominant_constitution.name} (得分: ${reportData.dominant_constitution.score})</p>`;
            if (reportData.dominant_constitution.description) {
                contentHtml += `<p>${reportData.dominant_constitution.description}</p>`;
            }
        }
        if (reportData.secondary_constitutions && reportData.secondary_constitutions.length > 0) {
            contentHtml += `<h3>次要体质</h3><ul>`;
            reportData.secondary_constitutions.forEach(sc => {
                contentHtml += `<li>${sc.name} (得分: ${sc.score})</li>`;
            });
            contentHtml += `</ul>`;
        }
        if (reportData.constitution_scores) {
            contentHtml += `<h3>各项体质评分</h3><ul>`;
            const nameMap = {
                "qi_deficiency": "气虚质", "yang_deficiency": "阳虚质", "yin_deficiency": "阴虚质",
                "phlegm_dampness": "痰湿质", "damp_heat": "湿热质", "blood_stasis": "血瘀质",
                "qi_stagnation": "气郁质", "special_constitution": "特禀质", "peaceful_constitution": "平和质"
            };
            for (const [key, value] of Object.entries(reportData.constitution_scores)) {
                let displayName = nameMap[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                contentHtml += `<li class="score-item"><span class="constitution-name">${displayName}:</span> <span class="constitution-score">${value}</span></li>`;
            }
            contentHtml += `</ul>`;
        }
         if (reportData.priority_conditioning_selection) {
            contentHtml += `<p><strong>用户希望优先调理:</strong> ${reportData.priority_conditioning_selection}</p>`;
        }
        if (reportData.preliminary_advice && reportData.preliminary_advice.general_notes) {
             contentHtml += `<h3>初步建议</h3><p>${reportData.preliminary_advice.general_notes}</p>`;
        }
         if (reportData.user_selections) {
            contentHtml += `<h3>用户选择摘要</h3><ul>`;
            for (const [key, value] of Object.entries(reportData.user_selections)) {
                let displayKey = key.startsWith('q') ? key.substring(3).replace(/_/g, ' ') : key;
                displayKey = displayKey.charAt(0).toUpperCase() + displayKey.slice(1);
                let displayValue = Array.isArray(value) ? value.join(', ') : value;
                if(displayValue) contentHtml += `<li><strong>${displayKey}:</strong> ${displayValue}</li>`;
            }
            contentHtml += `</ul>`;
        }
    } else if (type === 'complaint') {
        const task3Report = reportData.task3_report || reportData;
        if (task3Report) {
            contentHtml += `<h3>综合主诉评估详情</h3>`;
            
            let chiefComplaintObj = task3Report.chief_complaint;
            if (!chiefComplaintObj && typeof task3Report.primary === 'string') {
                chiefComplaintObj = {
                    primary: task3Report.primary,
                    secondary: task3Report.secondary || []
                };
            }
            if (chiefComplaintObj) {
                contentHtml += `<div class="report-section complaint-section">
                    <h4>主诉信息</h4>
                    <p><strong>主要主诉:</strong> ${chiefComplaintObj.primary || '未提供'}</p>`;
                if (chiefComplaintObj.secondary && chiefComplaintObj.secondary.length > 0) {
                    contentHtml += `<p><strong>次要主诉:</strong> ${Array.isArray(chiefComplaintObj.secondary) ? chiefComplaintObj.secondary.join(', ') : chiefComplaintObj.secondary}</p>`;
                }
                if (chiefComplaintObj.document3_mapping && chiefComplaintObj.document3_mapping.symptoms) {
                    contentHtml += `<p><strong>关联症状 (问卷映射):</strong> ${chiefComplaintObj.document3_mapping.symptoms.join(', ')}</p>`;
                }
                contentHtml += `</div>`;
            }

            let tcmAnalysisObj = task3Report.tcm_analysis;
            if (!tcmAnalysisObj && task3Report.current_pattern_differentiation) {
                tcmAnalysisObj = {
                    current_pattern_differentiation: task3Report.current_pattern_differentiation
                };
            }
            if (tcmAnalysisObj) {
                contentHtml += `<div class="report-section tcm-analysis-section">
                    <h4>中医辨证分析</h4>`;
                if (tcmAnalysisObj.current_pattern_differentiation) {
                    contentHtml += `<p><strong>当前证候分型:</strong> ${tcmAnalysisObj.current_pattern_differentiation}</p>`;
                }
                if (tcmAnalysisObj.constitution_review) {
                    contentHtml += `<p><strong>体质回顾:</strong> ${tcmAnalysisObj.constitution_review}</p>`;
                }
                if (tcmAnalysisObj.etiology) {
                    contentHtml += `<p><strong>病因分析:</strong> ${tcmAnalysisObj.etiology}</p>`;
                }
                contentHtml += `</div>`;
            }

            if (task3Report.constitution_radar) {
                contentHtml += `<div class="report-section radar-section">
                    <h4>体质雷达图</h4>
                    <canvas id="constitution-radar-chart" style="max-width: 400px; max-height: 400px; margin: auto;"></canvas>
                </div>`;
                
                setTimeout(() => {
                    try {
                        const radarData = task3Report.constitution_radar;
                        const labels = Object.keys(radarData);
                        const values = Object.values(radarData);
                        
                        const ctx = document.getElementById('constitution-radar-chart');
                        if(ctx) {
                            new Chart(ctx.getContext('2d'), {
                                type: 'radar',
                                data: {
                                    labels: labels,
                                    datasets: [{
                                        label: '体质得分',
                                        data: values,
                                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                        borderColor: 'rgba(54, 162, 235, 1)',
                                        borderWidth: 2,
                                        pointBackgroundColor: 'rgba(54, 162, 235, 1)'
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: true,
                                    scales: {
                                        r: {
                                            beginAtZero: true,
                                            max: 100
                                        }
                                    },
                                    plugins: { legend: { display: false } }
                                }
                            });
                        }
                    } catch (e) {
                        console.error("Error rendering radar chart:", e);
                        document.getElementById('constitution-radar-chart').innerHTML = "<p>雷达图加载失败。</p>";
                    }
                }, 100);
            } else if (task3Report.constitution_scores) {
                contentHtml += `<div class="report-section scores-section"><h4>体质评分回顾</h4><ul>`;
                const nameMap = {
                    "qi_deficiency": "气虚质", "yang_deficiency": "阳虚质", "yin_deficiency": "阴虚质",
                    "phlegm_dampness": "痰湿质", "damp_heat": "湿热质", "blood_stasis": "血瘀质",
                    "qi_stagnation": "气郁质", "special_constitution": "特禀质", "peaceful_constitution": "平和质"
                };
                for (const [key, valueObj] of Object.entries(task3Report.constitution_scores)) {
                    let scoreDisplay = 'N/A';
                    if (typeof valueObj === 'object' && valueObj !== null && 'final' in valueObj) scoreDisplay = valueObj.final;
                    else if (typeof valueObj === 'number') scoreDisplay = valueObj;
                    else if (typeof valueObj === 'object' && valueObj !== null && 'score' in valueObj) scoreDisplay = valueObj.score;
                    else if (typeof valueObj === 'object' && valueObj !== null && 'base' in valueObj) scoreDisplay = valueObj.base;
                    const displayName = nameMap[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    contentHtml += `<li class="score-item"><span class="constitution-name">${displayName}:</span> <span class="constitution-score">${scoreDisplay}</span></li>`;
                }
                contentHtml += `</ul></div>`;
            }

            let managementPlanObj = task3Report.management_plan;
            if (!managementPlanObj) {
                managementPlanObj = {};
                if (task3Report.dietary_recommendations) managementPlanObj.dietary_recommendations = task3Report.dietary_recommendations;
                if (task3Report.lifestyle_advice) managementPlanObj.lifestyle_advice = task3Report.lifestyle_advice;
                if (task3Report.follow_up_recommendations) managementPlanObj.follow_up_recommendations = task3Report.follow_up_recommendations;
            }
            
            if (managementPlanObj && Object.keys(managementPlanObj).length > 0) {
                contentHtml += `<div class="report-section management-plan-section"><h4>调理建议方案</h4>`;
                if (managementPlanObj.dietary_recommendations) {
                    contentHtml += `<div class="recommendation-group"><h5>饮食建议</h5><ul>`;
                    const recommendations = Array.isArray(managementPlanObj.dietary_recommendations) ? managementPlanObj.dietary_recommendations : [managementPlanObj.dietary_recommendations];
                    recommendations.forEach(rec => { contentHtml += `<li>${rec}</li>`; });
                    contentHtml += `</ul></div>`;
                }
                if (managementPlanObj.lifestyle_advice) {
                    contentHtml += `<div class="recommendation-group"><h5>生活建议</h5><ul>`;
                    const advice = Array.isArray(managementPlanObj.lifestyle_advice) ? managementPlanObj.lifestyle_advice : [managementPlanObj.lifestyle_advice];
                    advice.forEach(item => { contentHtml += `<li>${item}</li>`; });
                    contentHtml += `</ul></div>`;
                }
                if (managementPlanObj.follow_up_recommendations) {
                    contentHtml += `<p><strong>后续跟进:</strong> ${managementPlanObj.follow_up_recommendations}</p>`;
                }
                contentHtml += `</div>`;
            }
            
            if (task3Report.recommended_teas && task3Report.recommended_teas.length > 0) {
                contentHtml += `<div class="report-section teas-section"><h4>推荐茶饮</h4><div class="tea-cards-container">`;
                task3Report.recommended_teas.forEach(tea => {
                    contentHtml += `<div class="tea-card">
                        ${tea.image_url ? `<div class="tea-image"><img src="${tea.image_url}" alt="${tea.chinese_name}"></div>` : ''}
                        <div class="tea-info">
                            <h5>${tea.chinese_name || tea.name}</h5>
                            ${tea.name && tea.chinese_name ? `<p class="tea-english-name">${tea.name}</p>` : ''}
                            <p class="tea-description">${tea.description || ''}</p>
                        </div>
                    </div>`;
                });
                contentHtml += `</div></div>`;
            }
        } else {
            contentHtml += `<p>主诉报告数据格式不完整或未能正确解析，原始数据如下：</p>`;
            contentHtml += `<pre>${JSON.stringify(reportData, null, 2)}</pre>`;
        }
    }
    reportDisplayContent.innerHTML = contentHtml;
}

closeReportDisplayButton.addEventListener('click', () => {
    reportDisplaySection.style.display = 'none';
    reportListSection.style.display = 'block';
});

async function viewReportDetail(reportId, type) {
    showMainLoading(true); showError(null);
    console.log(`Viewing ${type} report detail:`, reportId);
    try {
        // 修正API路径，使用正确的路由格式
        const url = `/api/v1/reports/${type}/${reportId}`;
        console.log(`Fetching report detail from: ${url}`);
        
        const response = await fetch(url);
        console.log("Report detail response status:", response.status);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Report detail error:", errorData);
            throw new Error(errorData.detail || errorData.message || "无法加载报告详情");
        }
        
        const result = await response.json();
        console.log("Report detail result:", result);
        
        if (result.code === 0 && result.data) {
            displayDetailedReport(result.data, type);
        } else if (result && result.id) {
            // FastAPI直接返回报告数据，没有code字段
            displayDetailedReport(result, type);
        } else {
            throw new Error(result.message || result.detail || "加载报告详情失败。");
        }
    } catch (error) {
        showError(`查看报告失败: ${error.message}`);
        console.error("Error viewing report:", error);
    } finally {
        showMainLoading(false);
    }
}

async function startComplaintAssessment(baseReportId) {
    showMainLoading(true); showError(null);
    questionnaireSection.style.display = 'none';
    reportListSection.style.display = 'none';
    reportDisplaySection.style.display = 'none';
    chatSection.style.display = 'block';
    chatbox.innerHTML = '';
    addSystemNotification(`正在基于报告 ${baseReportId} 准备主诉评估...`);
    currentTaskType = "chief_complaint";

    try {
        const url = '/api/v1/reports/complaint/start';
        console.log(`Starting complaint assessment with: ${url}`);
        
        const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ user_id: currentUserId, base_constitution_report_id: baseReportId })
        });
        
        console.log("Complaint assessment start response status:", response.status);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Complaint assessment start error:", errorData);
            throw new Error(errorData.detail || errorData.message || `开启主诉评估失败`);
        }
        
        const result = await response.json();
        console.log("Complaint assessment start result:", result);
        
        if (result.code === 0 && result.data) {
            currentConversationId = result.data.conversation_id;
            chatTitle.textContent = "综合主诉评估进行中...";
            await triggerInitialAIMessage(); 
        } else {
            throw new Error(result.message || "开启主诉评估时返回数据格式不正确。");
        }
    } catch (error) {
        console.error("Error starting complaint assessment:", error);
        showError(`开启主诉评估失败: ${error.message}`);
        chatSection.style.display = 'none';
        reportListSection.style.display = 'block'; 
    } finally {
        showMainLoading(false);
    }
}

let currentAIMessage = "";
let currentMessageComplete = true;

function updateChatboxWithCurrentAI() {
    let lastAIMessageDiv = chatbox.querySelector('.ai-message:last-child');
    
    if (currentMessageComplete || !lastAIMessageDiv) {
        lastAIMessageDiv = addMessageToChatbox(currentAIMessage, false, new Date().toISOString());
        currentMessageComplete = false;
    } else {
        lastAIMessageDiv.querySelector('p').textContent = currentAIMessage;
    }
    
    chatbox.scrollTop = chatbox.scrollHeight;
}

async function handleChiefComplaintStream(messageToSend = null) {
    if (!currentUserId || !currentConversationId) {
        showError("缺少用户ID或对话ID，无法发送消息。");
        return;
    }
    
    const userInput = messageToSend !== null ? messageToSend : messageInput.value.trim();
    if (!userInput) return;
    
    if (messageToSend === null) {
        addMessageToChatbox(userInput, true, new Date().toISOString());
    }
    messageInput.value = '';
    showChatLoading(true);
    setInputDisabled(true);
    
    currentAIMessage = "";
    currentMessageComplete = true; // Set to true to create a new message bubble

    try {
        const url = '/api/v1/chat/send_stream';
        console.log(`Sending message to: ${url}`);
        console.log(`Message payload: user_id=${currentUserId}, conversation_id=${currentConversationId}, message=${userInput}`);
        
        const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                user_id: currentUserId,
                conversation_id: currentConversationId,
                message: userInput,
                task_type: "chief_complaint"
            })
        });
        
        console.log("Message send response status:", response.status);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error("Message send error:", errorData);
            throw new Error(errorData.detail || errorData.message || `请求失败，状态码: ${response.status}`);
        }
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
            const { value, done } = await reader.read();
            if (done) {
                break;
            }
            
            const chunk = decoder.decode(value, { stream: true });
            const sseMessages = chunk.split('\n\n');
            
            for (const sseMessage of sseMessages) {
                if (!sseMessage.trim() || !sseMessage.startsWith('data: ')) continue;
                
                try {
                    const eventDataStr = sseMessage.substring(6);
                    const eventData = JSON.parse(eventDataStr);
                    await handleStreamData(eventData);
                } catch (e) {
                    console.error("解析流数据时出错:", e, "数据:", sseMessage);
                }
            }
        }
    } catch (error) {
        console.error("发送消息时出错:", error);
        showError(`发送消息失败: ${error.message}`);
        addSystemNotification(`消息发送失败，请重试或刷新页面。`);
    } finally {
        showChatLoading(false);
        setInputDisabled(false);
    }
}

async function handleStreamData(data) {
    if (!data || !data.type) return;

    if (data.type === "ai_chunk") {
        currentAIMessage += data.data.content || "";
        updateChatboxWithCurrentAI();
    } else if (data.type === "stream_end") {
        currentMessageComplete = true;
        showChatLoading(false);
        setInputDisabled(false);
    } else if (data.type === "task_complete") {
        currentMessageComplete = true;
        updateChatboxWithCurrentAI();
        addSystemNotification(`✅ 主诉评估报告已成功保存！ID: ${data.data.report_id}`);
        setInputDisabled(true);
        endAssessmentButton.style.display = 'none';
        
        addSystemNotification("即将为您跳转到报告详情页...");
        
        setTimeout(() => {
            window.location.href = `/report/complaint/${data.data.report_id}`;
        }, 2000); 
    } else if (data.type === "error") {
        console.error("Stream error:", data.data.message);
        showError(data.data.message || "处理消息时出错");
        currentMessageComplete = true;
        showChatLoading(false);
        setInputDisabled(false);
    }
}

async function triggerInitialAIMessage() {
    if (!currentConversationId || !currentTaskType) {
        showError("无法触发初始AI消息：缺少会话ID或任务类型。");
        setInputDisabled(false); 
        return;
    }
    const initialTriggerMessage = "<START_ASSESSMENT>";
    await handleChiefComplaintStream(initialTriggerMessage);
}

sendButton.addEventListener('click', () => handleChiefComplaintStream());
messageInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter' && !sendButton.disabled) {
        handleChiefComplaintStream();
    }
});
startNewAssessmentButton.addEventListener('click', startNewConstitutionAssessmentViaQuestionnaire);
endAssessmentButton.addEventListener('click', async () => {
    if (!currentConversationId || currentTaskType !== 'chief_complaint') {
        showError("没有活动的综合主诉评估任务可结束。"); 
        return;
    }
    addSystemNotification("正在尝试结束对话并生成报告...");
    showChatLoading(true); 
    setInputDisabled(true); 
    endAssessmentButton.style.display = 'none';
    await handleChiefComplaintStream("<REQUEST_REPORT_GENERATION>"); 
});

console.log("Report assessment script loaded!");
document.addEventListener('DOMContentLoaded', () => {
    console.log("DOM content loaded, initializing assessment page...");
    initializeAssessmentPage();
});
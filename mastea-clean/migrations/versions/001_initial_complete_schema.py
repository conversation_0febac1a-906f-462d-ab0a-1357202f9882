"""Initial complete schema

Revision ID: 001_initial_complete_schema
Revises: 
Create Date: 2025-07-19 16:55:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_initial_complete_schema'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create users table with all fields
    op.create_table('users',
        sa.Column('id', sa.String(length=32), nullable=False),
        sa.Column('username', sa.String(length=64), nullable=False),
        sa.Column('email', sa.String(length=120), nullable=True),
        sa.Column('password_hash', sa.String(length=128), nullable=True),
        sa.Column('mobile', sa.String(length=20), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('gender', sa.String(length=10), nullable=True),
        sa.Column('age', sa.Integer(), nullable=True),
        sa.Column('height', sa.Float(), nullable=True),
        sa.Column('weight', sa.Float(), nullable=True),
        sa.Column('is_pregnant', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('is_preparing_pregnancy', sa.Boolean(), nullable=False, server_default='false'),
        
        # WeChat related fields
        sa.Column('wechat_openid', sa.String(length=64), nullable=True),
        sa.Column('wechat_unionid', sa.String(length=64), nullable=True),
        sa.Column('wechat_nickname', sa.String(length=100), nullable=True),
        sa.Column('wechat_avatar', sa.String(length=256), nullable=True),
        sa.Column('wechat_authorized', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('wechat_auth_time', sa.DateTime(), nullable=True),
        sa.Column('is_wechat_user', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('last_login_at', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        
        # Extended user info fields
        sa.Column('real_name', sa.String(length=50), nullable=True),
        sa.Column('address', sa.String(length=200), nullable=True),
        sa.Column('zip_code', sa.String(length=10), nullable=True),
        sa.Column('avatar_url', sa.String(length=256), nullable=True),
        sa.Column('is_member', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('membership_start_date', sa.DateTime(), nullable=True),
        sa.Column('membership_end_date', sa.DateTime(), nullable=True),
        
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('username'),
        sa.UniqueConstraint('email'),
        sa.UniqueConstraint('mobile'),
        sa.UniqueConstraint('wechat_openid'),
        sa.UniqueConstraint('wechat_unionid')
    )
    
    # Create indexes for users table
    op.create_index('ix_users_wechat_openid', 'users', ['wechat_openid'], unique=True)
    op.create_index('ix_users_wechat_unionid', 'users', ['wechat_unionid'], unique=True)
    
    # Create addresses table
    op.create_table('addresses',
        sa.Column('id', sa.String(length=32), nullable=False),
        sa.Column('user_id', sa.String(length=32), nullable=False),
        sa.Column('name', sa.String(length=50), nullable=False),
        sa.Column('phone', sa.String(length=20), nullable=False),
        sa.Column('province', sa.String(length=50), nullable=False),
        sa.Column('city', sa.String(length=50), nullable=False),
        sa.Column('district', sa.String(length=50), nullable=False),
        sa.Column('detail', sa.String(length=200), nullable=False),
        sa.Column('is_default', sa.Boolean(), nullable=False, server_default='false'),
        sa.Column('create_time', sa.DateTime(), nullable=False),
        sa.Column('update_time', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create conversations table
    op.create_table('conversations',
        sa.Column('id', sa.String(length=32), nullable=False),
        sa.Column('user_id', sa.String(length=32), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create chat_messages table
    op.create_table('chat_messages',
        sa.Column('id', sa.String(length=32), nullable=False),
        sa.Column('conversation_id', sa.String(length=32), nullable=True),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('is_user', sa.Boolean(), nullable=False),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create constitution_reports table
    op.create_table('constitution_reports',
        sa.Column('id', sa.String(length=32), nullable=False),
        sa.Column('user_id', sa.String(length=32), nullable=False),
        sa.Column('report_data', sa.JSON(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create complaint_reports table
    op.create_table('complaint_reports',
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('user_id', sa.String(length=36), nullable=False),
        sa.Column('base_constitution_report_id', sa.String(length=36), nullable=True),
        sa.Column('complaint_data', sa.JSON(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['base_constitution_report_id'], ['constitution_reports.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    op.drop_table('complaint_reports')
    op.drop_table('constitution_reports')
    op.drop_table('chat_messages')
    op.drop_table('conversations')
    op.drop_table('addresses')
    op.drop_index('ix_users_wechat_unionid', table_name='users')
    op.drop_index('ix_users_wechat_openid', table_name='users')
    op.drop_table('users')

# Mastea Flask - Clean Version Docker Compose
services:
  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: mastea_postgres_clean
    environment:
      POSTGRES_USER: mastea
      POSTGRES_PASSWORD: wyh12257410
      POSTGRES_DB: mastea_prod
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"  # 避免与本地PostgreSQL冲突
    networks:
      - mastea_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mastea -d mastea_prod"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI应用服务
  fastapi_app:
    image: mastea-fastapi-clean:latest
    container_name: mastea_fastapi_clean
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=*********************************************/mastea_prod
      - SECRET_KEY=your-secret-key-here
      - WECHAT_APP_ID=your-wechat-app-id
      - WECHAT_APP_SECRET=your-wechat-app-secret
      - AI_API_KEY=your-ai-api-key
      - AI_API_BASE_URL=https://api.openai.com/v1
      - ENVIRONMENT=production
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    ports:
      - "8001:8001"
    networks:
      - mastea_network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# 网络配置
networks:
  mastea_network:
    driver: bridge

# 数据卷配置
volumes:
  postgres_data:
    driver: local

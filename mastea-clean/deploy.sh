#!/bin/bash
# Mastea Flask - Clean Version Deployment Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

DOCKER_COMPOSE_CMD="docker compose"

log_info "=== Mastea Flask Clean Version Deployment ==="

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! $DOCKER_COMPOSE_CMD version &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 清理旧容器和镜像
cleanup_old() {
    log_info "清理旧的容器和镜像..."
    
    # 停止并删除旧的mastea容器
    docker ps -a | grep mastea | awk '{print $1}' | xargs -r docker stop 2>/dev/null || true
    docker ps -a | grep mastea | awk '{print $1}' | xargs -r docker rm 2>/dev/null || true
    
    # 删除旧的mastea镜像
    docker images | grep mastea | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true
    
    log_success "旧容器和镜像清理完成"
}

# 构建和启动服务
deploy_services() {
    log_info "构建和启动服务..."
    
    # 构建镜像
    $DOCKER_COMPOSE_CMD build
    
    # 启动服务
    $DOCKER_COMPOSE_CMD up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待数据库就绪
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if $DOCKER_COMPOSE_CMD exec -T postgres pg_isready -U mastea -d mastea_prod &>/dev/null; then
            log_success "数据库已就绪"
            break
        fi
        
        log_info "等待数据库启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "数据库启动超时"
        exit 1
    fi
    
    # 等待应用就绪
    sleep 10
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    $DOCKER_COMPOSE_CMD exec -T fastapi_app bash -c "cd migrations && alembic upgrade head"
    
    if [ $? -eq 0 ]; then
        log_success "数据库迁移完成"
    else
        log_error "数据库迁移失败"
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查容器状态
    if ! $DOCKER_COMPOSE_CMD ps | grep -q "Up"; then
        log_error "容器启动失败"
        $DOCKER_COMPOSE_CMD logs
        exit 1
    fi
    
    # 检查应用健康状态
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8001/api/v1/health &>/dev/null; then
            log_success "应用健康检查通过"
            break
        fi
        
        log_info "等待应用启动... ($attempt/$max_attempts)"
        sleep 3
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_warning "应用健康检查失败，请手动验证"
    fi
    
    # 验证关键字段
    local wechat_fields=$($DOCKER_COMPOSE_CMD exec -T postgres psql -U mastea -d mastea_prod -t -c "SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'wechat_authorized';" | tr -d ' \n')
    
    if [ "$wechat_fields" = "wechat_authorized" ]; then
        log_success "数据库字段验证通过"
    else
        log_error "数据库字段验证失败"
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    local server_ip=$(curl -s ifconfig.me 2>/dev/null || hostname -I | awk '{print $1}')
    
    log_success "=== 部署完成！ ==="
    echo ""
    log_info "访问地址:"
    log_success "  应用首页: http://${server_ip}:8001"
    log_success "  API文档: http://${server_ip}:8001/api/v1/docs"
    log_success "  管理界面: http://${server_ip}:8001/dashboard_enhanced"
    log_success "  健康检查: http://${server_ip}:8001/api/v1/health"
    
    echo ""
    log_info "常用管理命令:"
    log_info "  查看日志: $DOCKER_COMPOSE_CMD logs -f"
    log_info "  重启服务: $DOCKER_COMPOSE_CMD restart"
    log_info "  停止服务: $DOCKER_COMPOSE_CMD down"
    
    echo ""
    log_info "服务状态:"
    $DOCKER_COMPOSE_CMD ps
}

# 主函数
main() {
    check_docker
    cleanup_old
    deploy_services
    wait_for_services
    run_migrations
    verify_deployment
    show_deployment_info
}

# 执行主函数
main "$@"

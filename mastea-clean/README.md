# Mastea Flask - Clean Version

这是Mastea Flask项目的干净重构版本，解决了之前版本中的迁移混乱和配置问题。

## 🎯 特性

- **干净的数据库迁移**: 单一完整的迁移文件，包含所有必要字段
- **简化的配置**: 统一的Docker配置，避免版本冲突
- **完整的微信集成**: 包含所有微信相关字段（wechat_authorized等）
- **健康检查**: 完整的应用和数据库健康检查
- **一键部署**: 自动化部署脚本

## 📁 项目结构

```
mastea-clean/
├── fastapi_app/          # 应用代码
├── migrations/           # 数据库迁移
│   └── versions/
│       └── 001_initial_complete_schema.py
├── init-scripts/         # 数据库初始化脚本
├── logs/                 # 日志目录
├── uploads/              # 上传文件目录
├── Dockerfile            # 应用镜像配置
├── docker-compose.yml    # 容器编排配置
├── alembic.ini          # 数据库迁移配置
├── .env.docker          # 环境变量配置
├── requirements.txt      # Python依赖
├── run_fastapi.py       # 应用启动脚本
├── deploy.sh            # 一键部署脚本
└── README.md            # 项目文档
```

## 🚀 快速开始

### 本地开发

1. **克隆项目**
   ```bash
   cd mastea-clean
   ```

2. **配置环境变量**
   ```bash
   cp .env.docker .env
   # 编辑.env文件，设置你的配置
   ```

3. **启动服务**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

4. **访问应用**
   - 应用首页: http://localhost:8001
   - API文档: http://localhost:8001/api/v1/docs
   - 管理界面: http://localhost:8001/dashboard_enhanced

### 服务器部署

1. **上传到服务器**
   ```bash
   tar -czf mastea-clean.tar.gz mastea-clean/
   scp mastea-clean.tar.gz root@your-server:/opt/
   ```

2. **在服务器上部署**
   ```bash
   ssh root@your-server
   cd /opt
   tar -xzf mastea-clean.tar.gz
   cd mastea-clean
   chmod +x deploy.sh
   sudo ./deploy.sh
   ```

## 🔧 配置说明

### 环境变量

主要配置项在`.env.docker`文件中：

- `DATABASE_URL`: 数据库连接字符串
- `SECRET_KEY`: 应用密钥
- `WECHAT_APP_ID`: 微信小程序ID
- `WECHAT_APP_SECRET`: 微信小程序密钥
- `AI_API_KEY`: AI服务API密钥

### 端口配置

- **应用端口**: 8001
- **数据库端口**: 5433 (避免与本地PostgreSQL冲突)

## 🗄️ 数据库

### 迁移管理

项目使用Alembic进行数据库迁移管理：

```bash
# 查看当前迁移状态
docker compose exec fastapi_app bash -c "cd migrations && alembic current"

# 升级到最新版本
docker compose exec fastapi_app bash -c "cd migrations && alembic upgrade head"

# 创建新的迁移
docker compose exec fastapi_app bash -c "cd migrations && alembic revision --autogenerate -m 'description'"
```

### 数据库表

主要数据表：
- `users`: 用户信息（包含完整的微信字段）
- `addresses`: 用户地址
- `conversations`: 对话记录
- `chat_messages`: 聊天消息
- `constitution_reports`: 体质报告
- `complaint_reports`: 主诉报告

## 🛠️ 开发指南

### 添加新功能

1. 修改模型文件 (`fastapi_app/models/`)
2. 创建数据库迁移
3. 更新API接口 (`fastapi_app/api/`)
4. 测试功能

### 常用命令

```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 进入容器
docker compose exec fastapi_app bash
docker compose exec postgres psql -U mastea -d mastea_prod
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8001
   # 修改docker-compose.yml中的端口映射
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker compose exec postgres pg_isready -U mastea
   ```

3. **迁移失败**
   ```bash
   # 查看迁移状态
   docker compose exec fastapi_app bash -c "cd migrations && alembic current"
   # 查看迁移历史
   docker compose exec fastapi_app bash -c "cd migrations && alembic history"
   ```

## 📝 更新日志

### v1.0.0 (2025-07-19)
- 重构项目结构
- 统一数据库迁移
- 简化Docker配置
- 添加完整的微信字段支持
- 实现一键部署脚本

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目采用MIT许可证。

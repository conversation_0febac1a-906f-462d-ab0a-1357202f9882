#!/bin/bash
# Mastea Flask Docker构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理旧的Docker镜像..."
    
    # 停止并删除旧容器
    docker-compose down --remove-orphans 2>/dev/null || true
    
    # 删除旧镜像
    docker rmi mastea_flask_fastapi_app 2>/dev/null || true
    docker rmi mastea-fastapi:latest 2>/dev/null || true
    
    # 清理未使用的镜像
    docker image prune -f
    
    log_success "旧镜像清理完成"
}

# 构建Docker镜像
build_images() {
    log_info "开始构建Docker镜像..."
    
    # 构建FastAPI应用镜像
    log_info "构建FastAPI应用镜像..."
    docker build -t mastea-fastapi:latest .
    
    if [ $? -eq 0 ]; then
        log_success "FastAPI镜像构建成功"
    else
        log_error "FastAPI镜像构建失败"
        exit 1
    fi
    
    # 标记镜像
    docker tag mastea-fastapi:latest mastea-fastapi:v1.0
    
    log_success "所有镜像构建完成"
}

# 验证镜像
verify_images() {
    log_info "验证Docker镜像..."
    
    if docker images | grep -q "mastea-fastapi"; then
        log_success "FastAPI镜像验证成功"
        docker images | grep mastea-fastapi
    else
        log_error "FastAPI镜像验证失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始Mastea Flask Docker构建流程..."
    
    # 检查环境
    check_docker
    
    # 清理旧镜像
    cleanup_old_images
    
    # 构建镜像
    build_images
    
    # 验证镜像
    verify_images
    
    log_success "Docker镜像构建完成！"
    log_info "使用以下命令启动服务："
    log_info "  docker-compose up -d"
    log_info "或者使用部署脚本："
    log_info "  ./scripts/deploy.sh"
}

# 执行主函数
main "$@"

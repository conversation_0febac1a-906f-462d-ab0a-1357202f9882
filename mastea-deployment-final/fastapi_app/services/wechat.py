import httpx
import logging
from typing import Optional, Dict, Any
from fastapi import HTTPException
from fastapi_app.core.config import settings

logger = logging.getLogger(__name__)

class WeChatService:
    """微信登录服务"""
    
    def __init__(self):
        # 这些配置应该从环境变量或配置文件中获取
        self.app_id = getattr(settings, 'WECHAT_APP_ID', 'your_wechat_app_id')
        self.app_secret = getattr(settings, 'WECHAT_APP_SECRET', 'your_wechat_app_secret')
        self.access_token_url = "https://api.weixin.qq.com/sns/oauth2/access_token"
        self.user_info_url = "https://api.weixin.qq.com/sns/userinfo"
        self.refresh_token_url = "https://api.weixin.qq.com/sns/oauth2/refresh_token"
        self.check_token_url = "https://api.weixin.qq.com/sns/auth"
    
    async def get_access_token(self, code: str) -> Dict[str, Any]:
        """
        通过授权码获取access_token
        
        Args:
            code: 微信授权码
            
        Returns:
            包含access_token等信息的字典
            
        Raises:
            HTTPException: 当获取token失败时
        """
        params = {
            'appid': self.app_id,
            'secret': self.app_secret,
            'code': code,
            'grant_type': 'authorization_code'
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.access_token_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # 检查是否有错误
                if 'errcode' in data:
                    error_msg = f"微信API错误: {data.get('errmsg', '未知错误')} (错误码: {data.get('errcode')})"
                    logger.error(error_msg)
                    raise HTTPException(status_code=400, detail=error_msg)
                
                logger.info(f"成功获取微信access_token，openid: {data.get('openid', 'N/A')}")
                return data
                
        except httpx.HTTPError as e:
            logger.error(f"请求微信API失败: {e}")
            raise HTTPException(status_code=500, detail="微信服务暂时不可用")
        except Exception as e:
            logger.error(f"获取微信access_token时发生未知错误: {e}")
            raise HTTPException(status_code=500, detail="登录失败，请重试")
    
    async def get_user_info(self, access_token: str, openid: str) -> Dict[str, Any]:
        """
        获取微信用户信息
        
        Args:
            access_token: 访问令牌
            openid: 用户openid
            
        Returns:
            用户信息字典
            
        Raises:
            HTTPException: 当获取用户信息失败时
        """
        params = {
            'access_token': access_token,
            'openid': openid,
            'lang': 'zh_CN'
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.user_info_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # 检查是否有错误
                if 'errcode' in data:
                    error_msg = f"获取用户信息失败: {data.get('errmsg', '未知错误')} (错误码: {data.get('errcode')})"
                    logger.error(error_msg)
                    raise HTTPException(status_code=400, detail=error_msg)
                
                logger.info(f"成功获取微信用户信息，nickname: {data.get('nickname', 'N/A')}")
                return data
                
        except httpx.HTTPError as e:
            logger.error(f"请求微信用户信息API失败: {e}")
            raise HTTPException(status_code=500, detail="获取用户信息失败")
        except Exception as e:
            logger.error(f"获取微信用户信息时发生未知错误: {e}")
            raise HTTPException(status_code=500, detail="获取用户信息失败，请重试")
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        刷新access_token
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            新的token信息
        """
        params = {
            'appid': self.app_id,
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.refresh_token_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                if 'errcode' in data:
                    error_msg = f"刷新token失败: {data.get('errmsg', '未知错误')}"
                    logger.error(error_msg)
                    raise HTTPException(status_code=400, detail=error_msg)
                
                return data
                
        except httpx.HTTPError as e:
            logger.error(f"刷新微信token失败: {e}")
            raise HTTPException(status_code=500, detail="刷新登录状态失败")
    
    async def check_access_token(self, access_token: str, openid: str) -> bool:
        """
        检查access_token是否有效
        
        Args:
            access_token: 访问令牌
            openid: 用户openid
            
        Returns:
            True if valid, False otherwise
        """
        params = {
            'access_token': access_token,
            'openid': openid
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.check_token_url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # errcode为0表示token有效
                return data.get('errcode') == 0
                
        except Exception as e:
            logger.error(f"检查微信token有效性失败: {e}")
            return False

# 创建全局实例
wechat_service = WeChatService()

from pydantic import BaseModel, Field
from typing import List, Optional, Any
from datetime import datetime
from .address import Address

class OrderGoods(BaseModel):
    id: str
    goods_id: str
    name: str
    image: Optional[str] = None
    price: float
    count: int
    sku_name: Optional[str] = None

    class Config:
        from_attributes = True
        populate_by_name = True

class Order(BaseModel):
    id: str
    order_no: str
    status: int
    total_price: float
    pay_price: float
    create_time: datetime
    pay_time: Optional[datetime] = None
    ship_time: Optional[datetime] = None
    complete_time: Optional[datetime] = None
    cancel_time: Optional[datetime] = None
    remark: Optional[str] = None
    address_snapshot: Optional[str] = None
    order_goods: List[OrderGoods] = []

    class Config:
        from_attributes = True
        populate_by_name = True

class OrderCreate(BaseModel):
    address_id: str
    remark: Optional[str] = None
    # The goods to create the order from will likely come from the user's cart
    # or a similar mechanism, so we might not need to pass them directly.
    # We will handle this logic in the CRUD/API layer. 
import uuid
from datetime import datetime
from sqlalchemy import String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from fastapi_app.core.database import Base
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .conversation import Conversation

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class ChatMessage(Base):
    __tablename__ = 'chat_messages'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    user_id: Mapped[str] = mapped_column(String(32), ForeignKey('users.id', ondelete='CASCADE'), index=True, nullable=True)
    conversation_id: Mapped[str] = mapped_column(String(32), ForeignKey('conversations.id', ondelete='CASCADE'), index=True, nullable=False)
    message: Mapped[str] = mapped_column(Text, nullable=False)
    is_user: Mapped[bool] = mapped_column(<PERSON><PERSON><PERSON>, default=True)
    timestamp: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    conversation: Mapped["Conversation"] = relationship(back_populates="chat_messages")
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'user_id': self.user_id,
            'message': self.message,
            'is_user': self.is_user,
            'timestamp': int(self.timestamp.timestamp() * 1000) if isinstance(self.timestamp, datetime) else None
        } 
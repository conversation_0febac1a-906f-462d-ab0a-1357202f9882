# Mastea Flask Docker 部署指南

## 项目概述

Mastea Flask是一个基于FastAPI的智能茶品推荐系统，支持体质评估、AI主诉分析、个性化茶品推荐等功能。本文档提供完整的Docker容器化部署方案。

## 系统要求

- **操作系统**: Linux/macOS/Windows
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少5GB可用空间
- **网络**: 需要访问互联网下载镜像

## 快速部署

### 1. 一键部署（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd Mastea_flask

# 构建并启动服务
docker-compose up -d

# 等待服务启动完成（约2-3分钟）
docker-compose logs -f fastapi_app
```

访问地址：
- **应用首页**: http://localhost:8001
- **API文档**: http://localhost:8001/api/v1/docs
- **管理界面**: http://localhost:8001/dashboard_enhanced

### 2. 分步部署

```bash
# 1. 构建Docker镜像
docker build -t mastea-fastapi:latest .

# 2. 启动数据库
docker-compose up -d postgres

# 3. 等待数据库就绪
sleep 30

# 4. 启动应用
docker-compose up -d fastapi_app

# 5. 运行数据库迁移
docker-compose exec fastapi_app alembic upgrade head
```

## 服务架构

### 容器组成

1. **mastea_postgres**: PostgreSQL 15数据库
   - 端口: 5432
   - 数据持久化: postgres_data卷

2. **mastea_fastapi**: FastAPI应用服务
   - 端口: 8001
   - 依赖: PostgreSQL数据库

3. **mastea_redis**: Redis缓存服务（可选）
   - 端口: 6379
   - 用于会话管理和缓存

### 网络配置

- 内部网络: mastea_network
- 外部访问端口: 8001 (FastAPI), 5432 (PostgreSQL), 6379 (Redis)

## 环境配置

### 环境变量

主要环境变量配置（在docker-compose.yml中）：

```yaml
# 数据库配置
DATABASE_URL: "*********************************************/mastea_prod"
POSTGRES_HOST: postgres
POSTGRES_USER: mastea
POSTGRES_PASSWORD: wyh12257410
POSTGRES_DB: mastea_prod

# 应用配置
ENVIRONMENT: production
SECRET_KEY: "mastea-production-secret-key-2024"
JWT_SECRET_KEY: "mastea-jwt-production-secret-key-2024"
PORT: 8001

# 微信小程序配置
WECHAT_APP_ID: "wxc8301173764205f1"
WECHAT_APP_SECRET: "d61b1e2c0191cd8e2491e4038e3eaa1a"

# AI配置
SILICONFLOW_API_KEY: "sk-tfgujfpskkcjhtnhfhbvegnrthmmyebrzodvizydvdjmzfnl"
AI_API_BASE_URL: "https://api.siliconflow.cn/v1"
AI_MODEL_ID: "deepseek-ai/DeepSeek-V3"
```

### 自定义配置

如需修改配置，可以：

1. **修改docker-compose.yml**中的环境变量
2. **创建.env.local**文件覆盖默认配置
3. **使用环境变量**在运行时传入

## 数据管理

### 数据持久化

- **数据库数据**: 存储在`postgres_data`卷中
- **应用日志**: 映射到`./logs`目录
- **上传文件**: 映射到`./uploads`目录

### 数据库操作

```bash
# 连接数据库
docker-compose exec postgres psql -U mastea -d mastea_prod

# 备份数据库
docker-compose exec postgres pg_dump -U mastea mastea_prod > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U mastea mastea_prod < backup.sql

# 查看数据库日志
docker-compose logs postgres
```

### 数据库迁移

```bash
# 运行迁移
docker-compose exec fastapi_app alembic upgrade head

# 创建新迁移
docker-compose exec fastapi_app alembic revision --autogenerate -m "描述"

# 查看迁移历史
docker-compose exec fastapi_app alembic history
```

## 服务管理

### 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]

# 停止服务
docker-compose down

# 完全清理（包括数据）
docker-compose down -v

# 更新镜像
docker-compose pull
docker-compose up -d
```

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost:8001/api/v1/health

# 检查数据库连接
docker-compose exec postgres pg_isready -U mastea

# 查看容器资源使用
docker stats
```

## 监控和日志

### 日志管理

```bash
# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f fastapi_app

# 查看最近的日志
docker-compose logs --tail=100 fastapi_app

# 导出日志
docker-compose logs fastapi_app > app.log
```

### 性能监控

```bash
# 查看容器资源使用
docker stats --no-stream

# 查看磁盘使用
docker system df

# 清理未使用的资源
docker system prune
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8001
   
   # 修改docker-compose.yml中的端口映射
   ports:
     - "8002:8001"  # 改为8002端口
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec postgres pg_isready -U mastea
   
   # 重启数据库
   docker-compose restart postgres
   ```

3. **应用启动失败**
   ```bash
   # 查看详细错误日志
   docker-compose logs fastapi_app
   
   # 检查环境变量
   docker-compose exec fastapi_app env | grep DATABASE_URL
   ```

4. **内存不足**
   ```bash
   # 检查内存使用
   docker stats
   
   # 增加Docker内存限制或清理不用的容器
   docker container prune
   ```

### 调试模式

```bash
# 以调试模式启动
docker-compose -f docker-compose.yml -f docker-compose.debug.yml up

# 进入容器调试
docker-compose exec fastapi_app bash

# 查看应用进程
docker-compose exec fastapi_app ps aux
```

## 生产环境部署

### 安全配置

1. **修改默认密码**
2. **使用HTTPS**
3. **配置防火墙**
4. **定期备份数据**

### 性能优化

1. **增加worker数量**
2. **配置负载均衡**
3. **使用CDN**
4. **数据库优化**

### 高可用部署

1. **多实例部署**
2. **数据库主从复制**
3. **Redis集群**
4. **监控告警**

## 服务器部署

### 自动部署脚本

```bash
# 使用服务器部署脚本
./scripts/server-deploy.sh

# 或手动指定服务器
./scripts/server-deploy.sh -h ************** -u root
```

### 手动服务器部署

1. **上传部署包**
2. **安装Docker环境**
3. **运行部署脚本**
4. **配置域名和SSL**

## 支持和维护

### 更新流程

1. **拉取最新代码**
2. **重新构建镜像**
3. **备份数据**
4. **更新服务**
5. **验证功能**

### 联系支持

- **文档**: 查看项目README.md
- **问题**: 提交GitHub Issue
- **邮件**: 联系开发团队

---

**注意**: 本部署方案已在多个环境中测试验证，如遇问题请参考故障排除章节或联系技术支持。

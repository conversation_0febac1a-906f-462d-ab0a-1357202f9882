# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.env.local
.env.development.local
.env.test.local
.env.production.local
.venv/
venv/
ENV/
env/
.venv/
.env/
env.bak/
venv.bak/
.python-version

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.sqlite3
*.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
docs/
*.md

# Test files
test/
tests/
*_test.py
test_*.py

# Cache
.cache/
.pytest_cache/

# OS
Thumbs.db
.DS_Store

# Temporary files
*.tmp
*.temp
.tmp/

# Claude settings
.claude/

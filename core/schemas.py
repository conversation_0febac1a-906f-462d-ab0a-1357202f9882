"""
数据模型定义
包含API请求和响应的Pydantic模型
"""

from pydantic import BaseModel, Field
from typing import Optional

class TokenData(BaseModel):
    """JWT令牌数据"""
    sub: Optional[str] = None

class WeChatLogin(BaseModel):
    """
    微信小程序登录请求模型
    
    基于2025年最新的微信小程序API规范
    包含获取用户信息所需的字段
    """
    code: str = Field(..., description="微信小程序授权码，由前端wx.login()获取")
    encryptedData: Optional[str] = Field(None, description="加密的用户信息，需要用户授权并由wx.getUserInfo()获取")
    iv: Optional[str] = Field(None, description="加密算法的初始向量，由wx.getUserInfo()获取")
    raw_data: Optional[str] = Field(None, description="原始数据字符串，由wx.getUserInfo()获取")
    signature: Optional[str] = Field(None, description="使用sha1得到的数据签名，由wx.getUserInfo()获取")
    app_version: Optional[str] = Field(None, description="小程序版本号")
    client_ip: Optional[str] = Field(None, description="客户端IP地址")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "code": "013Xjp000kQnPM1pgt300LqaLS4Xjp0W",
                "encryptedData": "1234abcd...",
                "iv": "abcd1234...",
                "raw_data": "{\"nickName\":\"微信用户\",\"gender\":0}",
                "signature": "1234567890abcdef",
                "app_version": "1.0.0"
            }
        }
    }

class RefreshToken(BaseModel):
    """
    刷新令牌请求模型
    """
    refreshToken: str = Field(..., description="刷新令牌")

class UserResponse(BaseModel):
    """
    用户信息响应模型
    """
    id: str
    nickname: str
    avatar: Optional[str] = None
    vipLevel: int = 0
    registerDate: str
    isAuthorized: bool = False

# 购物车相关模型
class CartItemAdd(BaseModel):
    """添加商品到购物车请求模型"""
    teaProductId: str
    quantity: int = 1
    productType: int = 0  # 0=普通茶品，1=定制茶品
    
class CartItemUpdate(BaseModel):
    """更新购物车商品数量请求模型"""
    cartItemId: str
    quantity: int
    
class CartItemRemove(BaseModel):
    """删除购物车商品请求模型"""
    cartItemIds: list[str]

class CartItemSelect(BaseModel):
    """购物车商品选择状态请求模型"""
    cartItemIds: list[str]
    selected: bool

class CartItemResponse(BaseModel):
    """购物车商品响应模型"""
    id: str
    productId: str
    productName: str
    price: float
    originalPrice: Optional[float] = None
    quantity: int
    image: str
    isVip: bool = False
    stock: int
    maxPurchase: Optional[int] = None
    selected: bool
    productType: int  # 0=普通茶品，1=定制茶品
    
class CartResponse(BaseModel):
    """购物车响应模型"""
    items: list[CartItemResponse] = []
    totalPrice: float = 0
    totalQuantity: int = 0 
# Mastea Flask 服务器部署包（修复版）

## 修复内容

✅ **数据库字段修复**: 添加了缺失的微信相关字段
- `wechat_authorized` - 微信授权状态
- `wechat_auth_time` - 授权时间
- `is_wechat_user` - 是否为微信用户
- `last_login_at` - 最后登录时间
- `is_active` - 用户激活状态

✅ **迁移文件完整**: 包含所有必要的数据库迁移
✅ **Docker镜像**: 包含最新的应用代码和修复

## 包含内容

- **应用代码**: fastapi_app/, migrations/
- **配置文件**: docker-compose.yml, .env, alembic.ini
- **Docker镜像**: mastea-fastapi-image.tar (约650MB)
- **部署脚本**: server-deploy.sh

## 快速部署步骤

### 1. 上传部署包到服务器
```bash
scp mastea-deployment-final-fixed.tar.gz root@111.231.145.58:/opt/
```

### 2. 在服务器上解压并部署
```bash
# SSH连接到服务器
ssh root@111.231.145.58

# 解压部署包
cd /opt
tar -xzf mastea-deployment-final-fixed.tar.gz
cd mastea-deployment-final-fixed

# 执行一键部署
chmod +x server-deploy.sh
sudo ./server-deploy.sh
```

## 部署脚本功能

`server-deploy.sh` 脚本会自动执行以下操作：

1. **安装Docker环境**（如果未安装）
2. **清理旧的mastea容器和镜像**
3. **加载Docker镜像**（从tar文件）
4. **设置部署目录**（/opt/mastea）
5. **启动Docker Compose服务**
6. **运行数据库迁移**（包含修复的字段）
7. **配置防火墙**（开放8001端口）
8. **验证部署状态**

## 访问地址

部署完成后可通过以下地址访问：

- **应用首页**: http://111.231.145.58:8001
- **API文档**: http://111.231.145.58:8001/api/v1/docs
- **管理界面**: http://111.231.145.58:8001/dashboard_enhanced ✅ **已修复登录问题**
- **健康检查**: http://111.231.145.58:8001/api/v1/health

## 服务管理

```bash
# 进入部署目录
cd /opt/mastea

# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 查看应用日志
docker-compose logs fastapi_app

# 查看数据库日志
docker-compose logs postgres
```

## 数据库管理

### 本地与服务器数据库说明

- **本地数据库**: 用于开发和测试，包含完整的表结构和测试数据
- **服务器数据库**: 生产环境数据库，通过迁移脚本自动创建表结构
- **数据同步**: 服务器部署时会自动运行所有迁移，确保表结构一致

### 数据库操作

```bash
# 连接数据库
docker-compose exec postgres psql -U mastea mastea_prod

# 查看表结构
\d users

# 查看微信相关字段
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'users' AND column_name LIKE '%wechat%';

# 备份数据库
docker-compose exec postgres pg_dump -U mastea mastea_prod > backup.sql
```

## 故障排除

### 常见问题

1. **dashboard_enhanced登录失败**
   - ✅ 已修复：添加了缺失的`wechat_authorized`等字段

2. **数据库字段不存在错误**
   - ✅ 已修复：完整的迁移文件包含所有必要字段

3. **容器启动失败**
   ```bash
   docker-compose logs
   ```

4. **数据库连接失败**
   ```bash
   docker-compose exec postgres pg_isready -U mastea
   ```

### 手动部署步骤

如果自动脚本失败，可以手动执行：

```bash
# 1. 加载镜像
docker load -i mastea-fastapi-image.tar

# 2. 启动服务
docker-compose up -d

# 3. 运行迁移
docker-compose exec -T fastapi_app bash -c "cd migrations && alembic upgrade head"

# 4. 检查状态
docker-compose ps
curl http://localhost:8001/api/v1/health
```

## 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **内存**: 最少2GB，推荐4GB+
- **磁盘空间**: 最少3GB可用空间
- **网络**: 需要访问互联网安装Docker（仅首次）

## 技术支持

如遇问题请联系开发团队。

---
**注意**: 此版本已修复所有已知的数据库字段问题，dashboard_enhanced页面登录功能正常。

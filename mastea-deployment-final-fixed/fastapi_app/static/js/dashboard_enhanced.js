// === Global State Management ===
let currentUser = null;
let authToken = null;
let currentConversationId = null;
let currentComplaintConversationId = null;
let currentTaskType = null;
let isDebugMode = false;

// === Utility Functions ===
function getAuthHeaders() {
    const headers = { 'Content-Type': 'application/json' };
    if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
    }
    return headers;
}

function showError(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = message;
        element.style.display = 'block';
        setTimeout(() => element.style.display = 'none', 5000);
    }
}

function showSuccess(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = message;
        element.style.display = 'block';
        setTimeout(() => element.style.display = 'none', 3000);
    }
}

function showLoading(elementId, show = true) {
    const element = document.getElementById(elementId);
    if (element) {
        element.style.display = show ? 'block' : 'none';
    }
}

function logApiCall(method, url, requestData = null, responseData = null, targetElementId = null) {
    if (!isDebugMode) return;
    
    const debugContainer = targetElementId ? document.getElementById(targetElementId) : null;
    if (!debugContainer) return;
    
    // Clear previous debug info
    debugContainer.innerHTML = '';
    
    // Request info
    const requestDiv = document.createElement('div');
    requestDiv.className = 'api-request';
    requestDiv.innerHTML = `
        <strong>${method} ${url}</strong><br>
        ${requestData ? `Request: ${JSON.stringify(requestData, null, 2)}` : 'No request data'}
    `;
    debugContainer.appendChild(requestDiv);
    
    // Response info
    if (responseData) {
        const responseDiv = document.createElement('div');
        responseDiv.className = 'api-response';
        responseDiv.innerHTML = `Response: ${JSON.stringify(responseData, null, 2)}`;
        debugContainer.appendChild(responseDiv);
    }
}

// === Authentication Functions ===
function showRegister() {
    document.getElementById('register-form').style.display = 'block';
    const timestamp = Date.now().toString(36);
    document.getElementById('reg-username').value = `testuser_${timestamp}`;
    document.getElementById('reg-email').value = `test_${timestamp}@example.com`;
    document.getElementById('reg-mobile').value = `138${timestamp.substr(-8)}`;
}

function showLogin() {
    document.getElementById('register-form').style.display = 'none';
}

async function quickTestLogin() {
    // Use existing test user credentials
    document.getElementById('login-username').value = 'testuser123';
    document.getElementById('login-password').value = '123456';
    await performLogin();
}

async function performRegister() {
    const userData = {
        username: document.getElementById('reg-username').value,
        email: document.getElementById('reg-email').value,
        mobile: document.getElementById('reg-mobile').value,
        password: document.getElementById('reg-password').value
    };

    try {
        const response = await fetch('/api/v1/auth/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(userData)
        });

        const result = await response.json();
        logApiCall('POST', '/api/v1/auth/register', userData, result, 'auth-debug-response');
        
        if (response.ok && result.code === 0) {
            showSuccess('login-success', '注册成功！正在自动登录...');
            document.getElementById('login-username').value = userData.username;
            document.getElementById('login-password').value = userData.password;
            setTimeout(() => performLogin(), 1000);
        } else {
            showError('login-error', result.message || '注册失败');
        }
    } catch (error) {
        showError('login-error', '注册时发生错误: ' + error.message);
    }
}

async function performLogin() {
    const loginData = {
        username: document.getElementById('login-username').value,
        password: document.getElementById('login-password').value
    };

    try {
        const response = await fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(loginData)
        });

        const result = await response.json();
        logApiCall('POST', '/api/v1/auth/login', loginData, result, 'auth-debug-response');
        
        if (response.ok && result.code === 0) {
            currentUser = result.data.userInfo;
            authToken = result.data.access_token;
            
            localStorage.setItem('mastea_token', authToken);
            localStorage.setItem('mastea_user', JSON.stringify(currentUser));
            
            showDashboard();
        } else {
            showError('login-error', result.message || '登录失败');
        }
    } catch (error) {
        showError('login-error', '登录时发生错误: ' + error.message);
    }
}

function logout() {
    currentUser = null;
    authToken = null;
    currentConversationId = null;
    currentComplaintConversationId = null;
    localStorage.removeItem('mastea_token');
    localStorage.removeItem('mastea_user');
    
    document.getElementById('login-section').style.display = 'block';
    document.getElementById('dashboard-section').style.display = 'none';
}

function showDashboard() {
    document.getElementById('login-section').style.display = 'none';
    document.getElementById('dashboard-section').style.display = 'block';
    
    document.getElementById('username').textContent = currentUser.username;
    
    loadOverview();
}

// === Navigation Functions ===
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const toggleText = document.getElementById('toggle-text');
    
    sidebar.classList.toggle('collapsed');
    
    if (sidebar.classList.contains('collapsed')) {
        toggleText.textContent = '展开';
    } else {
        toggleText.textContent = '收起';
    }
}

function showSection(sectionName, sourceElement = null) {
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    document.querySelectorAll('.menu-link').forEach(link => {
        link.classList.remove('active');
    });
    
    document.getElementById(sectionName + '-section').classList.add('active');
    
    // Only set active on source element if it exists (when called from click event)
    if (sourceElement) {
        sourceElement.classList.add('active');
    }
    
    switch(sectionName) {
        case 'overview':
            loadOverview();
            break;
        case 'reports':
            loadReports();
            break;
        case 'assessment':
            loadAssessmentPage();
            break;
        case 'chat':
            loadConversations();
            break;
        case 'products':
            loadProducts();
            break;
        case 'cart':
            loadCart();
            break;
        case 'addresses':
            loadAddresses();
            break;
        case 'orders':
            loadOrders();
            break;
        case 'profile':
            loadProfile();
            break;
    }
}

// === Overview Functions ===
async function loadOverview() {
    showLoading('overview-loading');
    
    try {
        document.getElementById('user-id').textContent = currentUser.id;
        document.getElementById('user-name').textContent = currentUser.username;
        document.getElementById('user-email').textContent = currentUser.email;
        document.getElementById('user-mobile').textContent = currentUser.mobile;
        
        const [reports, conversations, cart, orders] = await Promise.all([
            loadReportCounts(),
            loadConversationCount(),
            loadCartCount(),
            loadOrderCount()
        ]);
        
        showLoading('overview-loading', false);
    } catch (error) {
        showError('overview-error', '加载概览数据失败: ' + error.message);
        showLoading('overview-loading', false);
    }
}

async function loadReportCounts() {
    try {
        const [constResponse, compResponse] = await Promise.all([
            fetch(`/api/v1/reports/constitution/list?user_id=${currentUser.id}`, {
                headers: getAuthHeaders()
            }),
            fetch(`/api/v1/reports/complaint/list?user_id=${currentUser.id}`, {
                headers: getAuthHeaders()
            })
        ]);
        
        const constResult = await constResponse.json();
        const compResult = await compResponse.json();
        
        const constCount = constResult.data ? constResult.data.length : 0;
        const compCount = compResult.data ? compResult.data.length : 0;
        
        document.getElementById('constitution-count').textContent = constCount;
        document.getElementById('complaint-count').textContent = compCount;
        
        return { constitution: constCount, complaint: compCount };
    } catch (error) {
        console.error('加载报告统计失败:', error);
        return { constitution: 0, complaint: 0 };
    }
}

async function loadConversationCount() {
    try {
        const response = await fetch(`/api/v1/chat/conversations`, {
            headers: getAuthHeaders()
        });
        const result = await response.json();
        const conversations = Array.isArray(result) ? result : (result.data || []);
        const count = conversations.length;
        document.getElementById('conversation-count').textContent = count;
        return count;
    } catch (error) {
        console.error('加载对话统计失败:', error);
        return 0;
    }
}

async function loadCartCount() {
    try {
        const response = await fetch(`/api/v1/cart`, {
            headers: getAuthHeaders()
        });
        const result = await response.json();
        const cartItems = Array.isArray(result) ? result : (result.data || []);
        const count = cartItems.length;
        document.getElementById('cart-count').textContent = count;
        return count;
    } catch (error) {
        console.error('加载购物车统计失败:', error);
        return 0;
    }
}

async function loadOrderCount() {
    try {
        const response = await fetch(`/api/v1/orders/list`, {
            headers: getAuthHeaders()
        });
        const result = await response.json();
        const orders = Array.isArray(result) ? result : (result.data || []);
        const count = orders.length;
        document.getElementById('order-count').textContent = count;
        return count;
    } catch (error) {
        console.error('加载订单统计失败:', error);
        return 0;
    }
}

// === Reports Functions ===
async function loadReports() {
    showLoading('reports-loading');
    
    try {
        const [constResponse, compResponse] = await Promise.all([
            fetch(`/api/v1/reports/constitution/list?user_id=${currentUser.id}`, {
                headers: getAuthHeaders()
            }),
            fetch(`/api/v1/reports/complaint/list?user_id=${currentUser.id}`, {
                headers: getAuthHeaders()
            })
        ]);
        
        const constResult = await constResponse.json();
        const compResult = await compResponse.json();
        
        logApiCall('GET', '/api/v1/reports/constitution/list', null, constResult, 'constitution-response');
        logApiCall('GET', '/api/v1/reports/complaint/list', null, compResult, 'complaint-response');
        
        displayReports(constResult.data, 'constitution');
        displayReports(compResult.data, 'complaint');
        
        showLoading('reports-loading', false);
    } catch (error) {
        showError('reports-error', '加载报告失败: ' + error.message);
        showLoading('reports-loading', false);
    }
}

function displayReports(reports, type) {
    const container = document.getElementById(type === 'constitution' ? 'constitution-reports' : 'complaint-reports');
    
    if (reports && reports.length > 0) {
        const reportsHtml = reports.map(report => `
            <div style="padding: 10px; border-bottom: 1px solid #eee;">
                <strong>${report.title || '体质评估报告'}</strong><br>
                <small>创建时间: ${new Date(report.created_at).toLocaleString()}</small><br>
                <button class="btn btn-outline" onclick="viewReport('${report.id}', '${type}')">查看详情</button>
                ${type === 'constitution' ? `<button class="btn btn-warning" onclick="useForComplaint('${report.id}')">用于主诉评估</button>` : ''}
            </div>
        `).join('');
        container.innerHTML = reportsHtml;
    } else {
        container.innerHTML = '<p>暂无报告</p>';
    }
}

async function createConstitutionReport() {
    try {
        const reportData = {
            user_id: currentUser.id,
            answers: {
                gender: "男",
                age_group: "30-40",
                q3_core_symptom: "疲劳乏力，容易感冒",
                q4_sweating: ["白天动则大汗"],
                q5_stool: "规律正常",
                q6_sleep: "睡眠正常",
                q7_season_response: ["四季适应良好"],
                q8_diet_preference: "规律清淡",
                q9_long_term_habits: ["作息规律"],
                q10_body_type: "肌肉发达，体脂低",
                q11_skin_status: "光滑红润",
                q12_priority_conditioning: "增强体质"
            }
        };
        
        const response = await fetch('/api/v1/reports/constitution/calculate', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(reportData)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/reports/constitution/calculate', reportData, result, 'constitution-response');
        
        if (response.ok) {
            alert('体质报告生成成功！');
            loadReports();
            loadOverview(); // Update counts
        } else {
            throw new Error(result.message || '生成报告失败');
        }
    } catch (error) {
        showError('reports-error', '生成体质报告失败: ' + error.message);
    }
}

let currentBaseReportId = null;

function useForComplaint(reportId) {
    currentBaseReportId = reportId;
    // Update the base report select in assessment tab
    const select = document.getElementById('base-report-select');
    if (select) {
        // Add option if not exists
        let option = select.querySelector(`option[value="${reportId}"]`);
        if (!option) {
            option = document.createElement('option');
            option.value = reportId;
            option.textContent = `报告 ${reportId}`;
            select.appendChild(option);
        }
        select.value = reportId;
    }
    alert('已选择此体质报告作为主诉评估的基础，请前往健康评估页面继续。');
}

async function startComplaintAssessment() {
    if (!currentBaseReportId) {
        alert('请先选择一个体质报告作为基础');
        return;
    }
    
    try {
        const data = {
            user_id: currentUser.id,
            base_constitution_report_id: currentBaseReportId
        };
        
        const response = await fetch('/api/v1/reports/complaint/start', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/reports/complaint/start', data, result, 'complaint-response');
        
        if (response.ok) {
            currentComplaintConversationId = result.data?.conversation_id;
            alert('主诉评估已开始！您可以在健康评估页面继续对话。');
            showSection('assessment');
            showAssessmentTab('complaint');
            // 自动发送初始消息给AI，包含体质报告信息
            await autoSendInitialComplaintMessage();
        } else {
            throw new Error(result.detail || '开始主诉评估失败');
        }
    } catch (error) {
        showError('reports-error', '开始主诉评估失败: ' + error.message);
    }
}

async function autoSendInitialComplaintMessage() {
    if (!currentComplaintConversationId) {
        console.error('没有活动的主诉评估对话');
        return;
    }
    
    try {
        // 获取基础体质报告信息
        let constitutionReport = "基础体质评估报告";
        if (currentBaseReportId) {
            try {
                const reportResponse = await fetch(`/api/v1/reports/${currentBaseReportId}`, {
                    headers: getAuthHeaders()
                });
                if (reportResponse.ok) {
                    const reportData = await reportResponse.json();
                    constitutionReport = JSON.stringify(reportData.data, null, 2);
                }
            } catch (e) {
                console.warn('获取体质报告失败，使用默认信息:', e);
            }
        }
        
        // 构造初始消息 - 简洁直接，触发AI的主诉确认流程
        const initialMessage = `[体质报告数据]\n${constitutionReport}\n\n[开始主诉评估] 请基于我的体质特点，直接询问最可能的主诉症状。`;
        
        // 显示发送状态（AI的回复会直接显示，不需要额外提示）
        // addMessageToComplaintChat('✅ 已将基础体质报告+提示词成功发送给AI', false);
        
        // 发送初始消息
        const response = await fetch('/api/v1/chat/send_stream', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({
                user_id: currentUser.id,
                conversation_id: currentComplaintConversationId,
                message: initialMessage,
                task_type: "chief_complaint"
            })
        });
        
        if (!response.ok) {
            throw new Error('发送初始消息失败');
        }
        
        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let aiMessageDiv = null;
        let streamingContent = "";
        
        while (true) {
            const { value, done } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;
            
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            
            for (const line of lines) {
                if (line.trim() && line.startsWith('data: ')) {
                    try {
                        const dataStr = line.substring(6).trim();
                        if (dataStr === '[DONE]') {
                            if (aiMessageDiv) {
                                const timeSpan = aiMessageDiv.querySelector('.timestamp');
                                if (timeSpan && !timeSpan.textContent) {
                                    timeSpan.textContent = new Date().toLocaleString();
                                }
                            }
                            break;
                        }
                        
                        const data = JSON.parse(dataStr);
                        
                        if (data.type === 'ai_chunk') {
                            const content = data.data?.content || '';
                            streamingContent += content;
                            
                            if (!aiMessageDiv || !aiMessageDiv.parentNode) {
                                aiMessageDiv = document.createElement('div');
                                aiMessageDiv.className = 'message ai-message';
                                
                                const contentP = document.createElement('p');
                                contentP.style.margin = '0';
                                aiMessageDiv.appendChild(contentP);
                                
                                const timeSpan = document.createElement('span');
                                timeSpan.className = 'timestamp';
                                aiMessageDiv.appendChild(timeSpan);
                                
                                const chatbox = document.getElementById('complaint-chatbox');
                                chatbox.appendChild(aiMessageDiv);
                            }
                            
                            const contentP = aiMessageDiv.querySelector('p');
                            if (contentP) {
                                contentP.textContent = streamingContent;
                                contentP.offsetHeight;
                                
                                const chatbox = document.getElementById('complaint-chatbox');
                                chatbox.scrollTop = chatbox.scrollHeight;
                            }
                        }
                    } catch (e) {
                        console.error('解析流数据失败:', e);
                    }
                }
            }
        }
        
    } catch (error) {
        console.error('自动发送初始消息失败:', error);
        addMessageToComplaintChat('❌ 自动发送初始消息失败，请手动开始对话', false);
    }
}

async function viewReport(reportId, type) {
    try {
        const response = await fetch(`/api/v1/reports/${type}/${reportId}`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', `/api/v1/reports/${type}/${reportId}`, null, result, 'constitution-response');
        
        if (response.ok) {
            const content = JSON.stringify(result, null, 2);
            alert(`报告详情：\n${content.substring(0, 500)}...`);
        } else {
            throw new Error('获取报告详情失败');
        }
    } catch (error) {
        alert('查看报告失败: ' + error.message);
    }
}

// === Assessment Functions ===
function showAssessmentTab(tabName) {
    document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    
    event.target.classList.add('active');
    document.getElementById(tabName + '-tab').classList.add('active');
    
    if (tabName === 'complaint') {
        loadBaseReportSelect();
    }
}

async function loadAssessmentPage() {
    await loadBaseReportSelect();
}

async function loadBaseReportSelect() {
    try {
        const response = await fetch(`/api/v1/reports/constitution/list?user_id=${currentUser.id}`, {
            headers: getAuthHeaders()
        });
        const result = await response.json();
        
        const select = document.getElementById('base-report-select');
        select.innerHTML = '<option value="">请选择基础体质报告</option>';
        
        if (result.data && result.data.length > 0) {
            result.data.forEach(report => {
                const option = document.createElement('option');
                option.value = report.id;
                option.textContent = `${report.title} (${new Date(report.created_at).toLocaleDateString()})`;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载基础报告列表失败:', error);
    }
}

async function submitConstitutionForm() {
    const form = document.getElementById('constitution-form');
    const formData = new FormData(form);
    
    const answers = {
        gender: formData.get('gender'),
        age_group: formData.get('age_group'),
        q3_core_symptom: formData.get('q3_core_symptom'),
        q12_priority_conditioning: formData.get('q12_priority_conditioning')
    };
    
    const reportData = {
        user_id: currentUser.id,
        answers: answers
    };
    
    try {
        const response = await fetch('/api/v1/reports/constitution/calculate', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(reportData)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/reports/constitution/calculate', reportData, result, 'constitution-response');
        
        if (response.ok) {
            alert('体质报告生成成功！');
            loadReports();
            loadOverview();
        } else {
            throw new Error(result.message || '生成报告失败');
        }
    } catch (error) {
        showError('assessment-error', '生成体质报告失败: ' + error.message);
    }
}

async function startComplaintFromAssessment() {
    const baseReportId = document.getElementById('base-report-select').value;
    if (!baseReportId) {
        alert('请先选择一个基础体质报告');
        return;
    }
    
    try {
        const data = {
            user_id: currentUser.id,
            base_constitution_report_id: baseReportId
        };
        
        const response = await fetch('/api/v1/reports/complaint/start', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/reports/complaint/start', data, result, 'complaint-response');
        
        if (response.ok) {
            currentComplaintConversationId = result.data?.conversation_id;
            document.getElementById('complaint-chat-area').style.display = 'block';
            
            // Initialize the conversation - 不再显示固定的AI开场白
            const chatbox = document.getElementById('complaint-chatbox');
            chatbox.innerHTML = '';
            // 直接自动发送初始消息，让AI自然回复
            await autoSendInitialComplaintMessage();
        } else {
            throw new Error(result.detail || '开始主诉评估失败');
        }
    } catch (error) {
        showError('assessment-error', '开始主诉评估失败: ' + error.message);
    }
}

function addMessageToComplaintChat(text, isUser) {
    const chatbox = document.getElementById('complaint-chatbox');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
    messageDiv.innerHTML = `
        <p style="margin: 0;">${text}</p>
        <div class="timestamp">${new Date().toLocaleTimeString()}</div>
    `;
    chatbox.appendChild(messageDiv);
    chatbox.scrollTop = chatbox.scrollHeight;
}

async function sendComplaintMessage() {
    const input = document.getElementById('complaint-input');
    const message = input.value.trim();
    if (!message || !currentComplaintConversationId) return;
    
    addMessageToComplaintChat(message, true);
    input.value = '';
    
    try {
        const response = await fetch('/api/v1/chat/send_stream', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({
                user_id: currentUser.id,
                conversation_id: currentComplaintConversationId,
                message: message,
                task_type: "chief_complaint"
            })
        });
        
        logApiCall('POST', '/api/v1/chat/send_stream', {
            conversation_id: currentComplaintConversationId,
            message: message
        }, null, 'complaint-response');
        
        if (!response.ok) {
            throw new Error('发送消息失败');
        }
        
        // Handle streaming response
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let streamingContent = "";
        let buffer = '';
        let aiMessageDiv = null;
        
        while (true) {
            const { value, done } = await reader.read();
            if (done) break;
            
            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;
            
            // Process complete lines
            const lines = buffer.split('\n');
            buffer = lines.pop(); // Keep incomplete line in buffer
            
            for (const line of lines) {
                if (line.trim() && line.startsWith('data: ')) {
                    try {
                        const dataStr = line.substring(6).trim();
                        if (dataStr === '[DONE]') {
                            if (aiMessageDiv) {
                                const timeSpan = aiMessageDiv.querySelector('.timestamp');
                                if (timeSpan && !timeSpan.textContent) {
                                    timeSpan.textContent = new Date().toLocaleString();
                                }
                            }
                            break;
                        }
                        
                        const data = JSON.parse(dataStr);
                        if (data.type === 'ai_chunk') {
                            const content = data.data?.content || '';
                            streamingContent += content;
                            
                            if (!aiMessageDiv || !aiMessageDiv.parentNode) {
                                aiMessageDiv = document.createElement('div');
                                aiMessageDiv.className = 'message ai-message';
                                
                                const contentP = document.createElement('p');
                                contentP.style.margin = '0';
                                aiMessageDiv.appendChild(contentP);
                                
                                const timeSpan = document.createElement('span');
                                timeSpan.className = 'timestamp';
                                aiMessageDiv.appendChild(timeSpan);
                                
                                const chatbox = document.getElementById('complaint-chatbox');
                                chatbox.appendChild(aiMessageDiv);
                            }
                            
                            const contentP = aiMessageDiv.querySelector('p');
                            if (contentP) {
                                // 实现打字机效果 - 立即更新文本内容
                                contentP.textContent = streamingContent;
                                
                                // 强制重绘以确保字符立即显示
                                contentP.offsetHeight;
                                
                                const chatbox = document.getElementById('complaint-chatbox');
                                chatbox.scrollTop = chatbox.scrollHeight;
                                
                                // 添加光标效果
                                contentP.style.borderRight = '2px solid #007bff';
                                setTimeout(() => {
                                    if (contentP.style.borderRight) {
                                        contentP.style.borderRight = '';
                                    }
                                }, 50);
                            }
                        } else if (data.type === 'task_complete') {
                            if (aiMessageDiv) {
                                const timeSpan = aiMessageDiv.querySelector('.timestamp');
                                if (timeSpan && !timeSpan.textContent) {
                                    timeSpan.textContent = new Date().toLocaleString();
                                }
                            }
                            addMessageToComplaintChat(`✅ 主诉评估报告已生成！报告ID: ${data.data.report_id}`, false);
                            break;
                        } else if (data.type === 'stream_end') {
                            if (aiMessageDiv) {
                                const timeSpan = aiMessageDiv.querySelector('.timestamp');
                                if (timeSpan && !timeSpan.textContent) {
                                    timeSpan.textContent = new Date().toLocaleString();
                                }
                            }
                            break;
                        }
                    } catch (e) {
                        console.error('解析流数据失败:', e, 'Line:', line);
                    }
                }
            }
        }
        
    } catch (error) {
        addMessageToComplaintChat('抱歉，发送消息时出现错误，请重试。', false);
    } finally {
        // Re-enable input controls
        const input = document.getElementById('complaint-input');
        const button = document.querySelector('#complaint-tab .send-button');
        if (input) input.disabled = false;
        if (button) button.disabled = false;
        if (input) input.focus();
    }
}

function updateComplaintAIMessage(content) {
    const chatbox = document.getElementById('complaint-chatbox');
    let lastAIMessage = chatbox.querySelector('.ai-message:last-child');
    
    if (!lastAIMessage || lastAIMessage.classList.contains('completed')) {
        addMessageToComplaintChat(content, false);
        lastAIMessage = chatbox.querySelector('.ai-message:last-child');
        lastAIMessage.setAttribute('data-streaming', 'true');
    } else if (lastAIMessage.getAttribute('data-streaming') === 'true') {
        lastAIMessage.querySelector('p').textContent = content;
    } else {
        // Previous message was completed, create new one
        addMessageToComplaintChat(content, false);
        lastAIMessage = chatbox.querySelector('.ai-message:last-child');
        lastAIMessage.setAttribute('data-streaming', 'true');
    }
}

// === Chat Functions ===
async function loadConversations() {
    showLoading('chat-loading');
    
    try {
        const response = await fetch(`/api/v1/chat/conversations?user_id=${currentUser.id}`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/chat/conversations', null, result, 'chat-response');
        
        displayConversations(result.data);
        showLoading('chat-loading', false);
    } catch (error) {
        showError('chat-error', '加载对话失败: ' + error.message);
        showLoading('chat-loading', false);
    }
}

function displayConversations(conversations) {
    const container = document.getElementById('conversation-list');
    
    if (conversations && conversations.length > 0) {
        const conversationsHtml = conversations.map(conv => `
            <div style="padding: 10px; border-bottom: 1px solid #eee; cursor: pointer;" onclick="selectConversation('${conv.id}')">
                <strong>${conv.title}</strong><br>
                <small>创建时间: ${new Date(conv.created_at).toLocaleString()}</small>
            </div>
        `).join('');
        container.innerHTML = conversationsHtml;
    } else {
        container.innerHTML = '<p>暂无对话</p>';
    }
}

async function createNewConversation() {
    try {
        const response = await fetch('/api/v1/chat/conversations', {
            method: 'POST',
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/chat/conversations', null, result, 'chat-response');
        
        if (response.ok) {
            currentConversationId = result.data?.id;
            alert('新对话创建成功！');
            loadConversations();
            clearMainChat();
        } else {
            throw new Error('创建对话失败');
        }
    } catch (error) {
        showError('chat-error', '创建对话失败: ' + error.message);
    }
}

function selectConversation(conversationId) {
    currentConversationId = conversationId;
    clearMainChat();
    addMessageToMainChat(`已选择对话 ${conversationId}，您可以开始聊天。`, false);
}

function clearMainChat() {
    document.getElementById('main-chatbox').innerHTML = '';
}

function addMessageToMainChat(text, isUser) {
    const chatbox = document.getElementById('main-chatbox');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
    messageDiv.innerHTML = `
        <p style="margin: 0;">${text}</p>
        <div class="timestamp">${new Date().toLocaleTimeString()}</div>
    `;
    chatbox.appendChild(messageDiv);
    chatbox.scrollTop = chatbox.scrollHeight;
}

async function sendMainChatMessage() {
    const input = document.getElementById('main-chat-input');
    const message = input.value.trim();
    if (!message) return;
    
    if (!currentConversationId) {
        alert('请先创建或选择一个对话');
        return;
    }
    
    addMessageToMainChat(message, true);
    input.value = '';
    
    try {
        const response = await fetch('/api/v1/chat/send_stream', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify({
                user_id: currentUser.id,
                conversation_id: currentConversationId,
                message: message,
                task_type: "chief_complaint"
            })
        });
        
        logApiCall('POST', '/api/v1/chat/send_stream', {
            conversation_id: currentConversationId,
            message: message
        }, null, 'chat-response');
        
        if (!response.ok) {
            throw new Error('发送消息失败');
        }
        
        // Handle streaming response
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let streamingContent = "";
        let buffer = '';
        let aiMessageDiv = null;

        while (true) {
            const { value, done } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;

            // Process complete lines
            const lines = buffer.split('\n');
            buffer = lines.pop(); // Keep incomplete line in buffer

            for (const line of lines) {
                if (line.trim() && line.startsWith('data: ')) {
                    try {
                        const dataStr = line.substring(6).trim();
                        if (dataStr === '[DONE]') {
                            if (aiMessageDiv) {
                                const timeSpan = aiMessageDiv.querySelector('.timestamp');
                                if (timeSpan && !timeSpan.textContent) {
                                    timeSpan.textContent = new Date().toLocaleString();
                                }
                            }
                            break;
                        }

                        const data = JSON.parse(dataStr);
                        
                        if (data.type === 'ai_chunk') {
                            const content = data.data?.content || '';
                            streamingContent += content;
                            
                            if (!aiMessageDiv || !aiMessageDiv.parentNode) {
                                aiMessageDiv = document.createElement('div');
                                aiMessageDiv.className = 'message ai-message';
                                
                                const contentP = document.createElement('p');
                                contentP.style.margin = '0';
                                aiMessageDiv.appendChild(contentP);
                                
                                const timeSpan = document.createElement('span');
                                timeSpan.className = 'timestamp';
                                aiMessageDiv.appendChild(timeSpan);
                                
                                const chatbox = document.getElementById('main-chatbox');
                                chatbox.appendChild(aiMessageDiv);
                            }
                            
                            const contentP = aiMessageDiv.querySelector('p');
                            if (contentP) {
                                // 实现打字机效果 - 立即更新文本内容
                                contentP.textContent = streamingContent;
                                
                                // 强制重绘以确保字符立即显示
                                contentP.offsetHeight;
                                
                                const chatbox = document.getElementById('main-chatbox');
                                chatbox.scrollTop = chatbox.scrollHeight;
                                
                                // 添加光标效果
                                contentP.style.borderRight = '2px solid #007bff';
                                setTimeout(() => {
                                    if (contentP.style.borderRight) {
                                        contentP.style.borderRight = '';
                                    }
                                }, 50);
                            }
                        } else if (data.type === 'stream_end') {
                            if (aiMessageDiv) {
                                const timeSpan = aiMessageDiv.querySelector('.timestamp');
                                if (timeSpan && !timeSpan.textContent) {
                                    timeSpan.textContent = new Date().toLocaleString();
                                }
                            }
                            break;
                        }
                    } catch (e) {
                        console.error('解析流数据失败:', e, 'Line:', line);
                    }
                }
            }
        }
        
    } catch (error) {
        addMessageToMainChat('抱歉，发送消息时出现错误，请重试。', false);
    } finally {
        // Re-enable input controls
        const input = document.getElementById('main-chat-input');
        const button = document.querySelector('#chat-section .send-button');
        if (input) input.disabled = false;
        if (button) button.disabled = false;
        if (input) input.focus();
    }
}

function updateMainAIMessage(content) {
    const chatbox = document.getElementById('main-chatbox');
    let lastAIMessage = chatbox.querySelector('.ai-message:last-child');
    
    if (!lastAIMessage || lastAIMessage.classList.contains('completed')) {
        addMessageToMainChat(content, false);
        lastAIMessage = chatbox.querySelector('.ai-message:last-child');
        lastAIMessage.setAttribute('data-streaming', 'true');
    } else if (lastAIMessage.getAttribute('data-streaming') === 'true') {
        lastAIMessage.querySelector('p').textContent = content;
    } else {
        // Previous message was completed, create new one
        addMessageToMainChat(content, false);
        lastAIMessage = chatbox.querySelector('.ai-message:last-child');
        lastAIMessage.setAttribute('data-streaming', 'true');
    }
}

function markStreamingComplete(chatboxId) {
    const chatbox = document.getElementById(chatboxId);
    const lastAIMessage = chatbox.querySelector('.ai-message:last-child');
    if (lastAIMessage) {
        lastAIMessage.setAttribute('data-streaming', 'false');
        lastAIMessage.classList.add('completed');
    }
}

// === Products Functions ===
async function loadProducts() {
    showLoading('products-loading');
    
    try {
        const response = await fetch('/api/v1/goods', {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/goods', null, result, 'products-response');
        
        // Handle the API response format: {data: {items: [...], total: ...}}
        const products = result.data?.items || result.data || [];
        displayProducts(products);
        showLoading('products-loading', false);
    } catch (error) {
        showError('products-error', '加载商品失败: ' + error.message);
        showLoading('products-loading', false);
    }
}

function displayProducts(products) {
    const container = document.getElementById('products-grid');
    
    if (products && products.length > 0) {
        const productsHtml = products.map(product => `
            <div class="card">
                <div class="card-title">${product.name || product.title}</div>
                <p>价格: ¥${product.price || '暂无价格'}</p>
                <p>${product.description || '暂无描述'}</p>
                <button class="btn btn-success" onclick="addToCart('${product.id}')">🛒 加入购物车</button>
            </div>
        `).join('');
        container.innerHTML = productsHtml;
    } else {
        container.innerHTML = '<p>暂无商品数据</p>';
    }
}

async function searchProducts() {
    const keyword = document.getElementById('product-search').value;
    if (!keyword) {
        loadProducts();
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/goods/search?q=${encodeURIComponent(keyword)}`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', `/api/v1/goods/search?q=${keyword}`, null, result, 'products-response');
        
        // Handle the API response format: {data: {items: [...], total: ...}}
        const products = result.data?.items || result.data || [];
        displayProducts(products);
    } catch (error) {
        showError('products-error', '搜索商品失败: ' + error.message);
    }
}

// === Cart Functions ===
async function addToCart(goodsId) {
    try {
        const data = {
            goods_id: goodsId,
            count: 1
        };
        
        const response = await fetch('/api/v1/cart', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/cart/add', data, result, 'cart-response');
        
        if (response.ok) {
            alert('商品已添加到购物车！');
            loadOverview();
        } else {
            throw new Error('添加到购物车失败');
        }
    } catch (error) {
        alert('添加到购物车失败: ' + error.message);
    }
}

async function loadCart() {
    showLoading('cart-loading');
    
    try {
        const response = await fetch(`/api/v1/cart`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/cart', null, result, 'cart-response');
        
        // Cart API returns array directly, not wrapped in {data: ...}
        const cartItems = Array.isArray(result) ? result : (result.data || []);
        displayCart(cartItems);
        showLoading('cart-loading', false);
    } catch (error) {
        showError('cart-error', '加载购物车失败: ' + error.message);
        showLoading('cart-loading', false);
    }
}

function displayCart(cartItems) {
    const container = document.getElementById('cart-items');
    
    if (cartItems && cartItems.length > 0) {
        const cartHtml = cartItems.map(item => {
            // Use the cart item ID, handle both count and quantity field names
            const itemId = item.id || item.cart_id;
            const itemCount = item.count || item.quantity || 0;
            const goodsName = item.goods_name || item.name || '商品';
            
            return `
                <div style="padding: 10px; border-bottom: 1px solid #eee;">
                    <strong>${goodsName}</strong><br>
                    <span>数量: ${itemCount}</span><br>
                    <button class="btn btn-warning" onclick="updateCartItem('${itemId}', ${itemCount + 1})">➕</button>
                    <button class="btn btn-warning" onclick="updateCartItem('${itemId}', ${Math.max(1, itemCount - 1)})">➖</button>
                    <button class="btn btn-danger" onclick="removeFromCart('${itemId}')">🗑️ 删除</button>
                </div>
            `;
        }).join('');
        container.innerHTML = cartHtml;
    } else {
        container.innerHTML = '<p>购物车为空</p>';
    }
}

async function updateCartItem(itemId, count) {
    try {
        const data = {
            count: count
        };
        
        const response = await fetch(`/api/v1/cart/${itemId}`, {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        logApiCall('PUT', `/api/v1/cart/${itemId}`, data, result, 'cart-response');
        
        if (response.ok) {
            loadCart();
        } else {
            throw new Error(result.message || '更新购物车失败');
        }
    } catch (error) {
        showError('cart-error', '更新购物车失败: ' + error.message);
    }
}

async function removeFromCart(itemId) {
    try {
        const response = await fetch(`/api/v1/cart/${itemId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('DELETE', `/api/v1/cart/${itemId}`, null, result, 'cart-response');
        
        if (response.ok) {
            loadCart();
            loadOverview();
        } else {
            throw new Error(result.message || '删除商品失败');
        }
    } catch (error) {
        showError('cart-error', '删除商品失败: ' + error.message);
    }
}

async function clearCart() {
    if (confirm('确定要清空购物车吗？')) {
        try {
            // Get all cart items first
            const response = await fetch(`/api/v1/cart`, {
                headers: getAuthHeaders()
            });
            
            if (!response.ok) {
                throw new Error('获取购物车失败');
            }
            
            const result = await response.json();
            const cartItems = Array.isArray(result) ? result : (result.data || []);
            
            // Delete each item
            let deletedCount = 0;
            for (const item of cartItems) {
                try {
                    const itemId = item.id || item.cart_id;
                    const deleteResponse = await fetch(`/api/v1/cart/${itemId}`, {
                        method: 'DELETE',
                        headers: getAuthHeaders()
                    });
                    
                    if (deleteResponse.ok) {
                        deletedCount++;
                    }
                } catch (error) {
                    console.error('删除购物车项目失败:', error);
                }
            }
            
            if (deletedCount > 0) {
                alert(`已清空购物车，删除了 ${deletedCount} 个商品`);
                loadCart();
                loadOverview();
            } else {
                alert('购物车已经是空的');
            }
        } catch (error) {
            showError('cart-error', '清空购物车失败: ' + error.message);
        }
    }
}

async function checkout() {
    try {
        // 1. 检查购物车是否为空
        const cartResponse = await fetch('/api/v1/cart', {
            headers: getAuthHeaders()
        });
        
        if (!cartResponse.ok) {
            throw new Error('无法获取购物车信息');
        }
        
        const cartResult = await cartResponse.json();
        const cartItems = cartResult.data || [];
        if (!cartItems || cartItems.length === 0) {
            alert('购物车为空，请先添加商品');
            showSection('products');
            return;
        }
        
        // 2. 检查用户地址
        const addressResponse = await fetch('/api/v1/addresses', {
            headers: getAuthHeaders()
        });
        
        let addresses = [];
        if (addressResponse.ok) {
            const addressResult = await addressResponse.json();
            addresses = Array.isArray(addressResult) ? addressResult : (addressResult.data || []);
        }
        
        if (addresses.length === 0) {
            if (confirm('您还没有收货地址，是否前往添加地址？')) {
                showSection('addresses');
            }
            return;
        }
        
        // 3. 创建结算确认对话框
        const selectedItems = cartItems.filter(item => item.checked !== false);
        if (selectedItems.length === 0) {
            alert('请选择要结算的商品');
            return;
        }
        
        const totalPrice = selectedItems.reduce((sum, item) => sum + (item.price * item.count), 0);
        const defaultAddress = addresses.find(addr => addr.is_default) || addresses[0];
        
        const confirmMessage = `
确认结算信息：
• 商品数量：${selectedItems.length} 种商品，共 ${selectedItems.reduce((sum, item) => sum + item.count, 0)} 件
• 总金额：¥${totalPrice.toFixed(2)}
• 收货地址：${defaultAddress.name} ${defaultAddress.mobile}
  ${defaultAddress.province} ${defaultAddress.city} ${defaultAddress.district} ${defaultAddress.detail}

确认提交订单？`;
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        // 4. 创建订单
        const orderData = {
            address_id: defaultAddress.id,
            remark: '通过购物车结算创建的订单'
        };
        
        const orderResponse = await fetch('/api/v1/orders/create', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(orderData)
        });
        
        if (!orderResponse.ok) {
            const errorData = await orderResponse.json().catch(() => ({}));
            throw new Error(errorData.message || '创建订单失败');
        }
        
        const order = await orderResponse.json();
        
        // 5. 显示成功信息并跳转到订单页面
        alert(`订单创建成功！\n订单号：${order.order_no}\n订单金额：¥${order.total_price}\n\n即将跳转到订单页面...`);
        
        // 刷新购物车和订单列表
        if (document.getElementById('cart-grid')) {
            loadCart();
        }
        showSection('orders');
        
    } catch (error) {
        console.error('结算失败:', error);
        alert('结算失败: ' + error.message);
    }
}

// === Address Functions ===
async function loadAddresses() {
    showLoading('addresses-loading');
    
    try {
        const response = await fetch(`/api/v1/addresses`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/addresses', null, result, 'addresses-response');
        
        // Handle different response formats
        const addresses = Array.isArray(result) ? result : (result.data || []);
        displayAddresses(addresses);
        showLoading('addresses-loading', false);
    } catch (error) {
        showError('addresses-error', '加载地址失败: ' + error.message);
        showLoading('addresses-loading', false);
    }
}

function displayAddresses(addresses) {
    const container = document.getElementById('addresses-list');
    
    if (addresses && addresses.length > 0) {
        const addressesHtml = addresses.map(addr => `
            <div style="padding: 10px; border-bottom: 1px solid #eee;">
                <strong>${addr.name}</strong> ${addr.mobile}<br>
                <span>${addr.province} ${addr.city} ${addr.district} ${addr.detail}</span><br>
                <button class="btn btn-warning" onclick="editAddress('${addr.id}')">编辑</button>
                <button class="btn btn-danger" onclick="deleteAddress('${addr.id}')">删除</button>
            </div>
        `).join('');
        container.innerHTML = addressesHtml;
    } else {
        container.innerHTML = '<p>暂无收货地址</p>';
    }
}

function showAddAddressForm() {
    document.getElementById('add-address-form').style.display = 'block';
}

function hideAddAddressForm() {
    document.getElementById('add-address-form').style.display = 'none';
}

async function addAddress() {
    try {
        const data = {
            name: document.getElementById('address-name').value,
            mobile: document.getElementById('address-phone').value,
            province: document.getElementById('address-province').value,
            city: document.getElementById('address-city').value,
            district: document.getElementById('address-district').value,
            detail: document.getElementById('address-detail').value,
            is_default: false
        };
        
        const response = await fetch('/api/v1/addresses', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/addresses', data, result, 'addresses-response');
        
        if (response.ok) {
            alert('地址添加成功！');
            hideAddAddressForm();
            loadAddresses();
        } else {
            throw new Error('添加地址失败');
        }
    } catch (error) {
        showError('addresses-error', '添加地址失败: ' + error.message);
    }
}

function editAddress(addressId) {
    // For now, show a simplified edit approach
    // In a real implementation, you would populate the form with existing data
    alert('请使用添加地址功能重新创建地址。完整的编辑功能正在开发中。');
    showAddAddressForm();
}

async function deleteAddress(addressId) {
    if (confirm('确定要删除此地址吗？')) {
        try {
            const response = await fetch(`/api/v1/addresses/${addressId}`, {
                method: 'DELETE',
                headers: getAuthHeaders()
            });
            
            const result = await response.json();
            logApiCall('DELETE', `/api/v1/addresses/${addressId}`, null, result, 'addresses-response');
            
            if (response.ok) {
                alert('地址删除成功！');
                loadAddresses();
            } else {
                throw new Error(result.message || '删除地址失败');
            }
        } catch (error) {
            showError('addresses-error', '删除地址失败: ' + error.message);
        }
    }
}

// === Orders Functions ===
async function loadOrders() {
    showLoading('orders-loading');
    
    try {
        const response = await fetch(`/api/v1/orders/list`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/orders/list', null, result, 'orders-response');
        
        // Handle different response formats
        const orders = Array.isArray(result) ? result : (result.data || []);
        displayOrders(orders);
        showLoading('orders-loading', false);
    } catch (error) {
        showError('orders-error', '加载订单失败: ' + error.message);
        showLoading('orders-loading', false);
    }
}

function getOrderStatusText(status) {
    const statusMap = {
        0: '待支付',
        1: '已支付',
        2: '已发货',
        3: '已完成',
        4: '已取消'
    };
    return statusMap[status] || '未知状态';
}

function displayOrders(orders) {
    const container = document.getElementById('orders-list');
    
    if (orders && orders.length > 0) {
        const ordersHtml = orders.map(order => {
            const statusText = getOrderStatusText(order.status);
            return `
            <div style="padding: 10px; border-bottom: 1px solid #eee;">
                <strong>订单号: ${order.order_no || order.id}</strong><br>
                <span>状态: <span class="status-badge status-info">${statusText}</span></span><br>
                <span>金额: ¥${order.total_price || '0.00'}</span><br>
                <small>创建时间: ${new Date(order.create_time).toLocaleString()}</small><br>
                <button class="btn btn-outline" onclick="viewOrder('${order.id}')">查看详情</button>
                ${order.status === 0 ? `<button class="btn btn-warning" onclick="payOrder('${order.id}')">支付</button>` : ''}
            </div>
        `}).join('');
        container.innerHTML = ordersHtml;
    } else {
        container.innerHTML = '<p>暂无订单</p>';
    }
}

async function viewOrder(orderId) {
    try {
        const response = await fetch(`/api/v1/orders/${orderId}`, {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            const result = await response.json();
            const order = result.data || result;
            
            // Create a detailed order view
            const orderDetails = `
订单详情:
订单号: ${order.order_no || order.id}
状态: ${getOrderStatusText(order.status)}
金额: ¥${order.total_price || '0.00'}
创建时间: ${new Date(order.create_time).toLocaleString()}
${order.order_goods && order.order_goods.length > 0 ? `商品: ${order.order_goods.map(item => item.name).join(', ')}` : ''}
            `.trim();
            
            alert(orderDetails);
        } else {
            // Fallback for basic implementation
            alert(`查看订单详情: ${orderId}\n\n功能正在完善中，更多详情请联系客服。`);
        }
    } catch (error) {
        // Fallback for any error
        alert(`查看订单详情: ${orderId}\n\n无法获取详细信息: ${error.message}`);
    }
}

async function payOrder(orderId) {
    try {
        const response = await fetch(`/api/v1/orders/${orderId}/pay`, {
            method: 'POST',
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('POST', `/api/v1/orders/${orderId}/pay`, null, result, 'orders-response');
        
        if (response.ok) {
            alert('支付成功！');
            loadOrders();
        } else {
            throw new Error('支付失败');
        }
    } catch (error) {
        alert('支付失败: ' + error.message);
    }
}

// === Profile Functions ===
async function loadProfile() {
    if (currentUser) {
        document.getElementById('profile-username').value = currentUser.username;
        document.getElementById('profile-email').value = currentUser.email;
        document.getElementById('profile-mobile').value = currentUser.mobile;
        document.getElementById('profile-gender').value = currentUser.gender || '';
        document.getElementById('profile-age').value = currentUser.age || '';
    }
}

async function updateProfile() {
    try {
        const data = {
            gender: document.getElementById('profile-gender').value || null,
            age: parseInt(document.getElementById('profile-age').value) || null
        };
        
        // Remove null values to avoid sending unnecessary data
        Object.keys(data).forEach(key => {
            if (data[key] === null || data[key] === '') {
                delete data[key];
            }
        });
        
        const response = await fetch('/api/v1/users/me', {
            method: 'PUT',
            headers: getAuthHeaders(),
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            // Update the current user object
            currentUser.gender = result.gender;
            currentUser.age = result.age;
            
            // Save updated user info to localStorage
            localStorage.setItem('mastea_user', JSON.stringify(currentUser));
            
            alert('个人信息更新成功！');
            
            // Refresh the overview to show updated info
            loadOverview();
        } else {
            throw new Error(result.message || result.detail || '更新失败');
        }
    } catch (error) {
        showError('profile-error', '更新个人信息失败: ' + error.message);
    }
}

// === Debug Functions ===
function showDebugTab(tabName) {
    isDebugMode = true;
    document.querySelectorAll('#api-debug-section .tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('#api-debug-section .tab-content').forEach(content => content.classList.remove('active'));
    
    event.target.classList.add('active');
    document.getElementById(tabName + '-debug').classList.add('active');
}

async function testRegisterAPI() {
    const timestamp = Date.now().toString(36);
    const userData = {
        username: `testuser_${timestamp}`,
        email: `test_${timestamp}@example.com`,
        mobile: `138${timestamp.substr(-8)}`,
        password: '123456'
    };
    
    try {
        const response = await fetch('/api/v1/auth/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(userData)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/auth/register', userData, result, 'auth-debug-response');
    } catch (error) {
        logApiCall('POST', '/api/v1/auth/register', userData, { error: error.message }, 'auth-debug-response');
    }
}

async function testLoginAPI() {
    const loginData = {
        username: 'testuser123',
        password: '123456'
    };
    
    try {
        const response = await fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(loginData)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/auth/login', loginData, result, 'auth-debug-response');
    } catch (error) {
        logApiCall('POST', '/api/v1/auth/login', loginData, { error: error.message }, 'auth-debug-response');
    }
}

async function testUserInfoAPI() {
    if (!authToken) {
        logApiCall('GET', '/api/v1/users/me', null, { error: 'No auth token' }, 'auth-debug-response');
        return;
    }
    
    try {
        const response = await fetch('/api/v1/users/me', {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/users/me', null, result, 'auth-debug-response');
    } catch (error) {
        logApiCall('GET', '/api/v1/users/me', null, { error: error.message }, 'auth-debug-response');
    }
}

async function testConstitutionListAPI() {
    if (!currentUser) {
        logApiCall('GET', '/api/v1/reports/constitution/list', null, { error: 'No current user' }, 'reports-debug-response');
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/reports/constitution/list?user_id=${currentUser.id}`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/reports/constitution/list', null, result, 'reports-debug-response');
    } catch (error) {
        logApiCall('GET', '/api/v1/reports/constitution/list', null, { error: error.message }, 'reports-debug-response');
    }
}

async function testComplaintListAPI() {
    if (!currentUser) {
        logApiCall('GET', '/api/v1/reports/complaint/list', null, { error: 'No current user' }, 'reports-debug-response');
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/reports/complaint/list?user_id=${currentUser.id}`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/reports/complaint/list', null, result, 'reports-debug-response');
    } catch (error) {
        logApiCall('GET', '/api/v1/reports/complaint/list', null, { error: error.message }, 'reports-debug-response');
    }
}

async function testCreateConstitutionAPI() {
    if (!currentUser) {
        logApiCall('POST', '/api/v1/reports/constitution/calculate', null, { error: 'No current user' }, 'reports-debug-response');
        return;
    }
    
    const reportData = {
        user_id: currentUser.id,
        answers: {
            gender: "男",
            age_group: "30-40",
            q3_core_symptom: "疲劳乏力，容易感冒",
            q12_priority_conditioning: "增强体质"
        }
    };
    
    try {
        const response = await fetch('/api/v1/reports/constitution/calculate', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(reportData)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/reports/constitution/calculate', reportData, result, 'reports-debug-response');
    } catch (error) {
        logApiCall('POST', '/api/v1/reports/constitution/calculate', reportData, { error: error.message }, 'reports-debug-response');
    }
}

async function testConversationsAPI() {
    if (!currentUser) {
        logApiCall('GET', '/api/v1/chat/conversations', null, { error: 'No current user' }, 'chat-debug-response');
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/chat/conversations?user_id=${currentUser.id}`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/chat/conversations', null, result, 'chat-debug-response');
    } catch (error) {
        logApiCall('GET', '/api/v1/chat/conversations', null, { error: error.message }, 'chat-debug-response');
    }
}

async function testCreateConversationAPI() {
    try {
        const response = await fetch('/api/v1/chat/conversations', {
            method: 'POST',
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/chat/conversations', null, result, 'chat-debug-response');
    } catch (error) {
        logApiCall('POST', '/api/v1/chat/conversations', null, { error: error.message }, 'chat-debug-response');
    }
}

async function testSendMessageAPI() {
    if (!currentConversationId) {
        logApiCall('POST', '/api/v1/chat/send_stream', null, { error: 'No conversation selected' }, 'chat-debug-response');
        return;
    }
    
    const messageData = {
        user_id: currentUser.id,
        conversation_id: currentConversationId,
        message: "测试消息",
        task_type: "general_chat"
    };
    
    try {
        const response = await fetch('/api/v1/chat/send_stream', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(messageData)
        });
        
        logApiCall('POST', '/api/v1/chat/send_stream', messageData, { status: response.status, note: 'Streaming response' }, 'chat-debug-response');
    } catch (error) {
        logApiCall('POST', '/api/v1/chat/send_stream', messageData, { error: error.message }, 'chat-debug-response');
    }
}

async function testGoodsListAPI() {
    try {
        const response = await fetch('/api/v1/goods', {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/goods', null, result, 'goods-debug-response');
    } catch (error) {
        logApiCall('GET', '/api/v1/goods', null, { error: error.message }, 'goods-debug-response');
    }
}

async function testCartAPI() {
    if (!currentUser) {
        logApiCall('GET', '/api/v1/cart', null, { error: 'No current user' }, 'goods-debug-response');
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/cart?user_id=${currentUser.id}`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/cart', null, result, 'goods-debug-response');
    } catch (error) {
        logApiCall('GET', '/api/v1/cart', null, { error: error.message }, 'goods-debug-response');
    }
}

async function testAddToCartAPI() {
    if (!currentUser) {
        logApiCall('POST', '/api/v1/cart/add', null, { error: 'No current user' }, 'goods-debug-response');
        return;
    }
    
    const cartData = {
        goods_id: 'test-goods-id',
        quantity: 1,
        user_id: currentUser.id
    };
    
    try {
        const response = await fetch('/api/v1/cart/add', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(cartData)
        });
        
        const result = await response.json();
        logApiCall('POST', '/api/v1/cart/add', cartData, result, 'goods-debug-response');
    } catch (error) {
        logApiCall('POST', '/api/v1/cart/add', cartData, { error: error.message }, 'goods-debug-response');
    }
}

async function testOrdersListAPI() {
    if (!currentUser) {
        logApiCall('GET', '/api/v1/orders/list', null, { error: 'No current user' }, 'orders-debug-response');
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/orders/list?user_id=${currentUser.id}`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/orders/list', null, result, 'orders-debug-response');
    } catch (error) {
        logApiCall('GET', '/api/v1/orders/list', null, { error: error.message }, 'orders-debug-response');
    }
}

async function testAddressesAPI() {
    if (!currentUser) {
        logApiCall('GET', '/api/v1/addresses', null, { error: 'No current user' }, 'orders-debug-response');
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/addresses?user_id=${currentUser.id}`, {
            headers: getAuthHeaders()
        });
        
        const result = await response.json();
        logApiCall('GET', '/api/v1/addresses', null, result, 'orders-debug-response');
    } catch (error) {
        logApiCall('GET', '/api/v1/addresses', null, { error: error.message }, 'orders-debug-response');
    }
}

// === Initialization ===
document.addEventListener('DOMContentLoaded', function() {
    const savedToken = localStorage.getItem('mastea_token');
    const savedUser = localStorage.getItem('mastea_user');
    
    if (savedToken && savedUser) {
        try {
            authToken = savedToken;
            currentUser = JSON.parse(savedUser);
            showDashboard();
        } catch (error) {
            console.error('恢复登录状态失败:', error);
            logout();
        }
    }
});
# 麦斯特养生茶后端系统使用指南

## 项目概述

麦斯特养生茶后端系统是一个基于FastAPI框架开发的智能茶品推荐系统，提供体质评估、主诉分析、个性化茶品推荐等功能。系统支持微信小程序登录，具备完整的用户管理、商品管理、订单管理和AI对话功能。

## 环境要求

- Python 3.12+
- PostgreSQL 15+
- Docker 20.10+ (用于容器化部署)
- Docker Compose 1.29+
- 操作系统：Linux/macOS/Windows

## 环境配置

### 1. 环境变量设置

创建 `.env` 文件并配置以下变量：

```bash
# 微信小程序配置
WECHAT_APP_ID="your_wechat_app_id"
WECHAT_APP_SECRET="your_wechat_app_secret"

# 数据库配置 - 本地开发
# 使用本地PostgreSQL数据库保持环境一致性
DATABASE_URL="postgresql://mastea:wyh12257410@localhost:5432/mastea_dev"
USE_POSTGRES=true

# 安全配置
SECRET_KEY="your-secret-key-for-development"
JWT_SECRET_KEY="your-jwt-secret-key"

# AI配置
SILICONFLOW_API_KEY="your_siliconflow_api_key"
AI_API_BASE_URL="https://api.siliconflow.cn/v1"
AI_MODEL_ID="deepseek-ai/DeepSeek-V3"

# 环境标识
ENVIRONMENT="dev"
```

### 2. 数据库设置

#### PostgreSQL安装和配置：

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install postgresql postgresql-contrib

# macOS
brew install postgresql
brew services start postgresql

# Windows
# 下载并安装PostgreSQL官方安装包
```

#### 创建数据库：

```sql
-- 连接到PostgreSQL
psql -U postgres

-- 创建数据库和用户
CREATE DATABASE mastea_dev;
CREATE USER mastea WITH PASSWORD 'wyh12257410';
GRANT ALL PRIVILEGES ON DATABASE mastea_dev TO mastea;

-- 生产环境数据库
CREATE DATABASE mastea_prod;
GRANT ALL PRIVILEGES ON DATABASE mastea_prod TO mastea;
```

#### 使用数据库初始化脚本：

```bash
# 使用提供的初始化脚本
./scripts/init-database.sh localhost 5432 mastea wyh12257410 mastea_dev
```

### 3. Python环境设置

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/macOS
source venv/bin/activate
# Windows
venv\Scripts\activate

# 安装依赖
pip install -r k_api/requirements.txt
```

## 安装和启动

### 1. 基础安装

```bash
# 克隆项目
git clone <repository-url>
cd Mastea_flask

# 安装依赖
pip install -r k_api/requirements.txt

# 数据库迁移
alembic upgrade head

# 初始化测试数据（可选）
python -m fastapi_app.initial_data
```

### 2. 启动服务

```bash
# 方式1：使用提供的启动脚本
python run_fastapi.py

# 方式2：直接使用uvicorn
uvicorn fastapi_app.main:app --host 0.0.0.0 --port 8000 --reload

# 方式3：使用测试服务器
python start_test_server.py
```

### 3. 验证安装

```bash
# 检查服务状态
curl http://localhost:8000/api/v1/health

# 预期响应：
# {"status":"healthy","timestamp":**********,"database":"connected","version":"1.0.0"}
```

## API文档

### 1. 访问API文档

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### 2. 核心API端点

#### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/wechat/login` - 微信小程序登录

#### 用户管理
- `GET /api/v1/users/me` - 获取当前用户信息
- `PUT /api/v1/users/me` - 更新用户信息

#### 商品管理
- `GET /api/v1/goods/` - 获取商品列表
- `GET /api/v1/goods/{goods_id}` - 获取商品详情

#### 购物车
- `GET /api/v1/cart/` - 获取购物车
- `POST /api/v1/cart/add` - 添加商品到购物车
- `PUT /api/v1/cart/update` - 更新购物车商品数量

#### 订单管理
- `POST /api/v1/orders/create` - 创建订单
- `GET /api/v1/orders/list` - 获取订单列表
- `GET /api/v1/orders/{order_id}` - 获取订单详情
- `POST /api/v1/orders/{order_id}/pay` - 支付订单
- `POST /api/v1/orders/{order_id}/cancel` - 取消订单
- `POST /api/v1/orders/{order_id}/confirm` - 确认收货

#### AI对话
- `GET /api/v1/chat/conversations` - 获取对话列表
- `POST /api/v1/chat/conversations` - 创建新对话
- `GET /api/v1/chat/conversations/{conversation_id}/messages` - 获取对话消息
- `POST /api/v1/chat/send_stream` - 发送消息（流式响应）

#### 报告管理
- `GET /api/v1/reports` - 获取报告列表
- `GET /api/v1/reports/{report_id}` - 获取报告详情

## 前端集成

### 1. 仪表板页面

系统提供两个主要的管理页面：

- **增强版仪表板**: http://localhost:8000/dashboard_enhanced
  - 完整的功能集成
  - AI对话界面
  - 商品管理
  - 订单管理
  - 报告查看

- **用户仪表板**: http://localhost:8000/dashboard
  - 用户友好的界面
  - 基础功能操作
  - 简化的交互流程

### 2. 认证集成

前端需要处理JWT令牌认证：

```javascript
// 登录示例
const login = async (username, password) => {
    const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
    });
    
    const data = await response.json();
    if (data.code === 0) {
        localStorage.setItem('token', data.data.token);
        return data.data;
    }
    throw new Error(data.message);
};

// 使用令牌的API调用
const fetchWithAuth = async (url, options = {}) => {
    const token = localStorage.getItem('token');
    return fetch(url, {
        ...options,
        headers: {
            ...options.headers,
            'Authorization': `Bearer ${token}`,
        },
    });
};
```

## AI对话系统

### 1. 对话流程

AI对话系统支持两种主要的评估类型：

#### 基础体质评估
- **前端处理**: 通过前端表单收集用户基本信息
- **规则引擎**: 使用预定义规则生成体质评估报告
- **无AI对话**: 不需要AI参与，快速生成结果
- **报告格式**: 直接返回标准化的体质评估结果
- **使用方式**: 在仪表板的"健康评估"页面填写基础信息即可

#### 主诉评估（AI对话）
- **前置条件**: 基于用户的体质评估报告
- **AI交互**: 通过AI对话深入了解用户的健康问题
- **个性化**: 生成个性化的调理建议和茶品推荐
- **自动化启动**: 选择体质报告后，系统自动发送初始消息给AI
- **流程**: 体质报告 → 自动发送基础信息 → AI问诊 → 综合分析 → 茶品推荐
- **操作步骤**: 
  1. 完成基础体质评估
  2. 在报告列表中点击"用于主诉评估"
  3. 系统自动切换到主诉评估页面并发送初始消息
  4. 与AI进行对话，描述具体症状
  5. AI生成综合评估报告

### 2. AI对话API使用

```javascript
// 创建对话
const createConversation = async (taskType = 'chief_complaint') => {
    const response = await fetchWithAuth('/api/v1/chat/conversations', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            task_type: taskType,
            title: '综合主诉评估'
        }),
    });
    return response.json();
};

// 发送消息（流式响应）
const sendMessage = async (conversationId, message, taskType = 'chief_complaint') => {
    const response = await fetchWithAuth('/api/v1/chat/send_stream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            user_id: currentUser.id,
            conversation_id: conversationId,
            message: message,
            task_type: taskType
        }),
    });
    
    return response.body.getReader();
};
```

### 3. 流式响应处理

```javascript
const handleStreamResponse = async (reader) => {
    const decoder = new TextDecoder();
    let buffer = '';
    
    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';
            
            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]') return;
                    
                    try {
                        const parsed = JSON.parse(data);
                        handleStreamEvent(parsed);
                    } catch (e) {
                        console.error('Parse error:', e);
                    }
                }
            }
        }
    } catch (error) {
        console.error('Stream error:', error);
    }
};

const handleStreamEvent = (event) => {
    switch (event.type) {
        case 'connection_established':
            console.log('连接建立');
            break;
        case 'ai_chunk':
            // 逐字符显示AI回复
            appendToUI(event.data.content);
            break;
        case 'ai_complete':
            // AI回复完成
            console.log('AI回复完成');
            break;
        case 'task_complete':
            // 任务完成，生成了报告
            handleReport(event.data.report_data);
            break;
        case 'error':
            console.error('AI错误:', event.data.message);
            break;
    }
};
```

## 数据模型

### 1. 用户模型
```python
class User:
    id: str
    username: str
    email: str
    mobile: str
    wechat_openid: str
    wechat_unionid: str
    is_active: bool
    is_member: bool
    created_at: datetime
    updated_at: datetime
```

### 2. 商品模型
```python
class Goods:
    id: str
    name: str
    description: str
    price: float
    original_price: float
    image: str
    category_name: str
    tea_type: str
    effect: str
    status: int  # 0-上架, 1-下架
```

### 3. 订单模型
```python
class Order:
    id: str
    order_no: str
    user_id: str
    status: int  # 0-待支付, 1-已支付, 2-已发货, 3-已完成, 4-已取消
    total_price: float
    pay_price: float
    address_snapshot: str
    order_goods: List[OrderGoods]
```

## 部署说明

### 1. 本地开发部署

```bash
# 使用本地PostgreSQL数据库
export DATABASE_URL="postgresql://mastea:wyh12257410@localhost:5432/mastea_dev"
python run_fastapi.py
```

### 2. Docker本地部署

```bash
# 1. 构建Docker镜像
./scripts/build.sh

# 2. 启动所有服务（包括数据库）
docker-compose up -d

# 3. 查看服务状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f
```

### 3. 生产环境部署

```bash
# 1. 构建并标记生产镜像
./scripts/build.sh tag your-registry/mastea-flask:v1.0

# 2. 推送到镜像仓库
docker push your-registry/mastea-flask:v1.0

# 3. 使用部署脚本
./scripts/deploy.sh your-registry/mastea-flask:v1.0 production
```

### 4. 服务器部署

```bash
# 1. 下载服务器部署脚本
curl -O https://raw.githubusercontent.com/your-org/mastea-flask/main/scripts/server-deploy.sh

# 2. 运行服务器部署
sudo chmod +x server-deploy.sh
sudo ./server-deploy.sh /opt/mastea your-registry/mastea-flask:v1.0 your-domain.com

# 3. 检查服务状态
systemctl status mastea-flask
```

### 5. 数据库迁移

```bash
# 开发环境
./scripts/init-database.sh localhost 5432 mastea wyh12257410 mastea_dev

# 生产环境
./scripts/init-database.sh prod-db-host 5432 mastea your-password mastea_prod
```

## 故障排除

### 1. 常见问题

#### 数据库连接问题
```bash
# 检查数据库连接
psql -h localhost -U mastea_user -d mastea_dev

# 检查数据库迁移状态
alembic current
alembic history
```

#### 微信配置问题
```bash
# 检查微信配置
curl -X POST http://localhost:8000/api/v1/wechat/login \
  -H "Content-Type: application/json" \
  -d '{"code": "test_code"}'
```

#### AI服务问题
```bash
# 检查AI API密钥
curl -X POST "https://api.siliconflow.cn/v1/chat/completions" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"model": "deepseek-ai/DeepSeek-V3", "messages": [{"role": "user", "content": "Hello"}]}'
```

#### 购物车和订单问题
```bash
# 测试购物车API
curl -X GET http://localhost:8000/api/v1/cart/ \
  -H "Authorization: Bearer YOUR_TOKEN"

# 测试订单列表API
curl -X GET http://localhost:8000/api/v1/orders/list \
  -H "Authorization: Bearer YOUR_TOKEN"

# 测试创建订单API
curl -X POST http://localhost:8000/api/v1/orders/create \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"address_id": "address_id", "remark": "测试订单"}'
```

#### AI对话问题
```bash
# 测试AI对话流式响应
curl -X POST http://localhost:8000/api/v1/chat/send_stream \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user_id", "conversation_id": "conv_id", "message": "你好", "task_type": "chief_complaint"}'
```

### 2. 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看服务器日志
tail -f server.log

# 查看数据库日志
sudo tail -f /var/log/postgresql/postgresql-*.log
```

### 3. 性能监控

```bash
# 查看系统资源
htop

# 查看数据库连接
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# 查看API响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/api/v1/health
```

## 开发指南

### 1. 项目结构

```
fastapi_app/
├── api/                 # API路由
│   └── v1/
│       └── endpoints/   # 具体端点实现
├── core/                # 核心配置
│   ├── config.py       # 配置管理
│   ├── database.py     # 数据库连接
│   └── security.py     # 安全相关
├── crud/                # 数据库操作
├── models/              # 数据模型
├── schemas/             # Pydantic模式
├── services/            # 业务逻辑
├── static/              # 静态文件
├── templates/           # 模板文件
└── tests/               # 测试代码
```

### 2. 开发流程

```bash
# 1. 创建新分支
git checkout -b feature/new-feature

# 2. 添加新功能
# 编辑相关文件...

# 3. 运行测试
python -m pytest

# 4. 提交代码
git add .
git commit -m "Add new feature"

# 5. 推送分支
git push origin feature/new-feature
```

### 3. 代码规范

- 使用Black进行代码格式化
- 使用flake8进行代码检查
- 使用mypy进行类型检查
- 遵循PEP 8代码风格

## 安全注意事项

1. **环境变量管理**
   - 不要在代码中硬编码敏感信息
   - 使用.env文件管理配置
   - 生产环境使用安全的密钥管理

2. **API安全**
   - 所有API都需要适当的认证
   - 使用JWT令牌进行身份验证
   - 实现请求频率限制

3. **数据库安全**
   - 使用参数化查询防止SQL注入
   - 定期备份数据库
   - 限制数据库访问权限

## 联系信息

如有技术问题或需要支持，请联系：
- 技术支持邮箱：<EMAIL>
- 开发团队：<EMAIL>
- GitHub Issues：[项目Issues页面]

---

**版本**: 1.0.0  
**最后更新**: 2025-07-16  
**文档维护**: 开发团队
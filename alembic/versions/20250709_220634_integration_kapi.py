"""
整合k_api功能的数据库迁移
添加微信相关字段和购物车兼容性字段

Revision ID: integration_kapi_20250709_220634
Revises: 
Create Date: 2025-07-09T22:06:34.656054
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'integration_kapi_20250709_220634'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """升级数据库结构"""
    
    # 为用户表添加微信相关字段
    try:
        # 检查字段是否已存在，避免重复添加
        op.add_column('users', sa.Column('wechat_authorized', sa.<PERSON>(), default=False, nullable=True))
        op.add_column('users', sa.Column('wechat_auth_time', sa.DateTime(), nullable=True))
        op.add_column('users', sa.Column('is_wechat_user', sa.<PERSON>(), default=False, nullable=True))
        op.add_column('users', sa.Column('last_login_at', sa.DateTime(), nullable=True))
        op.add_column('users', sa.Column('is_active', sa.Boolean(), default=True, nullable=True))
        print("✅ 用户表微信字段添加成功")
    except Exception as e:
        print(f"⚠️  用户表字段可能已存在: {e}")
    
    # 为购物车表添加兼容性字段
    try:
        op.add_column('cart_items', sa.Column('quantity', sa.Integer(), default=1, nullable=True))
        op.add_column('cart_items', sa.Column('selected', sa.Boolean(), default=True, nullable=True))
        op.add_column('cart_items', sa.Column('product_type', sa.Integer(), default=0, nullable=True))
        print("✅ 购物车表兼容性字段添加成功")
    except Exception as e:
        print(f"⚠️  购物车表字段可能已存在: {e}")
    
    # 为商品表添加茶品特性字段（如果不存在）
    try:
        op.add_column('goods', sa.Column('series', sa.String(100), nullable=True))
        op.add_column('goods', sa.Column('effect', sa.String(200), nullable=True))
        op.add_column('goods', sa.Column('is_seasonal', sa.Boolean(), default=False, nullable=True))
        op.add_column('goods', sa.Column('is_help_sleep', sa.Boolean(), default=False, nullable=True))
        op.add_column('goods', sa.Column('is_energizing', sa.Boolean(), default=False, nullable=True))
        op.add_column('goods', sa.Column('is_weight_management', sa.Boolean(), default=False, nullable=True))
        op.add_column('goods', sa.Column('is_skin_care', sa.Boolean(), default=False, nullable=True))
        op.add_column('goods', sa.Column('is_digestive', sa.Boolean(), default=False, nullable=True))
        op.add_column('goods', sa.Column('ingredients', sa.Text(), nullable=True))
        op.add_column('goods', sa.Column('brewing_method', sa.Text(), nullable=True))
        op.add_column('goods', sa.Column('storage_method', sa.Text(), nullable=True))
        print("✅ 商品表茶品特性字段添加成功")
    except Exception as e:
        print(f"⚠️  商品表字段可能已存在: {e}")
    
    # 为体质报告表添加增强字段（如果不存在）
    try:
        op.add_column('constitution_reports', sa.Column('total_score', sa.Integer(), nullable=True))
        op.add_column('constitution_reports', sa.Column('primary_constitution', sa.String(50), nullable=True))
        op.add_column('constitution_reports', sa.Column('secondary_constitution', sa.String(50), nullable=True))
        op.add_column('constitution_reports', sa.Column('main_symptoms', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('constitution_definition', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('body_characteristics', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('common_symptoms', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('psychological_characteristics', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('susceptible_diseases', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('environmental_adaptation', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('season', sa.String(20), nullable=True))
        op.add_column('constitution_reports', sa.Column('health_advice', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('dietary_advice', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('sleep_advice', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('exercise_advice', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('emotional_advice', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('recommended_teas', sa.JSON(), nullable=True))
        op.add_column('constitution_reports', sa.Column('recommendation_reason', sa.Text(), nullable=True))
        op.add_column('constitution_reports', sa.Column('tea_usage_advice', sa.Text(), nullable=True))
        print("✅ 体质报告表增强字段添加成功")
    except Exception as e:
        print(f"⚠️  体质报告表字段可能已存在: {e}")
    
    # 同步quantity和count字段的数据
    try:
        # 将count字段的值复制到quantity字段
        op.execute("UPDATE cart_items SET quantity = count WHERE quantity IS NULL")
        # 将checked字段的值复制到selected字段
        op.execute("UPDATE cart_items SET selected = checked WHERE selected IS NULL")
        print("✅ 购物车字段数据同步成功")
    except Exception as e:
        print(f"⚠️  购物车字段数据同步失败: {e}")

def downgrade():
    """降级数据库结构"""
    
    # 删除用户表的微信字段
    try:
        op.drop_column('users', 'is_active')
        op.drop_column('users', 'last_login_at')
        op.drop_column('users', 'is_wechat_user')
        op.drop_column('users', 'wechat_auth_time')
        op.drop_column('users', 'wechat_authorized')
        print("✅ 用户表微信字段删除成功")
    except Exception as e:
        print(f"⚠️  用户表字段删除失败: {e}")
    
    # 删除购物车表的兼容性字段
    try:
        op.drop_column('cart_items', 'product_type')
        op.drop_column('cart_items', 'selected')
        op.drop_column('cart_items', 'quantity')
        print("✅ 购物车表兼容性字段删除成功")
    except Exception as e:
        print(f"⚠️  购物车表字段删除失败: {e}")
    
    # 删除商品表的茶品特性字段
    try:
        op.drop_column('goods', 'storage_method')
        op.drop_column('goods', 'brewing_method')
        op.drop_column('goods', 'ingredients')
        op.drop_column('goods', 'is_digestive')
        op.drop_column('goods', 'is_skin_care')
        op.drop_column('goods', 'is_weight_management')
        op.drop_column('goods', 'is_energizing')
        op.drop_column('goods', 'is_help_sleep')
        op.drop_column('goods', 'is_seasonal')
        op.drop_column('goods', 'effect')
        op.drop_column('goods', 'series')
        print("✅ 商品表茶品特性字段删除成功")
    except Exception as e:
        print(f"⚠️  商品表字段删除失败: {e}")
    
    # 删除体质报告表的增强字段
    try:
        op.drop_column('constitution_reports', 'tea_usage_advice')
        op.drop_column('constitution_reports', 'recommendation_reason')
        op.drop_column('constitution_reports', 'recommended_teas')
        op.drop_column('constitution_reports', 'emotional_advice')
        op.drop_column('constitution_reports', 'exercise_advice')
        op.drop_column('constitution_reports', 'sleep_advice')
        op.drop_column('constitution_reports', 'dietary_advice')
        op.drop_column('constitution_reports', 'health_advice')
        op.drop_column('constitution_reports', 'season')
        op.drop_column('constitution_reports', 'environmental_adaptation')
        op.drop_column('constitution_reports', 'susceptible_diseases')
        op.drop_column('constitution_reports', 'psychological_characteristics')
        op.drop_column('constitution_reports', 'common_symptoms')
        op.drop_column('constitution_reports', 'body_characteristics')
        op.drop_column('constitution_reports', 'constitution_definition')
        op.drop_column('constitution_reports', 'main_symptoms')
        op.drop_column('constitution_reports', 'secondary_constitution')
        op.drop_column('constitution_reports', 'primary_constitution')
        op.drop_column('constitution_reports', 'total_score')
        print("✅ 体质报告表增强字段删除成功")
    except Exception as e:
        print(f"⚠️  体质报告表字段删除失败: {e}")

import asyncio
import os
import json
import requests
from typing import List, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from fastapi_app.crud import chat as crud_chat, report as crud_report, goods as crud_goods
from fastapi_app.schemas.chat import ChatMessage
from fastapi_app.core.config import settings
import logging

# 添加与Flask版本一致的配置项
SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY", "sk-tfgujfpskkcjhtnhfhbvegnrthmmyebrzodvizydvdjmzfnl")
API_BASE_URL = os.getenv("AI_API_BASE_URL", "https://api.siliconflow.cn/v1")
CHAT_ENDPOINT = f"{API_BASE_URL}/chat/completions"
MODEL_ID = os.getenv("AI_MODEL_ID", "deepseek-ai/DeepSeek-V3")
USE_MOCK_AI = not SILICONFLOW_API_KEY or SILICONFLOW_API_KEY == "YOUR_API_KEY_HERE"

def load_prompt(name: str) -> str:
    """Loads a prompt from the 'docs' directory."""
    prompt_path = os.path.join("docs", f"{name}.md")
    try:
        with open(prompt_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"Warning: Prompt file not found at {prompt_path}")
        return ""

def get_products_data(db: Session) -> str:
    """获取产品数据并格式化为提示词"""
    try:
        # 获取所有商品数据
        from fastapi_app.crud import goods as crud_goods
        goods_list = crud_goods.get_all_goods(db)
        
        products_data = []
        for goods in goods_list:
            product_info = {
                "id": goods.id,
                "name": goods.name,
                "description": goods.description or "",
                "price": goods.price,
                "category": goods.category_name or "默认分类",
                "effect": goods.effect or "",
                "intro": goods.intro or "",
                "tea_type": goods.tea_type or "基础茶品"
            }
            products_data.append(product_info)
        
        return json.dumps(products_data, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.error(f"获取产品数据失败: {e}")
        return "[]"  # 返回空数组作为fallback

def prepare_system_prompt(task_type: str, db: Session, constitution_report: str = None) -> str:
    """准备系统提示词"""
    if task_type == "chief_complaint":
        prompt = load_prompt("chief_complaint")
        if constitution_report:
            prompt = prompt.replace("{constitution_report}", constitution_report)
        return prompt
    elif task_type == "report_generation":
        prompt = load_prompt("report_generation")
        products_data = get_products_data(db)
        prompt = prompt.replace("{products_data}", products_data)
        return prompt
    else:
        return ""

def get_mock_ai_response(message: str, task_type: str = None, history_messages: list = None, current_user_message_count: int = None) -> str:
    """生成简洁高效的模拟AI回复，遵循快速确认流程"""
    logger = logging.getLogger(__name__)
    if task_type == "chief_complaint":
        # 使用传入的当前用户消息数量，如果没有则从历史计算
        if current_user_message_count is not None:
            user_message_count = current_user_message_count
        else:
            user_message_count = len([msg for msg in history_messages if msg.is_user]) if history_messages else 0
        
        logger.info(f"当前用户消息数量: {user_message_count}, 最新消息: {message}")
        
        # 第一条用户消息（自动发送的体质报告）- 开始主诉确认
        if user_message_count <= 1:
            # 检查是否是体质报告数据
            if "[体质报告数据]" in message:
                return "基于您的体质特点，您目前主要感到**疲劳乏力**还是**气短胸闷**？请选择最困扰您的症状。"
            else:
                return "基于您的体质特点，您是否主要感到**疲劳乏力**或**气短胸闷**？请直接告诉我哪个症状更困扰您。"
        
        # 第二条用户消息 - 简单补充确认
        elif user_message_count == 2:
            if "疲劳" in message or "乏力" in message:
                return "这种疲劳主要是**持续性的**还是**活动后明显**？选择一个即可。"
            elif "气短" in message or "胸闷" in message:
                return "气短是**平时就有**还是**运动时明显**？选择一个即可。"
            else:
                return "您提到的症状，是**持续性**的还是**间歇性**的？"
        
        # 第三条用户消息 - 直接生成报告
        elif user_message_count >= 3:
            return "好的，我已了解您的主要情况。现在为您生成综合主诉评估报告..."
        
        return "请选择您的主要症状，我来为您评估。"
    
    # 其他情况的通用回复
    return "感谢您的分享，请继续告诉我您的具体情况。"

async def get_ai_response_stream(db: Session, user_id: str, conversation_id: str, history: List[ChatMessage], initial_prompt: str = None, user_message_data: dict = None, task_type: str = None):
    """
    生成AI回复的流式响应，并处理任务完成、保存数据等操作。
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始生成聊天响应流: 用户={user_id}, 对话={conversation_id}, 历史消息数={len(history)}")
    
    # 发送连接测试事件
    test_event = {
        "type": "connection_established", 
        "data": {"status": "ok", "message": "Stream started"}
    }
    yield f"data: {json.dumps(test_event, ensure_ascii=False)}\n\n"
    await asyncio.sleep(0.1)
    
    # 如果提供了用户消息数据，发送用户消息事件
    if user_message_data:
        user_msg_event = {"type": "user_message", "data": user_message_data}
        yield f"data: {json.dumps(user_msg_event, ensure_ascii=False)}\n\n"
        await asyncio.sleep(0.1)
    
    # 准备AI对话上下文
    messages_for_api = []
    
    # 准备系统提示词
    system_prompt = ""
    if task_type == "chief_complaint":
        # 主诉评估需要从用户消息中提取体质报告信息
        constitution_report = "（基础体质评估报告将在这里显示）"
        
        # 从最后一条用户消息中提取体质报告
        last_user_message = next((msg.message for msg in reversed(history) if msg.is_user), "")
        if "[体质报告数据]" in last_user_message:
            # 提取体质报告内容（在[体质报告数据]和[开始主诉评估]之间的部分）
            try:
                start_marker = "[体质报告数据]"
                end_marker = "[开始主诉评估]"
                start_pos = last_user_message.find(start_marker)
                end_pos = last_user_message.find(end_marker)
                
                if start_pos != -1 and end_pos != -1:
                    constitution_report = last_user_message[start_pos + len(start_marker):end_pos].strip()
                    logger.info(f"提取的体质报告: {constitution_report[:100]}...")
                else:
                    logger.warning("无法找到体质报告标记")
            except Exception as e:
                logger.error(f"提取体质报告时出错: {e}")
        elif "体质报告" in last_user_message or "体质评估" in last_user_message:
            # 兼容旧格式的提取逻辑
            lines = last_user_message.split('\n')
            report_lines = []
            in_report = False
            for line in lines:
                if '体质报告' in line or '体质评估' in line:
                    in_report = True
                    continue
                elif in_report and line.strip():
                    report_lines.append(line.strip())
                elif in_report and not line.strip() and len(report_lines) > 0:
                    break
            
            if report_lines:
                constitution_report = '\n'.join(report_lines)
                logger.info(f"提取的体质报告(兼容格式): {constitution_report[:100]}...")
        
        system_prompt = prepare_system_prompt(task_type, db, constitution_report)
    elif task_type == "report_generation":
        system_prompt = prepare_system_prompt(task_type, db)
    elif initial_prompt:
        system_prompt = initial_prompt
    
    if system_prompt:
        messages_for_api.append({"role": "system", "content": system_prompt})
    
    # 转换历史消息为API格式
    for msg in history:
        messages_for_api.append({
            "role": "user" if msg.is_user else "assistant",
            "content": msg.message
        })
    
    # 检查最后一条用户消息是否为报告生成请求
    last_user_message = next((msg.message for msg in reversed(history) if msg.is_user), "")
    is_report_generation_request = last_user_message == "<REQUEST_REPORT_GENERATION>"
    
    # 任务类型优先级：传入参数 > 对话标题推断 > 初始提示推断
    if not task_type:  # 如果没有传入task_type，才从其他来源推断
        # 从对话标题推断任务类型
        try:
            conversation = crud_chat.get_conversation_by_id(db, conversation_id)
            if conversation and conversation.title:
                if "主诉" in conversation.title:
                    task_type = "chief_complaint"
                elif "体质" in conversation.title:
                    task_type = "constitution_assessment"
        except Exception as e:
            logger.warning(f"无法从对话标题推断任务类型: {e}")
        
        # 如果还没有获取到，则从初始提示判断
        if not task_type:
            if initial_prompt and "主诉" in initial_prompt:
                task_type = "chief_complaint"
            # 基础体质评估现在由前端处理，不再使用AI对话
    
    logger.info(f"确定的任务类型: {task_type}")
    
    ai_response_full_text = ""
    parsed_json_report = None
    is_task_completed = False
    
    # 根据是否使用模拟响应选择不同的处理路径
    # 对于chief_complaint任务，强制使用结构化流程以确保一致性
    if USE_MOCK_AI or task_type == "chief_complaint":
        if USE_MOCK_AI:
            logger.info("API Key未有效配置，使用模拟回复")
        else:
            logger.info(f"chief_complaint任务使用结构化流程，任务类型: {task_type}")
        
        # 如果是报告生成请求，特殊处理
        if is_report_generation_request:
            logger.info(f"检测到报告生成请求，任务类型: {task_type}")
            if task_type == "chief_complaint":
                # 生成模拟的主诉报告
                ai_response = "非常好！基于我们的对话，我已经了解了您的主要健康状况。现在为您生成综合主诉评估报告..."
                # 创建模拟主诉报告数据
                mock_report_data = {
                    "report_type": "chief_complaint",
                    "assessment_date": datetime.utcnow().isoformat(),
                    "base_constitution": "气虚质",
                    "chief_complaint": {
                        "primary": "基于对话分析的主要症状",
                        "secondary": ["次要症状1", "次要症状2"],
                        "duration": "持续时间分析",
                        "severity": "中等程度"
                    },
                    "tcm_analysis": {
                        "syndrome_differentiation": "气虚血瘀证",
                        "affected_organs": ["脾", "肾"],
                        "pathogenesis": "气虚血瘀，脾肾不足",
                        "treatment_principle": "补气活血，健脾益肾"
                    },
                    "comprehensive_analysis": {
                        "constitution_relation": "体质特点与症状的关系分析",
                        "root_cause": "根本原因分析",
                        "prognosis": "预后良好，建议持续调理"
                    },
                    "treatment_plan": {
                        "primary_approach": "以茶饮调理为主，配合生活方式调整",
                        "lifestyle_modifications": ["保持规律作息", "适度运动"],
                        "dietary_therapy": ["建议多食用补气养血的食物", "避免寒凉生冷食物"],
                        "exercise_therapy": ["适量有氧运动", "太极八段锦等"]
                    },
                    "recommended_products": [
                        {
                            "product_id": "27f57d6f864b404eb10e592dafda2ca2",
                            "name": "提神茶",
                            "reason": "适合气虚体质，有助提神补气",
                            "usage": "每日上午饮用一次",
                            "priority": 1
                        }
                    ]
                }
                parsed_json_report = mock_report_data
                is_task_completed = True
            else:
                ai_response = "感谢您提供的信息，正在为您生成评估报告..."
        else:
            # 提取最后一条用户消息内容(通常是刚刚发送的)
            last_user_msg = last_user_message
            
            # 检查是否应该生成报告（历史已包含当前用户消息）
            user_messages = [msg for msg in history if msg.is_user]
            # 当前用户消息数量 = 历史中的用户消息（已包含当前消息）
            current_user_message_count = len(user_messages)
            
            # 获取模拟回复，传入正确的用户消息数量
            ai_response = get_mock_ai_response(last_user_msg, task_type, history, current_user_message_count)
            
            # 检查是否应该强制生成报告
            should_generate_report = False
            if task_type == "chief_complaint":
                # 触发条件：3轮对话后，或者用户要求生成报告
                logger.info(f"检查报告生成条件: 用户消息数={current_user_message_count}, 最后消息='{last_user_msg}'")
                if (current_user_message_count >= 3 or 
                    "生成报告" in last_user_msg or 
                    "结束对话" in last_user_msg or
                    "结束" in last_user_msg):
                    should_generate_report = True
                    logger.info(f"✅ 满足报告生成条件，设置should_generate_report=True")
            
            if should_generate_report:
                logger.info(f"强制生成主诉评估报告，用户消息数: {current_user_message_count}")
                
                # 基于对话历史生成个性化报告
                user_messages_text = [msg.message for msg in history if msg.is_user]
                
                # 分析用户症状
                primary_symptom = "疲劳乏力"  # 默认值
                severity = "中等程度"
                duration = "持续时间未明确"
                
                # 从对话中提取关键信息
                for msg in user_messages_text:
                    if "疲劳" in msg or "乏力" in msg:
                        primary_symptom = "疲劳乏力"
                    elif "气短" in msg or "胸闷" in msg:
                        primary_symptom = "气短胸闷"
                    elif "失眠" in msg or "睡不好" in msg:
                        primary_symptom = "失眠多梦"
                    
                    if "持续" in msg:
                        duration = "持续性症状"
                    elif "间歇" in msg:
                        duration = "间歇性症状"
                
                # 强制覆盖AI回复，生成JSON格式报告
                ai_response = f"好的，我已了解您的主要情况。现在为您生成综合主诉评估报告...\n\nSTART_JSON_REPORT\n"
                
                mock_report_data = {
                    "report_type": "chief_complaint",
                    "assessment_date": datetime.utcnow().isoformat(),
                    "base_constitution": "气虚质",
                    "chief_complaint": {
                        "primary": primary_symptom,
                        "secondary": ["相关症状待补充"],
                        "duration": duration,
                        "severity": severity
                    },
                    "tcm_analysis": {
                        "syndrome_differentiation": "气虚证",
                        "affected_organs": ["脾", "肺"],
                        "pathogenesis": "脾气虚弱，肺气不足",
                        "treatment_principle": "补气健脾，益肺固表"
                    },
                    "comprehensive_analysis": {
                        "constitution_relation": "气虚体质者易出现相关症状",
                        "root_cause": "先天禀赋不足，后天调养失当",
                        "prognosis": "预后良好，坚持调理可明显改善"
                    },
                    "treatment_plan": {
                        "primary_approach": "以补气茶饮为主，配合生活调理",
                        "lifestyle_modifications": ["保证充足睡眠", "避免过度劳累"],
                        "dietary_therapy": ["多食山药、大枣等补气食物", "少食寒凉食物"],
                        "exercise_therapy": ["适量运动如散步", "练习太极拳"]
                    },
                    "recommended_products": [
                        {
                            "product_id": "27f57d6f864b404eb10e592dafda2ca2",
                            "name": "提神茶",
                            "reason": f"适合气虚体质，能够改善{primary_symptom}症状",
                            "usage": "每日上午饮用一次，温水冲泡",
                            "priority": 1
                        }
                    ]
                }
                
                # 将JSON报告添加到回复中
                ai_response += json.dumps(mock_report_data, ensure_ascii=False, indent=2)
                ai_response += "\nEND_JSON_REPORT"
                
                parsed_json_report = mock_report_data
                is_task_completed = True
                logger.info("已强制设置报告生成标志")
        
        # 模拟字符级流式传输 - 使用异步延迟
        import random
        for i, char in enumerate(ai_response):
            chunk_event = {"type": "ai_chunk", "data": {"content": char}}
            yield f"data: {json.dumps(chunk_event, ensure_ascii=False)}\n\n"
            ai_response_full_text += char
            
            # 使用异步sleep避免阻塞
            if char in '，。！？；：':
                await asyncio.sleep(random.uniform(0.08, 0.15))
            elif char == ' ':
                await asyncio.sleep(random.uniform(0.02, 0.04))
            else:
                await asyncio.sleep(random.uniform(0.01, 0.03))  # 字符间随机延迟
    else:
        # 调用实际的AI API
        logger.info(f"调用真实AI ({MODEL_ID}) for conversation {conversation_id}...")
        
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {SILICONFLOW_API_KEY}"}
        payload = {
            "model": MODEL_ID, 
            "messages": messages_for_api, 
            "stream": True, 
            "max_tokens": 2048, 
            "temperature": 0.7
        }
        
        try:
            # 使用同步请求与流式处理，确保立即流式输出
            with requests.post(CHAT_ENDPOINT, json=payload, headers=headers, stream=True, timeout=60) as response:
                if response.status_code != 200:
                    error_text = response.text[:200] if response.text else "(No error body)"
                    error_message = f"AI API error: {response.status_code}, {error_text}"
                    logger.error(error_message)
                    error_event = {"type": "error", "data": {"message": error_message}}
                    yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
                else:
                    current_json_str = ""
                    in_json_block = False
                    
                    for line in response.iter_lines():
                        if line:
                            line_text = line.decode('utf-8')
                            if line_text.startswith('data: '):
                                data_text = line_text[6:]
                                if data_text == "[DONE]":
                                    break
                                    
                                try:
                                    chunk_data = json.loads(data_text)
                                    delta = chunk_data.get('choices', [{}])[0].get('delta', {})
                                    content = delta.get('content', '')
                                    
                                    if content:
                                        # 立即输出内容，避免缓冲
                                        # 处理可能的JSON报告块
                                        if "START_JSON_REPORT" in content:
                                            parts = content.split("START_JSON_REPORT", 1)
                                            before_json = parts[0]
                                            if before_json:
                                                # 将before_json也拆分为单个字符
                                                for char in before_json:
                                                    ai_response_full_text += char
                                                    chunk_event = {"type": "ai_chunk", "data": {"content": char}}
                                                    yield f"data: {json.dumps(chunk_event, ensure_ascii=False)}\n\n"
                                                    import random
                                                    await asyncio.sleep(random.uniform(0.01, 0.03))
                                            in_json_block = True
                                            current_json_str = parts[1]
                                        elif "END_JSON_REPORT" in content and in_json_block:
                                            parts = content.split("END_JSON_REPORT", 1)
                                            current_json_str += parts[0]
                                            after_json = parts[1]
                                            in_json_block = False
                                            is_task_completed = True
                                            
                                            try:
                                                parsed_json_report = json.loads(current_json_str.strip())
                                                # 这里可以添加报告处理逻辑
                                            except json.JSONDecodeError as je:
                                                logger.error(f"解析JSON报告失败: {je}")
                                            
                                            if after_json:
                                                # 将after_json也拆分为单个字符
                                                for char in after_json:
                                                    ai_response_full_text += char
                                                    chunk_event = {"type": "ai_chunk", "data": {"content": char}}
                                                    yield f"data: {json.dumps(chunk_event, ensure_ascii=False)}\n\n"
                                                    import random
                                                    await asyncio.sleep(random.uniform(0.01, 0.03))
                                        elif in_json_block:
                                            current_json_str += content
                                        else:
                                            # 将API返回的content拆分为单个字符进行流式传输
                                            for char in content:
                                                ai_response_full_text += char
                                                chunk_event = {"type": "ai_chunk", "data": {"content": char}}
                                                yield f"data: {json.dumps(chunk_event, ensure_ascii=False)}\n\n"
                                                
                                                # 添加字符间延迟以实现打字机效果 - 使用异步sleep
                                                import random
                                                if char in '，。！？；：':
                                                    await asyncio.sleep(random.uniform(0.08, 0.15))
                                                elif char == ' ':
                                                    await asyncio.sleep(random.uniform(0.02, 0.04))
                                                else:
                                                    await asyncio.sleep(random.uniform(0.01, 0.03))
                                except Exception as e:
                                    logger.error(f"处理AI流响应时出错: {e}")
        except requests.exceptions.RequestException as e:
            logger.error(f"调用AI API请求错误: {e}")
            error_event = {"type": "error", "data": {"message": f"AI服务连接错误: {str(e)}"}}
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
        except Exception as e:
            logger.error(f"处理AI响应流时发生未知错误: {e}")
            error_event = {"type": "error", "data": {"message": f"处理AI响应时出错: {str(e)}"}}
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"

    # 保存完整AI回复到数据库
    if ai_response_full_text:
        logger.info(f"生成AI回复完成，内容长度: {len(ai_response_full_text)}")
        ai_message = crud_chat.create_chat_message(
            db,
            user_id=user_id,
            conversation_id=conversation_id,
            message=ai_response_full_text,
            is_user=False
        )
        logger.info(f"AI回复已保存到数据库: id={ai_message.id}")
        
        # 发送完成消息通知前端
        ai_complete_message = {
            "id": ai_message.id,
            "conversation_id": conversation_id,
            "user_id": user_id,
            "message": ai_response_full_text,
            "is_user": False,
            "timestamp": int(ai_message.timestamp.timestamp() * 1000) if ai_message.timestamp else None
        }
        ai_complete_event = {"type": "ai_complete", "data": ai_complete_message}
        yield f"data: {json.dumps(ai_complete_event, ensure_ascii=False)}\n\n"
    
    # 如果任务完成且有报告数据，保存并发送报告
    if is_task_completed and parsed_json_report:
        try:
            logger.info(f"保存{task_type}报告到数据库...")
            if task_type == "chief_complaint":
                # 获取对话的基础体质报告ID（如果有的话）
                # 这里需要从对话元数据中获取，暂时设为None
                base_constitution_report_id = None
                try:
                    # 尝试从数据库获取对话元数据
                    conversation = crud_chat.get_conversation_by_id(db, conversation_id)
                    if conversation and hasattr(conversation, 'conversation_metadata') and conversation.conversation_metadata:
                        metadata = conversation.conversation_metadata
                        if isinstance(metadata, dict):
                            base_constitution_report_id = metadata.get('base_constitution_report_id')
                        elif isinstance(metadata, str):
                            metadata_dict = json.loads(metadata)
                            base_constitution_report_id = metadata_dict.get('base_constitution_report_id')
                except Exception as e:
                    logger.warning(f"无法获取对话元数据: {e}")
                
                # 保存主诉报告
                saved_report = crud_report.create_complaint_report(
                    db=db, 
                    user_id=user_id, 
                    report_data=parsed_json_report,
                    title=f"综合主诉评估报告 - {datetime.now().strftime('%Y-%m-%d')}",
                    base_constitution_report_id=base_constitution_report_id
                )
                logger.info(f"主诉报告已保存: ID={saved_report.id}")
            # 体质评估报告现在由前端直接生成，不再通过AI对话创建
            else:
                saved_report = None
                logger.warning(f"未知的任务类型: {task_type}")
            
            # 发送任务完成事件
            if saved_report:
                task_complete_event = {
                    "type": "task_complete",
                    "data": {
                        "report_type": task_type,
                        "report_id": saved_report.id,
                        "report_data": parsed_json_report
                    }
                }
                yield f"data: {json.dumps(task_complete_event, ensure_ascii=False)}\n\n"
                logger.info(f"任务完成事件已发送: report_id={saved_report.id}")
        except Exception as e:
            logger.error(f"保存报告时出错: {e}")
            error_event = {
                "type": "error", 
                "data": {"message": f"保存报告失败: {str(e)}"}
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
    
    # 发送流结束信号
    logger.info(f"聊天流响应结束: 用户={user_id}, 对话={conversation_id}")
    
    # 1. 发送与Flask兼容的标准格式
    stream_end_event_data = {"type": "stream_end", "data": {"message": "Stream ended"}}
    yield f"data: {json.dumps(stream_end_event_data, ensure_ascii=False)}\n\n"
    
    # 2. 发送SSE标准事件格式
    stream_end_event_sse = {"message": "Stream ended"}
    yield f"event: stream_end\ndata: {json.dumps(stream_end_event_sse, ensure_ascii=False)}\n\n"
    
    # 3. 发送FastAPI前端期望的 [DONE] 标识
    yield f"data: [DONE]\n\n"
"""
增强的茶品服务模块
整合k_api的茶品管理逻辑到主后端项目
"""

import logging
from typing import Dict, Any, List, Optional
from sqlalchemy import func, desc
from sqlalchemy.orm import Session
from fastapi import HTTPException

from fastapi_app.models.goods import Goods
from fastapi_app.models.user import User
from fastapi_app.crud import goods as crud_goods

logger = logging.getLogger(__name__)

class TeaProductService:
    """
    增强的茶品服务类
    整合k_api的茶品管理逻辑
    """
    
    @staticmethod
    def get_all_products(
        db: Session,
        page: int = 1,
        page_size: int = 20,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取所有茶产品列表，支持分页
        
        Args:
            db: 数据库会话
            page: 页码，默认为1
            page_size: 每页数量，默认为20
            user_id: 用户ID，如果提供则获取个性化推荐
            
        Returns:
            包含产品列表、分类和分页信息的字典
        """
        try:
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 获取产品总数 (status=0表示上架)
            total = db.query(func.count(Goods.id)).filter(
                Goods.status == 0
            ).scalar() or 0

            # 查询产品，按创建时间降序排序
            products = db.query(Goods).filter(
                Goods.status == 0
            ).order_by(
                desc(Goods.create_time)
            ).offset(offset).limit(page_size).all()
            
            # 判断用户是否已授权
            user_is_authorized = False
            has_body_data = False
            
            if user_id:
                user = db.query(User).filter(User.id == user_id).first()
                if user:
                    user_is_authorized = getattr(user, 'wechat_authorized', False)
                    # 检查用户是否有体质数据（可以通过查询体质报告表来判断）
                    has_body_data = False  # 暂时默认为False，后续可以根据实际情况调整
            
            # 格式化产品数据
            product_list = []
            for product in products:
                product_data = {
                    "id": product.id,
                    "name": product.name,
                    "series": getattr(product, 'series', '经典系列'),  # 如果没有系列字段，使用默认值
                    "intro": getattr(product, 'description', product.name),
                    "price": float(product.price),
                    "image": getattr(product, 'image', '') or "",
                    "effect": getattr(product, 'effect', '养生保健'),
                    "stock": getattr(product, 'stock', 100),  # 默认库存
                    "is_seasonal": getattr(product, 'is_seasonal', False),
                    "is_help_sleep": getattr(product, 'is_help_sleep', False),
                    "is_energizing": getattr(product, 'is_energizing', False),
                    "is_weight_management": getattr(product, 'is_weight_management', False),
                    "is_skin_care": getattr(product, 'is_skin_care', False),
                    "is_digestive": getattr(product, 'is_digestive', False)
                }
                product_list.append(product_data)
            
            # 构建分类数据（基于商品的功效特性）
            category_list = [
                {
                    "id": "all",
                    "name": "全部",
                    "property": "all",
                    "order": 1
                },
                {
                    "id": "seasonal",
                    "name": "时令推荐",
                    "property": "is_seasonal",
                    "order": 2
                },
                {
                    "id": "sleep",
                    "name": "助眠安神",
                    "property": "is_help_sleep",
                    "order": 3
                },
                {
                    "id": "energy",
                    "name": "提神醒脑",
                    "property": "is_energizing",
                    "order": 4
                },
                {
                    "id": "weight",
                    "name": "体重管理",
                    "property": "is_weight_management",
                    "order": 5
                },
                {
                    "id": "beauty",
                    "name": "美容养颜",
                    "property": "is_skin_care",
                    "order": 6
                },
                {
                    "id": "digest",
                    "name": "消化健胃",
                    "property": "is_digestive",
                    "order": 7
                }
            ]
            
            # 构造返回结果
            result = {
                "products": product_list,
                "categories": category_list,
                "pagination": {
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size
                },
                "user_status": {
                    "is_authorized": user_is_authorized,
                    "has_body_data": has_body_data
                }
            }
            
            logger.info(f"成功获取产品列表，共{len(product_list)}条记录，第{page}页，每页{page_size}条")
            return result
            
        except Exception as e:
            logger.error(f"获取产品列表时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取产品列表失败: {str(e)}")
    
    @staticmethod
    def get_product_by_id(
        db: Session,
        product_id: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        根据ID获取产品详情
        
        Args:
            db: 数据库会话
            product_id: 产品ID
            user_id: 用户ID，可选
            
        Returns:
            产品详情字典
        """
        try:
            # 查询产品
            product = db.query(Goods).filter(
                Goods.id == product_id,
                Goods.status == 0
            ).first()
            
            if not product:
                raise HTTPException(status_code=404, detail=f"产品不存在: {product_id}")
            
            # 格式化产品详情
            product_detail = {
                "id": product.id,
                "name": product.name,
                "series": getattr(product, 'series', '经典系列'),
                "intro": getattr(product, 'description', product.name),
                "price": float(product.price),
                "original_price": float(product.original_price) if product.original_price else None,
                "image": product.image_url or "",
                "images": [product.image_url] if product.image_url else [],
                "effect": getattr(product, 'effect', '养生保健'),
                "stock": product.stock,
                "is_seasonal": getattr(product, 'is_seasonal', False),
                "is_help_sleep": getattr(product, 'is_help_sleep', False),
                "is_energizing": getattr(product, 'is_energizing', False),
                "is_weight_management": getattr(product, 'is_weight_management', False),
                "is_skin_care": getattr(product, 'is_skin_care', False),
                "is_digestive": getattr(product, 'is_digestive', False),
                "ingredients": getattr(product, 'ingredients', ''),
                "brewing_method": getattr(product, 'brewing_method', ''),
                "storage_method": getattr(product, 'storage_method', ''),
                "created_at": product.created_at.isoformat() if product.created_at else None,
                "updated_at": product.updated_at.isoformat() if product.updated_at else None
            }
            
            logger.info(f"成功获取产品详情: {product_id}")
            return product_detail
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取产品详情时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取产品详情失败: {str(e)}")
    
    @staticmethod
    def get_products_by_category(
        db: Session,
        category: str,
        page: int = 1,
        page_size: int = 20,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        根据分类获取产品列表
        
        Args:
            db: 数据库会话
            category: 分类属性名
            page: 页码
            page_size: 每页数量
            user_id: 用户ID，可选
            
        Returns:
            分类产品列表
        """
        try:
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 构建查询条件
            query = db.query(Goods).filter(Goods.status == 0)
            
            # 根据分类过滤
            if category != "all":
                if hasattr(Goods, category):
                    query = query.filter(getattr(Goods, category) == True)
                else:
                    # 如果分类属性不存在，返回空结果
                    return {
                        "products": [],
                        "pagination": {
                            "total": 0,
                            "page": page,
                            "page_size": page_size,
                            "total_pages": 0
                        }
                    }
            
            # 获取总数
            total = query.count()
            
            # 获取产品列表
            products = query.order_by(desc(Goods.create_time)).offset(offset).limit(page_size).all()
            
            # 格式化产品数据
            product_list = []
            for product in products:
                product_data = {
                    "id": product.id,
                    "name": product.name,
                    "series": getattr(product, 'series', '经典系列'),
                    "intro": getattr(product, 'description', product.name),
                    "price": float(product.price),
                    "image": product.image_url or "",
                    "effect": getattr(product, 'effect', '养生保健'),
                    "stock": product.stock
                }
                product_list.append(product_data)
            
            result = {
                "products": product_list,
                "pagination": {
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size
                }
            }
            
            logger.info(f"成功获取分类产品列表: {category}，共{len(product_list)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"获取分类产品列表时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取分类产品列表失败: {str(e)}")

# 创建全局实例
tea_product_service = TeaProductService()

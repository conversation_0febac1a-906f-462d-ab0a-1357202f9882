/**
 * Admin商品管理JavaScript
 */

// 提交添加商品表单
function submitAddGoodsForm() {
    console.log('开始提交商品添加表单...');
    
    // 获取表单数据
    const name = document.getElementById('goodsName').value.trim();
    const description = document.getElementById('goodsDescription').value.trim();
    const price = parseFloat(document.getElementById('goodsPrice').value);
    const category = document.getElementById('goodsCategory').value.trim();
    
    // 验证必填字段
    if (!name) {
        showError('addGoodsError', '商品名称不能为空');
        return;
    }
    
    if (!price || price <= 0) {
        showError('addGoodsError', '请输入有效的商品价格');
        return;
    }
    
    // 清除之前的错误消息
    hideError('addGoodsError');
    
    // 构建请求数据
    const goodsData = {
        name: name,
        description: description,
        price: price,
        category_name: category || '默认分类',
        status: 0  // 0表示上架
    };
    
    console.log('提交的商品数据:', goodsData);
    
    // 发送API请求
    fetch('/api/v1/admin/goods', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': getAuthToken()
        },
        body: JSON.stringify(goodsData)
    })
    .then(response => {
        console.log('API响应状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('API响应数据:', data);
        if (data.code === 0) {
            // 成功
            console.log('商品添加成功:', data.data);
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('addGoodsModal'));
            if (modal) {
                modal.hide();
            }
            
            // 重置表单
            document.getElementById('addGoodsForm').reset();
            
            // 显示成功消息
            showToast('商品添加成功！', 'success');
            
            // 刷新商品列表
            if (typeof loadGoods === 'function') {
                loadGoods();
            }
        } else {
            // 失败
            showError('addGoodsError', data.message || '添加商品失败');
        }
    })
    .catch(error => {
        console.error('添加商品请求失败:', error);
        showError('addGoodsError', '网络错误，请稍后重试');
    });
}

// 获取认证token
function getAuthToken() {
    const token = localStorage.getItem('admin_token') || localStorage.getItem('token');
    return token ? `Bearer ${token}` : '';
}

// 显示错误消息
function showError(elementId, message) {
    const errorEl = document.getElementById(elementId);
    if (errorEl) {
        errorEl.textContent = message;
        errorEl.classList.remove('d-none');
    }
}

// 隐藏错误消息
function hideError(elementId) {
    const errorEl = document.getElementById(elementId);
    if (errorEl) {
        errorEl.classList.add('d-none');
    }
}

// 显示Toast消息
function showToast(message, type = 'info') {
    console.log(`显示Toast消息: ${message} (类型: ${type})`);
    
    // 使用Bootstrap的toast组件
    const toastEl = document.getElementById('liveToast');
    const toastMessage = document.getElementById('toastMessage');
    
    if (!toastEl || !toastMessage) {
        // 备用：使用alert
        console.warn('Toast组件不存在，使用alert代替');
        alert(message);
        return;
    }
    
    try {
        // 设置消息内容
        toastMessage.textContent = message;
        
        // 设置样式
        toastEl.classList.remove('bg-success', 'bg-danger', 'bg-warning', 'bg-info', 'text-white');
        switch (type) {
            case 'success':
                toastEl.classList.add('bg-success', 'text-white');
                break;
            case 'danger':
                toastEl.classList.add('bg-danger', 'text-white');
                break;
            case 'warning':
                toastEl.classList.add('bg-warning');
                break;
            default:
                toastEl.classList.add('bg-info', 'text-white');
        }
        
        // 显示toast
        if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
            const toast = new bootstrap.Toast(toastEl);
            toast.show();
        } else {
            console.warn('Bootstrap Toast组件不可用，使用替代显示');
            toastEl.style.display = 'block';
            setTimeout(() => {
                toastEl.style.display = 'none';
            }, 3000);
        }
    } catch (error) {
        console.error('显示Toast消息出错:', error);
        // 最终备用：alert
        try {
            alert(message);
        } catch (e) {
            console.error('显示alert出现问题:', e);
        }
    }
}
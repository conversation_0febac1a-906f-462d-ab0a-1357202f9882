import os
from datetime import timedelta
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn, model_validator
from typing import ClassVar, Optional

class Settings(BaseSettings):
    # Base
    SECRET_KEY: str = 'hard-to-guess-string'
    
    # Environment settings
    ENVIRONMENT: str = "dev"

    # JWT settings
    JWT_SECRET_KEY: str = 'jwt-secret-key'
    JWT_ACCESS_TOKEN_EXPIRES: timedelta = timedelta(hours=2)
    ALGORITHM: str = "HS256"

    # WeChat settings
    WECHAT_APP_ID: str = os.getenv("WECHAT_APP_ID", "")
    WECHAT_APP_SECRET: str = os.getenv("WECHAT_APP_SECRET", "")
    
    # Database settings - components
    POSTGRES_USER: str = "mastea"
    POSTGRES_PASSWORD: str = "wyh12257410"
    POSTGRES_SERVER: str = os.getenv("POSTGRES_HOST", "localhost")  # WSL2访问宿主机
    POSTGRES_PORT: int = 5432
    POSTGRES_DB: str = "mastea_dev"
    
    # SQLite fallback for development
    SQLITE_DATABASE_URL: str = "sqlite:///./test.db"
    
    # Database settings - constructed URIs (will be set by validator)
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    # MongoDB settings removed - migrated to PostgreSQL

    # Upload settings
    UPLOAD_FOLDER: str = os.path.join(os.path.abspath(os.path.dirname(__name__)), 'uploads')
    MAX_CONTENT_LENGTH: int = 16 * 1024 * 1024  # 16MB

    ALLOWED_EXTENSIONS: ClassVar[set] = {'png', 'jpg', 'jpeg', 'gif'}

    @model_validator(mode='after')
    def get_db_uris(self) -> 'Settings':
        # 优先使用DATABASE_URL环境变量（容器环境）
        database_url = os.getenv('DATABASE_URL')
        if database_url:
            self.SQLALCHEMY_DATABASE_URI = database_url
            return self

        # 默认使用PostgreSQL，除非明确设置USE_POSTGRES=false
        # 这样容器环境下默认就使用PostgreSQL
        use_postgres = os.getenv('USE_POSTGRES', 'true').lower() == 'true'

        if use_postgres:
            # Set DB name based on environment
            if self.ENVIRONMENT == "prod" or self.ENVIRONMENT == "production":
                postgres_db = "mastea_prod"
            elif self.ENVIRONMENT == "test":
                postgres_db = "mastea_test"
            else: # dev or default
                postgres_db = self.POSTGRES_DB  # mastea_dev

            # Construct PostgreSQL URI
            self.SQLALCHEMY_DATABASE_URI = f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{postgres_db}"
        else:
            # Use SQLite for special testing only
            self.SQLALCHEMY_DATABASE_URI = self.SQLITE_DATABASE_URL

        return self

    class Config:
        env_file = ".env"
        env_file_encoding = 'utf-8'
        extra = 'ignore'

# Create a single instance to be used throughout the application
settings = Settings() 
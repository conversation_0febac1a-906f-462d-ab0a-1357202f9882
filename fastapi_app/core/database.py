import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
from fastapi_app.core.config import settings

# 加载环境变量
load_dotenv()

# 使用配置中的数据库URL
DATABASE_URL = str(settings.SQLALCHEMY_DATABASE_URI)

# 创建数据库引擎
# 为SQLite添加特殊配置
if DATABASE_URL.startswith('sqlite'):
    engine = create_engine(
        DATABASE_URL, 
        echo=False,
        connect_args={"check_same_thread": False}  # SQLite需要这个参数
    )
else:
    engine = create_engine(DATABASE_URL, echo=False)

# 创建数据库会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建数据库模型的基类
Base = declarative_base()

# 获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 调试函数：获取表和列信息
def inspect_database():
    """检查数据库表结构"""
    from sqlalchemy import inspect
    
    inspector = inspect(engine)
    tables = inspector.get_table_names()
    
    print("数据库中的表:")
    for table in tables:
        print(f"- {table}")
        columns = inspector.get_columns(table)
        print("  列:")
        for column in columns:
            print(f"    - {column['name']}: {column['type']}")
    
    return tables 
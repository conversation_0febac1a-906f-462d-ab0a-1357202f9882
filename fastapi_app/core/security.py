from datetime import datetime, timedelta, timezone
from typing import Any, Union, Optional
from jose import jwt, JWTError
from fastapi import Depends, HTTPException, status, Query
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session

from fastapi_app.core.config import settings
from fastapi_app.models.user import User as UserModel
from fastapi_app.crud import user as crud_user
from fastapi_app.core.database import get_db, SessionLocal
from fastapi_app.schemas import TokenData
import logging

# 配置日志
logger = logging.getLogger(__name__)

oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"/api/v1/auth/login",
    auto_error=False  # 设置为 False，这样即使没有token也不会自动报错
)

def create_access_token(data: dict) -> str:
    expire = datetime.now(timezone.utc) + settings.JWT_ACCESS_TOKEN_EXPIRES
    to_encode = {"exp": expire, "sub": str(data["sub"])}
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm="HS256")
    return encoded_jwt

def create_refresh_token(data: dict) -> str:
    """
    创建刷新令牌

    Args:
        data: 包含用户信息的字典，必须包含 'sub' 字段

    Returns:
        编码后的JWT刷新令牌
    """
    # 刷新令牌有效期更长，通常为7天
    expire = datetime.now(timezone.utc) + timedelta(days=7)
    to_encode = {
        "exp": expire,
        "sub": str(data["sub"]),
        "type": "refresh"  # 标识这是一个刷新令牌
    }
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm="HS256")
    return encoded_jwt

async def get_current_user(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    if not token:
        logger.info("未提供认证token")
        return None # 如果没有提供token，返回None
    try:
        logger.debug(f"解码token: {token[:10]}...")
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.ALGORITHM])
        subject: str = payload.get("sub")
        if subject is None:
            logger.warning("Token无效: 缺少sub字段")
            return None
        token_data = TokenData(sub=subject)
        
        # 特殊处理admin用户ID
        if subject == "admin_user_id":
            logger.info("识别为admin用户token")
            # 检查admin用户是否已存在于数据库中
            admin_user = db.query(UserModel).filter(UserModel.id == "admin_user_id").first()
            if not admin_user:
                # 如果不存在，创建admin用户记录
                logger.info("创建admin用户记录")
                from sqlalchemy import text
                
                # 首先检查用户表是否已存在此ID
                result = db.execute(text("SELECT 1 FROM users WHERE id = 'admin_user_id'")).fetchone()
                if result:
                    # ID存在但对象不存在，这是一个不一致状态
                    logger.warning("数据库中已存在admin_user_id但对象不存在，尝试修复")
                    try:
                        # 尝试删除已存在的行，以便重新创建完整用户
                        db.execute(text("DELETE FROM users WHERE id = 'admin_user_id'"))
                        db.commit()
                        logger.info("已删除不完整的admin用户记录")
                    except Exception as e:
                        logger.error(f"尝试删除不完整admin用户记录失败: {str(e)}")
                        db.rollback()
                
                # 创建完整的admin用户
                admin_user = UserModel(
                    id="admin_user_id",
                    username="admin",
                    email="<EMAIL>",
                    mobile="admin",
                    password_hash="not_used_for_admin"  # 实际应用中应使用安全的哈希密码
                )
                db.add(admin_user)
                try:
                    db.commit()
                    db.refresh(admin_user)  # 确保能获取到所有字段
                    logger.info("Admin用户成功创建")
                except Exception as e:
                    db.rollback()
                    logger.error(f"创建Admin用户失败: {str(e)}")
                    # 再次检查admin用户
                    admin_user = db.query(UserModel).filter(UserModel.id == "admin_user_id").first()
            
            if admin_user:
                logger.info("Admin用户通过token认证成功")
                return admin_user
            else:
                # 如果所有尝试都失败，返回一个内存中的对象
                logger.warning("无法在数据库中创建或找到admin用户，使用内存对象")
                admin_user = UserModel(
                    id="admin_user_id",
                    username="admin",
                    email="<EMAIL>",
                    mobile="admin",
                    password_hash="not_used_for_admin"
                )
                return admin_user
    except JWTError as e:
        logger.warning(f"JWT token验证错误: {e}")
        return None
    
    user = db.query(UserModel).filter(UserModel.id == token_data.sub).first()
    if user is None:
        logger.warning(f"数据库中找不到ID为{token_data.sub}的用户")
        return None
    return user

async def get_user_by_id_or_token(
    user_id: Optional[str] = Query(None, description="User ID to act on behalf of (for testing/admin)."),
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(get_current_user)
) -> UserModel:
    if user_id == "admin_user_id":
        # 返回模拟的admin用户
        admin_user = UserModel(
            id="admin_user_id",
            username="admin",
            email="<EMAIL>",
            mobile="admin",
            password_hash="not_used_for_admin"
        )
        return admin_user
    elif user_id:
        user = db.query(UserModel).filter(UserModel.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User specified by user_id not found")
        return user
    if current_user:
        return current_user
    raise HTTPException(status_code=401, detail="Could not identify user from token or user_id")

def get_user(db: Session, username: str):
    if username is None:
        return None  # 改为返回 None 而不是抛出异常
    user = db.query(UserModel).filter(UserModel.username == username).first()
    if user is None:
        return None # 改为返回 None
    return user 
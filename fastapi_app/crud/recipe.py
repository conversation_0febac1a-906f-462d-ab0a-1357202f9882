from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional, Dict, Any
from fastapi_app.models.recipe import Recipe, HerbIngredient, RecipeIngredient
from fastapi_app.models.goods import Goods
import json
import logging

logger = logging.getLogger(__name__)

# Recipe CRUD operations
def create_recipe(db: Session, recipe_data: Dict[str, Any]) -> Recipe:
    """创建配方"""
    db_recipe = Recipe(
        name=recipe_data['name'],
        description=recipe_data.get('description'),
        intro=recipe_data.get('intro'),
        image=recipe_data.get('image'),
        grams=recipe_data.get('grams'),
        goods_id=recipe_data.get('goods_id'),
        is_active=recipe_data.get('is_active', True)
    )
    db.add(db_recipe)
    db.commit()
    db.refresh(db_recipe)
    return db_recipe

def get_recipe(db: Session, recipe_id: str) -> Optional[Recipe]:
    """根据ID获取配方"""
    return db.query(Recipe).filter(Recipe.id == recipe_id).first()

def get_recipes_by_goods_id(db: Session, goods_id: str) -> List[Recipe]:
    """根据商品ID获取配方列表"""
    return db.query(Recipe).filter(
        and_(Recipe.goods_id == goods_id, Recipe.is_active == True)
    ).all()

def get_recipes(db: Session, skip: int = 0, limit: int = 100, 
                name: Optional[str] = None, is_active: Optional[bool] = None) -> List[Recipe]:
    """获取配方列表"""
    query = db.query(Recipe)
    
    if name:
        query = query.filter(Recipe.name.ilike(f"%{name}%"))
    if is_active is not None:
        query = query.filter(Recipe.is_active == is_active)
    
    return query.offset(skip).limit(limit).all()

def update_recipe(db: Session, recipe_id: str, recipe_data: Dict[str, Any]) -> Optional[Recipe]:
    """更新配方"""
    db_recipe = get_recipe(db, recipe_id)
    if not db_recipe:
        return None
    
    for key, value in recipe_data.items():
        if hasattr(db_recipe, key):
            setattr(db_recipe, key, value)
    
    db.commit()
    db.refresh(db_recipe)
    return db_recipe

def delete_recipe(db: Session, recipe_id: str) -> bool:
    """删除配方"""
    db_recipe = get_recipe(db, recipe_id)
    if not db_recipe:
        return False
    
    db.delete(db_recipe)
    db.commit()
    return True

# HerbIngredient CRUD operations
def create_herb_ingredient(db: Session, herb_data: Dict[str, Any]) -> HerbIngredient:
    """创建草药成分"""
    db_herb = HerbIngredient(
        name=herb_data['name'],
        description=herb_data.get('description'),
        intro=herb_data.get('intro'),
        image=herb_data.get('image'),
        flavor=herb_data.get('flavor'),
        nature=herb_data.get('nature'),
        is_active=herb_data.get('is_active', True)
    )
    db.add(db_herb)
    db.commit()
    db.refresh(db_herb)
    return db_herb

def get_herb_ingredient(db: Session, herb_id: str) -> Optional[HerbIngredient]:
    """根据ID获取草药成分"""
    return db.query(HerbIngredient).filter(HerbIngredient.id == herb_id).first()

def get_herb_ingredients(db: Session, skip: int = 0, limit: int = 100,
                        name: Optional[str] = None, is_active: Optional[bool] = None) -> List[HerbIngredient]:
    """获取草药成分列表"""
    query = db.query(HerbIngredient)
    
    if name:
        query = query.filter(HerbIngredient.name.ilike(f"%{name}%"))
    if is_active is not None:
        query = query.filter(HerbIngredient.is_active == is_active)
    
    return query.offset(skip).limit(limit).all()

def update_herb_ingredient(db: Session, herb_id: str, herb_data: Dict[str, Any]) -> Optional[HerbIngredient]:
    """更新草药成分"""
    db_herb = get_herb_ingredient(db, herb_id)
    if not db_herb:
        return None
    
    for key, value in herb_data.items():
        if hasattr(db_herb, key):
            setattr(db_herb, key, value)
    
    db.commit()
    db.refresh(db_herb)
    return db_herb

def delete_herb_ingredient(db: Session, herb_id: str) -> bool:
    """删除草药成分"""
    db_herb = get_herb_ingredient(db, herb_id)
    if not db_herb:
        return False
    
    db.delete(db_herb)
    db.commit()
    return True

# RecipeIngredient CRUD operations
def add_ingredient_to_recipe(db: Session, recipe_id: str, herb_id: str, 
                           amount: Optional[float] = None, unit: Optional[str] = None) -> RecipeIngredient:
    """为配方添加草药成分"""
    db_recipe_ingredient = RecipeIngredient(
        recipe_id=recipe_id,
        herb_ingredient_id=herb_id,
        amount=amount,
        unit=unit
    )
    db.add(db_recipe_ingredient)
    db.commit()
    db.refresh(db_recipe_ingredient)
    return db_recipe_ingredient

def remove_ingredient_from_recipe(db: Session, recipe_id: str, herb_id: str) -> bool:
    """从配方中移除草药成分"""
    db_recipe_ingredient = db.query(RecipeIngredient).filter(
        and_(RecipeIngredient.recipe_id == recipe_id, 
             RecipeIngredient.herb_ingredient_id == herb_id)
    ).first()
    
    if not db_recipe_ingredient:
        return False
    
    db.delete(db_recipe_ingredient)
    db.commit()
    return True

def get_recipe_ingredients(db: Session, recipe_id: str) -> List[RecipeIngredient]:
    """获取配方的所有草药成分"""
    return db.query(RecipeIngredient).filter(RecipeIngredient.recipe_id == recipe_id).all()

def update_recipe_ingredient(db: Session, recipe_ingredient_id: str, 
                           amount: Optional[float] = None, unit: Optional[str] = None) -> Optional[RecipeIngredient]:
    """更新配方中草药成分的用量"""
    db_recipe_ingredient = db.query(RecipeIngredient).filter(
        RecipeIngredient.id == recipe_ingredient_id
    ).first()
    
    if not db_recipe_ingredient:
        return None
    
    if amount is not None:
        db_recipe_ingredient.amount = amount
    if unit is not None:
        db_recipe_ingredient.unit = unit
    
    db.commit()
    db.refresh(db_recipe_ingredient)
    return db_recipe_ingredient

# 茶品推荐相关功能
def get_recommended_teas_by_constitution(db: Session, constitution_type: str, 
                                       limit: int = 10) -> List[Goods]:
    """根据体质类型获取推荐茶品"""
    # 这里可以根据体质类型和茶品的功效进行匹配
    # 暂时返回基础推荐类型的茶品
    return db.query(Goods).filter(
        and_(Goods.tea_type == "basicRecommend", Goods.status == 0)
    ).limit(limit).all()

def get_seasonal_recommended_teas(db: Session, limit: int = 10) -> List[Goods]:
    """获取时令推荐茶品"""
    return db.query(Goods).filter(
        and_(Goods.seasonal_recommend == True, Goods.status == 0)
    ).limit(limit).all()

def get_self_select_teas(db: Session, category: Optional[str] = None, 
                        limit: int = 50) -> List[Goods]:
    """获取自选茶包"""
    query = db.query(Goods).filter(
        and_(Goods.self_select_enabled == True, Goods.status == 0)
    )
    
    if category and category != "全部":
        # 在self_select_categories JSON字段中搜索
        query = query.filter(Goods.self_select_categories.ilike(f"%{category}%"))
    
    return query.limit(limit).all()

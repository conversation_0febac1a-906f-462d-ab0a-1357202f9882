from sqlalchemy.orm import Session, joinedload
from fastapi_app.models import goods as models

def get_goods_list(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Goods).filter(models.Goods.status == 0).offset(skip).limit(limit).all()

def get_all_goods(db: Session):
    """获取所有商品数据（用于AI推荐）"""
    return db.query(models.Goods).filter(models.Goods.status == 0).all()

def get_goods_detail(db: Session, goods_id: str):
    return db.query(models.Goods).options(
        joinedload(models.Goods.goods_images),
        joinedload(models.Goods.goods_skus)
    ).filter(models.Goods.id == goods_id, models.Goods.status == 0).first() 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麦斯特养生茶 - 用户中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        .header .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .welcome-text {
            color: #666;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid #3498db;
            color: #3498db;
        }
        
        .btn-success { background: linear-gradient(45deg, #27ae60, #2ecc71); }
        .btn-warning { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .btn-danger { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
        }
        
        .sidebar {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 25px;
            height: fit-content;
        }
        
        .main-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 25px;
        }
        
        .section-title {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        
        .menu-list {
            list-style: none;
        }
        
        .menu-item {
            margin-bottom: 10px;
        }
        
        .menu-link {
            display: block;
            padding: 12px 15px;
            background: #f8f9fa;
            color: #333;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .menu-link:hover, .menu-link.active {
            background: #3498db;
            color: white;
        }
        
        .content-section {
            display: none;
        }
        
        .content-section.active {
            display: block;
        }
        
        .info-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
        }
        
        .card-title {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .result-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .header .container {
                flex-direction: column;
                gap: 15px;
            }
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">🍃 麦斯特养生茶</div>
            <div class="user-info">
                <span class="welcome-text">欢迎，<span id="username">访客</span></span>
                <button class="btn btn-outline" onclick="logout()">退出登录</button>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- 登录表单 -->
        <div id="login-section" class="main-content" style="max-width: 400px; margin: 50px auto;">
            <h2 class="section-title">用户登录</h2>
            <div class="error" id="login-error"></div>
            <div class="success" id="login-success"></div>
            
            <div class="form-group">
                <label class="form-label">用户名/邮箱/手机号:</label>
                <input type="text" class="form-input" id="login-username" value="testuser123">
            </div>
            <div class="form-group">
                <label class="form-label">密码:</label>
                <input type="password" class="form-input" id="login-password" value="123456">
            </div>
            <div class="form-group">
                <button class="btn btn-success" onclick="performLogin()" style="width: 100%;">登录</button>
            </div>
            <div class="form-group" style="text-align: center;">
                <span>没有账号？</span>
                <a href="#" onclick="showRegister()">立即注册</a>
            </div>
            
            <!-- 注册表单 -->
            <div id="register-form" style="display: none;">
                <h3 style="margin: 20px 0;">用户注册</h3>
                <div class="form-group">
                    <label class="form-label">用户名:</label>
                    <input type="text" class="form-input" id="reg-username" value="">
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱:</label>
                    <input type="email" class="form-input" id="reg-email" value="">
                </div>
                <div class="form-group">
                    <label class="form-label">手机号:</label>
                    <input type="text" class="form-input" id="reg-mobile" value="">
                </div>
                <div class="form-group">
                    <label class="form-label">密码:</label>
                    <input type="password" class="form-input" id="reg-password" value="123456">
                </div>
                <div class="form-group">
                    <button class="btn btn-success" onclick="performRegister()" style="width: 100%;">注册</button>
                </div>
                <div class="form-group" style="text-align: center;">
                    <a href="#" onclick="showLogin()">返回登录</a>
                </div>
            </div>
        </div>

        <!-- 主面板 -->
        <div id="dashboard-section" class="dashboard" style="display: none;">
            <div class="sidebar">
                <h3 class="section-title">功能导航</h3>
                <ul class="menu-list">
                    <li class="menu-item">
                        <a href="#" class="menu-link active" onclick="showSection('overview')">
                            📊 概览
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('reports')">
                            📋 体质报告
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('chat')">
                            💬 AI咨询
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('products')">
                            🛍️ 商品浏览
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('cart')">
                            🛒 购物车
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('addresses')">
                            📍 收货地址
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('orders')">
                            📦 我的订单
                        </a>
                    </li>
                    <li class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('profile')">
                            👤 个人信息
                        </a>
                    </li>
                </ul>
            </div>

            <div class="main-content">
                <!-- 概览页面 -->
                <div id="overview-section" class="content-section active">
                    <h2 class="section-title">用户概览</h2>
                    <div class="loading" id="overview-loading">加载中...</div>
                    <div class="error" id="overview-error"></div>
                    
                    <div class="info-card">
                        <h3 class="card-title">个人信息</h3>
                        <div class="info-row">
                            <span class="info-label">用户ID:</span>
                            <span class="info-value" id="user-id">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">用户名:</span>
                            <span class="info-value" id="user-name">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">邮箱:</span>
                            <span class="info-value" id="user-email">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">手机号:</span>
                            <span class="info-value" id="user-mobile">-</span>
                        </div>
                    </div>

                    <div class="card-grid">
                        <div class="card">
                            <div class="card-title">📋 我的报告</div>
                            <p>体质报告: <span id="constitution-count">0</span> 份</p>
                            <p>主诉报告: <span id="complaint-count">0</span> 份</p>
                            <button class="btn" onclick="showSection('reports')">查看报告</button>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">💬 AI咨询</div>
                            <p>对话数量: <span id="conversation-count">0</span> 个</p>
                            <button class="btn" onclick="showSection('chat')">开始咨询</button>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">🛒 购物车</div>
                            <p>商品数量: <span id="cart-count">0</span> 件</p>
                            <button class="btn" onclick="showSection('cart')">查看购物车</button>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">📦 订单</div>
                            <p>订单数量: <span id="order-count">0</span> 个</p>
                            <button class="btn" onclick="showSection('orders')">查看订单</button>
                        </div>
                    </div>
                </div>

                <!-- 体质报告页面 -->
                <div id="reports-section" class="content-section">
                    <h2 class="section-title">体质报告管理</h2>
                    <div class="loading" id="reports-loading">加载中...</div>
                    <div class="error" id="reports-error"></div>
                    
                    <button class="btn btn-success" onclick="createConstitutionReport()">📝 生成新的体质报告</button>
                    <button class="btn btn-warning" onclick="startComplaintAssessment()">🩺 开始主诉评估</button>
                    
                    <div class="card-grid" style="margin-top: 20px;">
                        <div class="card">
                            <div class="card-title">📊 体质报告列表</div>
                            <div id="constitution-reports">
                                <p>暂无体质报告</p>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">🩺 主诉报告列表</div>
                            <div id="complaint-reports">
                                <p>暂无主诉报告</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI咨询页面 -->
                <div id="chat-section" class="content-section">
                    <h2 class="section-title">AI健康咨询</h2>
                    <div class="loading" id="chat-loading">加载中...</div>
                    <div class="error" id="chat-error"></div>
                    
                    <button class="btn btn-success" onclick="createNewConversation()">💬 开始新对话</button>
                    
                    <div style="margin-top: 20px;">
                        <h3>对话列表</h3>
                        <div id="conversation-list">
                            <p>暂无对话</p>
                        </div>
                    </div>
                </div>

                <!-- 商品浏览页面 -->
                <div id="products-section" class="content-section">
                    <h2 class="section-title">商品浏览</h2>
                    <div class="loading" id="products-loading">加载中...</div>
                    <div class="error" id="products-error"></div>
                    
                    <div class="form-group">
                        <label class="form-label">搜索商品:</label>
                        <input type="text" class="form-input" id="product-search" placeholder="输入商品名称...">
                        <button class="btn" onclick="searchProducts()">🔍 搜索</button>
                    </div>
                    
                    <div id="products-grid" class="card-grid">
                        <p>暂无商品数据</p>
                    </div>
                </div>

                <!-- 购物车页面 -->
                <div id="cart-section" class="content-section">
                    <h2 class="section-title">我的购物车</h2>
                    <div class="loading" id="cart-loading">加载中...</div>
                    <div class="error" id="cart-error"></div>
                    
                    <div id="cart-items">
                        <p>购物车为空</p>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <button class="btn btn-success" onclick="checkout()">💰 结算</button>
                        <button class="btn btn-warning" onclick="clearCart()">🗑️ 清空购物车</button>
                    </div>
                </div>

                <!-- 收货地址页面 -->
                <div id="addresses-section" class="content-section">
                    <h2 class="section-title">收货地址管理</h2>
                    <div class="loading" id="addresses-loading">加载中...</div>
                    <div class="error" id="addresses-error"></div>
                    
                    <button class="btn btn-success" onclick="showAddAddressForm()">📍 添加新地址</button>
                    
                    <!-- 添加地址表单 -->
                    <div id="add-address-form" style="display: none; margin-top: 20px;">
                        <h3>添加收货地址</h3>
                        <div class="form-group">
                            <label class="form-label">收货人姓名:</label>
                            <input type="text" class="form-input" id="address-name" value="张三">
                        </div>
                        <div class="form-group">
                            <label class="form-label">联系电话:</label>
                            <input type="text" class="form-input" id="address-phone" value="13800138000">
                        </div>
                        <div class="form-group">
                            <label class="form-label">详细地址:</label>
                            <textarea class="form-textarea" id="address-detail">北京市朝阳区某某街道某某小区</textarea>
                        </div>
                        <button class="btn btn-success" onclick="addAddress()">保存地址</button>
                        <button class="btn btn-outline" onclick="hideAddAddressForm()">取消</button>
                    </div>
                    
                    <div id="addresses-list" style="margin-top: 20px;">
                        <p>暂无收货地址</p>
                    </div>
                </div>

                <!-- 订单页面 -->
                <div id="orders-section" class="content-section">
                    <h2 class="section-title">我的订单</h2>
                    <div class="loading" id="orders-loading">加载中...</div>
                    <div class="error" id="orders-error"></div>
                    
                    <div id="orders-list">
                        <p>暂无订单</p>
                    </div>
                </div>

                <!-- 个人信息页面 -->
                <div id="profile-section" class="content-section">
                    <h2 class="section-title">个人信息</h2>
                    <div class="loading" id="profile-loading">加载中...</div>
                    <div class="error" id="profile-error"></div>
                    
                    <div class="form-group">
                        <label class="form-label">用户名:</label>
                        <input type="text" class="form-input" id="profile-username" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">邮箱:</label>
                        <input type="email" class="form-input" id="profile-email" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">手机号:</label>
                        <input type="text" class="form-input" id="profile-mobile" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">性别:</label>
                        <select class="form-select" id="profile-gender">
                            <option value="">请选择</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">年龄:</label>
                        <input type="number" class="form-input" id="profile-age">
                    </div>
                    
                    <button class="btn btn-success" onclick="updateProfile()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentUser = null;
        let authToken = null;
        let currentBaseReportId = null; // 存储当前的体质报告ID，用于主诉评估

        // 工具函数
        function getAuthHeaders() {
            const headers = { 'Content-Type': 'application/json' };
            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }
            return headers;
        }

        function showError(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.style.display = 'block';
                setTimeout(() => element.style.display = 'none', 5000);
            }
        }

        function showSuccess(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.style.display = 'block';
                setTimeout(() => element.style.display = 'none', 3000);
            }
        }

        function showLoading(elementId, show = true) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = show ? 'block' : 'none';
            }
        }

        // 登录注册相关
        function showRegister() {
            document.getElementById('register-form').style.display = 'block';
            // 自动生成测试数据
            const timestamp = Date.now().toString(36);
            document.getElementById('reg-username').value = `testuser_${timestamp}`;
            document.getElementById('reg-email').value = `test_${timestamp}@example.com`;
            document.getElementById('reg-mobile').value = `138${timestamp.substr(-8)}`;
        }

        function showLogin() {
            document.getElementById('register-form').style.display = 'none';
        }

        async function performRegister() {
            const userData = {
                username: document.getElementById('reg-username').value,
                email: document.getElementById('reg-email').value,
                mobile: document.getElementById('reg-mobile').value,
                password: document.getElementById('reg-password').value
            };

            try {
                const response = await fetch('/api/v1/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();
                
                if (response.ok && result.code === 0) {
                    showSuccess('login-success', '注册成功！正在自动登录...');
                    // 自动填充登录表单
                    document.getElementById('login-username').value = userData.username;
                    document.getElementById('login-password').value = userData.password;
                    // 自动登录
                    setTimeout(() => performLogin(), 1000);
                } else {
                    showError('login-error', result.message || '注册失败');
                }
            } catch (error) {
                showError('login-error', '注册时发生错误: ' + error.message);
            }
        }

        async function performLogin() {
            const loginData = {
                username: document.getElementById('login-username').value,
                password: document.getElementById('login-password').value
            };

            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(loginData)
                });

                const result = await response.json();
                
                if (response.ok && result.code === 0) {
                    currentUser = result.data.userInfo;
                    authToken = result.data.access_token;
                    
                    // 保存到localStorage
                    localStorage.setItem('mastea_token', authToken);
                    localStorage.setItem('mastea_user', JSON.stringify(currentUser));
                    
                    showDashboard();
                } else {
                    showError('login-error', result.message || '登录失败');
                }
            } catch (error) {
                showError('login-error', '登录时发生错误: ' + error.message);
            }
        }

        function logout() {
            currentUser = null;
            authToken = null;
            localStorage.removeItem('mastea_token');
            localStorage.removeItem('mastea_user');
            
            document.getElementById('login-section').style.display = 'block';
            document.getElementById('dashboard-section').style.display = 'none';
        }

        function showDashboard() {
            document.getElementById('login-section').style.display = 'none';
            document.getElementById('dashboard-section').style.display = 'block';
            
            // 更新用户信息显示
            document.getElementById('username').textContent = currentUser.username;
            
            // 加载概览数据
            loadOverview();
        }

        // 导航相关
        function showSection(sectionName) {
            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // 更新菜单状态
            document.querySelectorAll('.menu-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示选中的内容区域
            document.getElementById(sectionName + '-section').classList.add('active');
            event.target.classList.add('active');
            
            // 根据不同section加载对应数据
            switch(sectionName) {
                case 'overview':
                    loadOverview();
                    break;
                case 'reports':
                    loadReports();
                    break;
                case 'chat':
                    loadConversations();
                    break;
                case 'products':
                    loadProducts();
                    break;
                case 'cart':
                    loadCart();
                    break;
                case 'addresses':
                    loadAddresses();
                    break;
                case 'orders':
                    loadOrders();
                    break;
                case 'profile':
                    loadProfile();
                    break;
            }
        }

        // 概览数据加载
        async function loadOverview() {
            showLoading('overview-loading');
            
            try {
                // 填充用户基本信息
                document.getElementById('user-id').textContent = currentUser.id;
                document.getElementById('user-name').textContent = currentUser.username;
                document.getElementById('user-email').textContent = currentUser.email;
                document.getElementById('user-mobile').textContent = currentUser.mobile;
                
                // 并行加载各种统计数据
                const [reports, conversations, cart, orders] = await Promise.all([
                    loadReportCounts(),
                    loadConversationCount(),
                    loadCartCount(),
                    loadOrderCount()
                ]);
                
                showLoading('overview-loading', false);
            } catch (error) {
                showError('overview-error', '加载概览数据失败: ' + error.message);
                showLoading('overview-loading', false);
            }
        }

        async function loadReportCounts() {
            try {
                const [constResponse, compResponse] = await Promise.all([
                    fetch(`/api/v1/reports/constitution/list?user_id=${currentUser.id}`, {
                        headers: getAuthHeaders()
                    }),
                    fetch(`/api/v1/reports/complaint/list?user_id=${currentUser.id}`, {
                        headers: getAuthHeaders()
                    })
                ]);
                
                const constResult = await constResponse.json();
                const compResult = await compResponse.json();
                
                const constCount = constResult.data ? constResult.data.length : 0;
                const compCount = compResult.data ? compResult.data.length : 0;
                
                document.getElementById('constitution-count').textContent = constCount;
                document.getElementById('complaint-count').textContent = compCount;
                
                return { constitution: constCount, complaint: compCount };
            } catch (error) {
                console.error('加载报告统计失败:', error);
                return { constitution: 0, complaint: 0 };
            }
        }

        async function loadConversationCount() {
            try {
                const response = await fetch(`/api/v1/chat/conversations?user_id=${currentUser.id}`, {
                    headers: getAuthHeaders()
                });
                const result = await response.json();
                const count = result.data ? result.data.length : 0;
                document.getElementById('conversation-count').textContent = count;
                return count;
            } catch (error) {
                console.error('加载对话统计失败:', error);
                return 0;
            }
        }

        async function loadCartCount() {
            try {
                const response = await fetch(`/api/v1/cart?user_id=${currentUser.id}`, {
                    headers: getAuthHeaders()
                });
                const result = await response.json();
                const count = result.data ? result.data.length : 0;
                document.getElementById('cart-count').textContent = count;
                return count;
            } catch (error) {
                console.error('加载购物车统计失败:', error);
                return 0;
            }
        }

        async function loadOrderCount() {
            try {
                const response = await fetch(`/api/v1/orders?user_id=${currentUser.id}`, {
                    headers: getAuthHeaders()
                });
                const result = await response.json();
                const count = result.data ? result.data.length : 0;
                document.getElementById('order-count').textContent = count;
                return count;
            } catch (error) {
                console.error('加载订单统计失败:', error);
                return 0;
            }
        }

        // 报告相关
        async function loadReports() {
            showLoading('reports-loading');
            
            try {
                const [constResponse, compResponse] = await Promise.all([
                    fetch(`/api/v1/reports/constitution/list?user_id=${currentUser.id}`, {
                        headers: getAuthHeaders()
                    }),
                    fetch(`/api/v1/reports/complaint/list?user_id=${currentUser.id}`, {
                        headers: getAuthHeaders()
                    })
                ]);
                
                const constResult = await constResponse.json();
                const compResult = await compResponse.json();
                
                // 显示体质报告
                const constReportsHtml = constResult.data && constResult.data.length > 0 
                    ? constResult.data.map(report => `
                        <div style="padding: 10px; border-bottom: 1px solid #eee;">
                            <strong>${report.title || '体质评估报告'}</strong><br>
                            <small>创建时间: ${new Date(report.created_at).toLocaleString()}</small><br>
                            <button class="btn btn-outline" onclick="viewReport('${report.id}', 'constitution')">查看详情</button>
                            <button class="btn btn-warning" onclick="useForComplaint('${report.id}')">用于主诉评估</button>
                        </div>
                    `).join('')
                    : '<p>暂无体质报告</p>';
                
                document.getElementById('constitution-reports').innerHTML = constReportsHtml;
                
                // 显示主诉报告
                const compReportsHtml = compResult.data && compResult.data.length > 0
                    ? compResult.data.map(report => `
                        <div style="padding: 10px; border-bottom: 1px solid #eee;">
                            <strong>${report.title || '主诉评估报告'}</strong><br>
                            <small>创建时间: ${new Date(report.created_at).toLocaleString()}</small><br>
                            <button class="btn btn-outline" onclick="viewReport('${report.id}', 'complaint')">查看详情</button>
                        </div>
                    `).join('')
                    : '<p>暂无主诉报告</p>';
                
                document.getElementById('complaint-reports').innerHTML = compReportsHtml;
                
                showLoading('reports-loading', false);
            } catch (error) {
                showError('reports-error', '加载报告失败: ' + error.message);
                showLoading('reports-loading', false);
            }
        }

        async function createConstitutionReport() {
            try {
                const reportData = {
                    user_id: currentUser.id,
                    answers: {
                        gender: "男",
                        age_group: "30-40",
                        q3_core_symptom: "疲劳乏力，容易感冒",
                        q4_sweating: ["白天动则大汗"],
                        q5_stool: "规律正常",
                        q6_sleep: "睡眠正常",
                        q7_season_response: ["四季适应良好"],
                        q8_diet_preference: "规律清淡",
                        q9_long_term_habits: ["作息规律"],
                        q10_body_type: "肌肉发达，体脂低",
                        q11_skin_status: "光滑红润",
                        q12_priority_conditioning: "增强体质"
                    }
                };
                
                const response = await fetch('/api/v1/reports/constitution/calculate', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(reportData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    alert('体质报告生成成功！');
                    loadReports(); // 重新加载报告列表
                } else {
                    throw new Error(result.message || '生成报告失败');
                }
            } catch (error) {
                showError('reports-error', '生成体质报告失败: ' + error.message);
            }
        }

        function useForComplaint(reportId) {
            currentBaseReportId = reportId;
            alert('已选择此体质报告作为主诉评估的基础，点击"开始主诉评估"继续。');
        }

        async function startComplaintAssessment() {
            if (!currentBaseReportId) {
                alert('请先选择一个体质报告作为基础');
                return;
            }
            
            try {
                const data = {
                    user_id: currentUser.id,
                    base_constitution_report_id: currentBaseReportId
                };
                
                const response = await fetch('/api/v1/reports/complaint/start', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    alert('主诉评估已开始！您可以在AI咨询页面继续对话。');
                    showSection('chat');
                } else {
                    throw new Error(result.detail || '开始主诉评估失败');
                }
            } catch (error) {
                showError('reports-error', '开始主诉评估失败: ' + error.message);
            }
        }

        async function viewReport(reportId, type) {
            try {
                const response = await fetch(`/api/v1/reports/${type}/${reportId}`, {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    // 简单显示报告内容
                    const content = JSON.stringify(result, null, 2);
                    alert(`报告详情：\n${content.substring(0, 500)}...`);
                } else {
                    throw new Error('获取报告详情失败');
                }
            } catch (error) {
                alert('查看报告失败: ' + error.message);
            }
        }

        // 对话相关
        async function loadConversations() {
            showLoading('chat-loading');
            
            try {
                const response = await fetch(`/api/v1/chat/conversations?user_id=${currentUser.id}`, {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                const conversationsHtml = result.data && result.data.length > 0
                    ? result.data.map(conv => `
                        <div style="padding: 10px; border-bottom: 1px solid #eee;">
                            <strong>${conv.title}</strong><br>
                            <small>创建时间: ${new Date(conv.created_at).toLocaleString()}</small><br>
                            <button class="btn btn-outline" onclick="viewConversation('${conv.id}')">查看对话</button>
                        </div>
                    `).join('')
                    : '<p>暂无对话</p>';
                
                document.getElementById('conversation-list').innerHTML = conversationsHtml;
                
                showLoading('chat-loading', false);
            } catch (error) {
                showError('chat-error', '加载对话失败: ' + error.message);
                showLoading('chat-loading', false);
            }
        }

        async function createNewConversation() {
            try {
                const response = await fetch('/api/v1/chat/conversations', {
                    method: 'POST',
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    alert('新对话创建成功！');
                    loadConversations();
                } else {
                    throw new Error('创建对话失败');
                }
            } catch (error) {
                showError('chat-error', '创建对话失败: ' + error.message);
            }
        }

        function viewConversation(conversationId) {
            // 可以跳转到专门的聊天页面
            window.open(`/chat?conversation_id=${conversationId}`, '_blank');
        }

        // 商品相关
        async function loadProducts() {
            showLoading('products-loading');
            
            try {
                const response = await fetch('/api/v1/goods', {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                const productsHtml = result.data && result.data.length > 0
                    ? result.data.map(product => `
                        <div class="card">
                            <div class="card-title">${product.name || product.title}</div>
                            <p>价格: ¥${product.price || '暂无价格'}</p>
                            <p>${product.description || '暂无描述'}</p>
                            <button class="btn btn-success" onclick="addToCart('${product.id}')">🛒 加入购物车</button>
                        </div>
                    `).join('')
                    : '<p>暂无商品数据</p>';
                
                document.getElementById('products-grid').innerHTML = productsHtml;
                
                showLoading('products-loading', false);
            } catch (error) {
                showError('products-error', '加载商品失败: ' + error.message);
                showLoading('products-loading', false);
            }
        }

        async function searchProducts() {
            const keyword = document.getElementById('product-search').value;
            if (!keyword) return;
            
            try {
                const response = await fetch(`/api/v1/goods/search?q=${encodeURIComponent(keyword)}`, {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                // 处理搜索结果...
                
            } catch (error) {
                showError('products-error', '搜索商品失败: ' + error.message);
            }
        }

        // 购物车相关
        async function addToCart(goodsId) {
            try {
                const data = {
                    goods_id: goodsId,
                    quantity: 1,
                    user_id: currentUser.id
                };
                
                const response = await fetch('/api/v1/cart/add', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(data)
                });
                
                if (response.ok) {
                    alert('商品已添加到购物车！');
                    loadOverview(); // 更新购物车数量
                }
            } catch (error) {
                alert('添加到购物车失败: ' + error.message);
            }
        }

        async function loadCart() {
            showLoading('cart-loading');
            
            try {
                const response = await fetch(`/api/v1/cart?user_id=${currentUser.id}`, {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                const cartHtml = result.data && result.data.length > 0
                    ? result.data.map(item => `
                        <div style="padding: 10px; border-bottom: 1px solid #eee;">
                            <strong>${item.goods_name || '商品'}</strong><br>
                            <span>数量: ${item.quantity}</span><br>
                            <button class="btn btn-warning" onclick="updateCartItem('${item.goods_id}', ${item.quantity + 1})">➕</button>
                            <button class="btn btn-warning" onclick="updateCartItem('${item.goods_id}', ${Math.max(1, item.quantity - 1)})">➖</button>
                            <button class="btn btn-danger" onclick="removeFromCart('${item.goods_id}')">🗑️ 删除</button>
                        </div>
                    `).join('')
                    : '<p>购物车为空</p>';
                
                document.getElementById('cart-items').innerHTML = cartHtml;
                
                showLoading('cart-loading', false);
            } catch (error) {
                showError('cart-error', '加载购物车失败: ' + error.message);
                showLoading('cart-loading', false);
            }
        }

        async function updateCartItem(goodsId, quantity) {
            try {
                const data = {
                    goods_id: goodsId,
                    quantity: quantity,
                    user_id: currentUser.id
                };
                
                const response = await fetch('/api/v1/cart/update', {
                    method: 'PUT',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(data)
                });
                
                if (response.ok) {
                    loadCart(); // 重新加载购物车
                }
            } catch (error) {
                showError('cart-error', '更新购物车失败: ' + error.message);
            }
        }

        async function removeFromCart(goodsId) {
            try {
                const response = await fetch(`/api/v1/cart/remove?goods_id=${goodsId}&user_id=${currentUser.id}`, {
                    method: 'DELETE',
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    loadCart(); // 重新加载购物车
                }
            } catch (error) {
                showError('cart-error', '删除商品失败: ' + error.message);
            }
        }

        async function clearCart() {
            if (confirm('确定要清空购物车吗？')) {
                // 实现清空购物车逻辑
                alert('清空购物车功能待实现');
            }
        }

        async function checkout() {
            alert('结算功能待实现，请先添加收货地址');
            showSection('addresses');
        }

        // 地址相关
        async function loadAddresses() {
            showLoading('addresses-loading');
            
            try {
                const response = await fetch(`/api/v1/addresses?user_id=${currentUser.id}`, {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                const addressesHtml = result.data && result.data.length > 0
                    ? result.data.map(addr => `
                        <div style="padding: 10px; border-bottom: 1px solid #eee;">
                            <strong>${addr.recipient_name}</strong> ${addr.recipient_phone}<br>
                            <span>${addr.address}</span><br>
                            <button class="btn btn-warning" onclick="editAddress('${addr.id}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteAddress('${addr.id}')">删除</button>
                        </div>
                    `).join('')
                    : '<p>暂无收货地址</p>';
                
                document.getElementById('addresses-list').innerHTML = addressesHtml;
                
                showLoading('addresses-loading', false);
            } catch (error) {
                showError('addresses-error', '加载地址失败: ' + error.message);
                showLoading('addresses-loading', false);
            }
        }

        function showAddAddressForm() {
            document.getElementById('add-address-form').style.display = 'block';
        }

        function hideAddAddressForm() {
            document.getElementById('add-address-form').style.display = 'none';
        }

        async function addAddress() {
            try {
                const data = {
                    recipient_name: document.getElementById('address-name').value,
                    recipient_phone: document.getElementById('address-phone').value,
                    address: document.getElementById('address-detail').value,
                    user_id: currentUser.id
                };
                
                const response = await fetch('/api/v1/addresses', {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(data)
                });
                
                if (response.ok) {
                    alert('地址添加成功！');
                    hideAddAddressForm();
                    loadAddresses();
                } else {
                    throw new Error('添加地址失败');
                }
            } catch (error) {
                showError('addresses-error', '添加地址失败: ' + error.message);
            }
        }

        function editAddress(addressId) {
            alert('编辑地址功能待实现');
        }

        async function deleteAddress(addressId) {
            if (confirm('确定要删除此地址吗？')) {
                // 实现删除地址逻辑
                alert('删除地址功能待实现');
            }
        }

        // 订单相关
        async function loadOrders() {
            showLoading('orders-loading');
            
            try {
                const response = await fetch(`/api/v1/orders?user_id=${currentUser.id}`, {
                    headers: getAuthHeaders()
                });
                
                const result = await response.json();
                
                const ordersHtml = result.data && result.data.length > 0
                    ? result.data.map(order => `
                        <div style="padding: 10px; border-bottom: 1px solid #eee;">
                            <strong>订单号: ${order.id}</strong><br>
                            <span>状态: <span class="status-badge status-info">${order.status || '待处理'}</span></span><br>
                            <span>金额: ¥${order.total_amount || '0.00'}</span><br>
                            <small>创建时间: ${new Date(order.created_at).toLocaleString()}</small><br>
                            <button class="btn btn-outline" onclick="viewOrder('${order.id}')">查看详情</button>
                            <button class="btn btn-warning" onclick="payOrder('${order.id}')">支付</button>
                        </div>
                    `).join('')
                    : '<p>暂无订单</p>';
                
                document.getElementById('orders-list').innerHTML = ordersHtml;
                
                showLoading('orders-loading', false);
            } catch (error) {
                showError('orders-error', '加载订单失败: ' + error.message);
                showLoading('orders-loading', false);
            }
        }

        function viewOrder(orderId) {
            alert(`查看订单详情: ${orderId}`);
        }

        async function payOrder(orderId) {
            try {
                const response = await fetch(`/api/v1/orders/${orderId}/pay`, {
                    method: 'POST',
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    alert('支付成功！');
                    loadOrders();
                }
            } catch (error) {
                alert('支付失败: ' + error.message);
            }
        }

        // 个人信息相关
        async function loadProfile() {
            if (currentUser) {
                document.getElementById('profile-username').value = currentUser.username;
                document.getElementById('profile-email').value = currentUser.email;
                document.getElementById('profile-mobile').value = currentUser.mobile;
                document.getElementById('profile-gender').value = currentUser.gender || '';
                document.getElementById('profile-age').value = currentUser.age || '';
            }
        }

        async function updateProfile() {
            try {
                const data = {
                    gender: document.getElementById('profile-gender').value,
                    age: parseInt(document.getElementById('profile-age').value) || null
                };
                
                // 调用更新用户信息的API
                alert('个人信息更新功能待实现');
            } catch (error) {
                showError('profile-error', '更新个人信息失败: ' + error.message);
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否已登录
            const savedToken = localStorage.getItem('mastea_token');
            const savedUser = localStorage.getItem('mastea_user');
            
            if (savedToken && savedUser) {
                try {
                    authToken = savedToken;
                    currentUser = JSON.parse(savedUser);
                    showDashboard();
                } catch (error) {
                    console.error('恢复登录状态失败:', error);
                    logout();
                }
            }
        });
    </script>
</body>
</html>
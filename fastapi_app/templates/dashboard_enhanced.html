<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麦斯特养生茶 - 智能健康管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 0;
            margin-bottom: 20px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        
        .header .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .welcome-text {
            color: #666;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid #3498db;
            color: #3498db;
        }
        
        .btn-success { background: linear-gradient(45deg, #27ae60, #2ecc71); }
        .btn-warning { background: linear-gradient(45deg, #f39c12, #e67e22); }
        .btn-danger { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .btn-info { background: linear-gradient(45deg, #17a2b8, #138496); }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        body {
            padding-top: 80px; /* Account for fixed header */
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .sidebar {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 15px;
            position: sticky;
            top: 100px;
            z-index: 100;
        }
        
        .sidebar.collapsed {
            max-height: 60px;
            overflow: hidden;
        }
        
        .sidebar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .toggle-sidebar {
            background: #3498db;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
        }
        
        .main-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 25px;
            min-height: 600px;
        }
        
        .section-title {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        
        .menu-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .menu-item {
            margin: 0;
        }
        
        .menu-link {
            display: block;
            padding: 8px 12px;
            background: #f8f9fa;
            color: #333;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
            font-size: 13px;
            text-align: center;
        }
        
        .menu-link:hover, .menu-link.active {
            background: #3498db;
            color: white;
        }
        
        .content-section {
            display: none;
        }
        
        .content-section.active {
            display: block;
        }
        
        .info-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .card-title {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        
        /* 聊天相关样式 */
        .chatbox {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow-y: auto;
            padding: 15px;
            background: #fafafa;
            margin-bottom: 15px;
        }
        
        .message-container {
            margin-bottom: 15px;
        }
        
        .message {
            max-width: 80%;
            padding: 10px 15px;
            border-radius: 18px;
            margin-bottom: 5px;
        }
        
        .user-message {
            background: #3498db;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .ai-message {
            background: #e9ecef;
            color: #333;
            margin-right: auto;
        }
        
        .timestamp {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
        
        .chat-input-area {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .chat-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            resize: none;
            max-height: 100px;
        }
        
        .send-button {
            padding: 10px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
        }
        
        /* API调试样式 */
        .api-debug {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .api-request {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        
        .api-response {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            cursor: pointer;
            margin-right: 2px;
        }
        
        .tab.active {
            background: white;
            border-bottom: 1px solid white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .header .container {
                flex-direction: column;
                gap: 15px;
            }
            
            .sidebar {
                position: relative;
                top: auto;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">🍃 麦斯特养生茶 - 智能健康管理平台</div>
            <div class="user-info">
                <span class="welcome-text">欢迎，<span id="username">访客</span></span>
                <button class="btn btn-outline" onclick="logout()">退出登录</button>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- 登录表单 -->
        <div id="login-section" class="main-content" style="max-width: 450px; margin: 50px auto;">
            <h2 class="section-title">🔐 用户登录</h2>
            <div class="error" id="login-error"></div>
            <div class="success" id="login-success"></div>
            
            <div class="form-group">
                <label class="form-label">用户名/邮箱/手机号:</label>
                <input type="text" class="form-input" id="login-username" value="testuser123">
            </div>
            <div class="form-group">
                <label class="form-label">密码:</label>
                <input type="password" class="form-input" id="login-password" value="123456">
            </div>
            <div class="form-group">
                <button class="btn btn-success" onclick="performLogin()" style="width: 100%;">登录</button>
            </div>
            <div class="form-group" style="text-align: center;">
                <span>没有账号？</span>
                <a href="#" onclick="showRegister()">立即注册</a>
                |
                <a href="#" onclick="quickTestLogin()">快速测试登录</a>
            </div>
            
            <!-- 注册表单 -->
            <div id="register-form" style="display: none;">
                <h3 style="margin: 20px 0;">用户注册</h3>
                <div class="form-group">
                    <label class="form-label">用户名:</label>
                    <input type="text" class="form-input" id="reg-username" value="">
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱:</label>
                    <input type="email" class="form-input" id="reg-email" value="">
                </div>
                <div class="form-group">
                    <label class="form-label">手机号:</label>
                    <input type="text" class="form-input" id="reg-mobile" value="">
                </div>
                <div class="form-group">
                    <label class="form-label">密码:</label>
                    <input type="password" class="form-input" id="reg-password" value="123456">
                </div>
                <div class="form-group">
                    <button class="btn btn-success" onclick="performRegister()" style="width: 100%;">注册</button>
                </div>
                <div class="form-group" style="text-align: center;">
                    <a href="#" onclick="showLogin()">返回登录</a>
                </div>
            </div>
        </div>

        <!-- 主面板 -->
        <div id="dashboard-section" class="dashboard" style="display: none;">
            <div class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h3 class="section-title" style="margin: 0;">🧭 功能导航</h3>
                    <button class="toggle-sidebar" onclick="toggleSidebar()">
                        <span id="toggle-text">收起</span>
                    </button>
                </div>
                <div class="menu-grid">
                    <div class="menu-item">
                        <a href="#" class="menu-link active" onclick="showSection('overview', this)">
                            📊 概览
                        </a>
                    </div>
                    <div class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('reports', this)">
                            📋 体质报告
                        </a>
                    </div>
                    <div class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('assessment', this)">
                            🩺 健康评估
                        </a>
                    </div>
                    <div class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('chat', this)">
                            💬 AI咨询
                        </a>
                    </div>
                    <div class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('products', this)">
                            🛍️ 商品浏览
                        </a>
                    </div>
                    <div class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('cart', this)">
                            🛒 购物车
                        </a>
                    </div>
                    <div class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('addresses', this)">
                            📍 收货地址
                        </a>
                    </div>
                    <div class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('orders', this)">
                            📦 我的订单
                        </a>
                    </div>
                    <div class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('api-debug', this)">
                            🔧 API调试
                        </a>
                    </div>
                    <div class="menu-item">
                        <a href="#" class="menu-link" onclick="showSection('profile', this)">
                            👤 个人信息
                        </a>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <!-- 概览页面 -->
                <div id="overview-section" class="content-section active">
                    <h2 class="section-title">📊 用户概览</h2>
                    <div class="loading" id="overview-loading">加载中...</div>
                    <div class="error" id="overview-error"></div>
                    
                    <div class="info-card">
                        <h3 class="card-title">👤 个人信息</h3>
                        <div class="info-row">
                            <span class="info-label">用户ID:</span>
                            <span class="info-value" id="user-id">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">用户名:</span>
                            <span class="info-value" id="user-name">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">邮箱:</span>
                            <span class="info-value" id="user-email">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">手机号:</span>
                            <span class="info-value" id="user-mobile">-</span>
                        </div>
                    </div>

                    <div class="card-grid">
                        <div class="card">
                            <div class="card-title">📋 我的报告</div>
                            <p>体质报告: <span id="constitution-count">0</span> 份</p>
                            <p>主诉报告: <span id="complaint-count">0</span> 份</p>
                            <button class="btn" onclick="showSection('reports', this)">查看报告</button>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">💬 AI咨询</div>
                            <p>对话数量: <span id="conversation-count">0</span> 个</p>
                            <button class="btn" onclick="showSection('chat', this)">开始咨询</button>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">🛒 购物车</div>
                            <p>商品数量: <span id="cart-count">0</span> 件</p>
                            <button class="btn" onclick="showSection('cart', this)">查看购物车</button>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">📦 订单</div>
                            <p>订单数量: <span id="order-count">0</span> 个</p>
                            <button class="btn" onclick="showSection('orders', this)">查看订单</button>
                        </div>
                    </div>
                </div>

                <!-- 体质报告页面 -->
                <div id="reports-section" class="content-section">
                    <h2 class="section-title">📋 体质报告管理</h2>
                    <div class="loading" id="reports-loading">加载中...</div>
                    <div class="error" id="reports-error"></div>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-success" onclick="createConstitutionReport()">📝 生成新的体质报告</button>
                        <button class="btn btn-warning" onclick="startComplaintAssessment()">🩺 开始主诉评估</button>
                        <button class="btn btn-info" onclick="showSection('assessment', this)">🔬 进入完整评估</button>
                    </div>
                    
                    <div class="card-grid">
                        <div class="card">
                            <div class="card-title">📊 体质报告列表</div>
                            <div id="constitution-reports">
                                <p>暂无体质报告</p>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-title">🩺 主诉报告列表</div>
                            <div id="complaint-reports">
                                <p>暂无主诉报告</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 健康评估页面 -->
                <div id="assessment-section" class="content-section">
                    <h2 class="section-title">🩺 健康评估中心</h2>
                    <div class="loading" id="assessment-loading">加载中...</div>
                    <div class="error" id="assessment-error"></div>
                    
                    <div class="tabs">
                        <div class="tab active" onclick="showAssessmentTab('questionnaire')">📝 体质问卷</div>
                        <div class="tab" onclick="showAssessmentTab('complaint')">💬 主诉评估</div>
                    </div>
                    
                    <!-- 体质问卷 -->
                    <div id="questionnaire-tab" class="tab-content active">
                        <h3>中医体质评估问卷</h3>
                        <form id="constitution-form">
                            <div class="form-group">
                                <label class="form-label">性别:</label>
                                <select class="form-select" name="gender">
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">年龄段:</label>
                                <select class="form-select" name="age_group">
                                    <option value="20-30">20-30岁</option>
                                    <option value="30-40" selected>30-40岁</option>
                                    <option value="40-50">40-50岁</option>
                                    <option value="50-60">50-60岁</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">3. 您最突出的核心症状是:</label>
                                <select class="form-select" name="q3_core_symptom">
                                    <option value="疲劳乏力，容易感冒">疲劳乏力，容易感冒</option>
                                    <option value="怕冷，手脚冰凉">怕冷，手脚冰凉</option>
                                    <option value="口干咽燥，手足心热">口干咽燥，手足心热</option>
                                    <option value="身体困重，面部油腻">身体困重，面部油腻</option>
                                    <option value="口苦口臭，皮肤长痘">口苦口臭，皮肤长痘</option>
                                    <option value="面色暗沉，局部刺痛">面色暗沉，局部刺痛</option>
                                    <option value="胸闷胁痛，情绪抑郁">胸闷胁痛，情绪抑郁</option>
                                    <option value="过敏频发，皮肤划痕">过敏频发，皮肤划痕</option>
                                    <option value="无明显不适" selected>无明显不适</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">12. 您希望优先调理的方向:</label>
                                <select class="form-select" name="q12_priority_conditioning">
                                    <option value="增强体质" selected>增强体质</option>
                                    <option value="改善睡眠">改善睡眠</option>
                                    <option value="调理肠胃">调理肠胃</option>
                                    <option value="美容养颜">美容养颜</option>
                                    <option value="减肥塑形">减肥塑形</option>
                                </select>
                            </div>
                        </form>
                        <button class="btn btn-success" onclick="submitConstitutionForm()">📊 生成体质报告</button>
                        
                        <div class="api-debug">
                            <div class="api-request" id="constitution-request" style="display: none;"></div>
                            <div class="api-response" id="constitution-response" style="display: none;"></div>
                        </div>
                    </div>
                    
                    <!-- 主诉评估 -->
                    <div id="complaint-tab" class="tab-content">
                        <h3>主诉评估</h3>
                        <div class="form-group">
                            <label class="form-label">选择基础体质报告:</label>
                            <select class="form-select" id="base-report-select">
                                <option value="">请先生成体质报告</option>
                            </select>
                        </div>
                        <button class="btn btn-warning" onclick="startComplaintFromAssessment()">🩺 开始主诉评估</button>
                        
                        <div id="complaint-chat-area" style="display: none; margin-top: 20px;">
                            <div class="chatbox" id="complaint-chatbox"></div>
                            <div class="chat-input-area">
                                <textarea class="chat-input" id="complaint-input" placeholder="请描述您的主要症状或不适..."></textarea>
                                <button class="send-button" onclick="sendComplaintMessage()">发送</button>
                            </div>
                        </div>
                        
                        <div class="api-debug">
                            <div class="api-request" id="complaint-request" style="display: none;"></div>
                            <div class="api-response" id="complaint-response" style="display: none;"></div>
                        </div>
                    </div>
                </div>

                <!-- AI咨询页面 -->
                <div id="chat-section" class="content-section">
                    <h2 class="section-title">💬 AI健康咨询</h2>
                    <div class="loading" id="chat-loading">加载中...</div>
                    <div class="error" id="chat-error"></div>
                    
                    <div style="margin-bottom: 20px;">
                        <button class="btn btn-success" onclick="createNewConversation()">💬 开始新对话</button>
                        <button class="btn btn-info" onclick="loadConversations()">🔄 刷新对话列表</button>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 20px;">
                        <!-- 对话列表 -->
                        <div>
                            <h3>对话列表</h3>
                            <div id="conversation-list" style="max-height: 400px; overflow-y: auto;">
                                <p>暂无对话</p>
                            </div>
                        </div>
                        
                        <!-- 聊天区域 -->
                        <div>
                            <h3>聊天窗口</h3>
                            <div class="chatbox" id="main-chatbox"></div>
                            <div class="chat-input-area">
                                <textarea class="chat-input" id="main-chat-input" placeholder="请输入您要咨询的健康问题..."></textarea>
                                <button class="send-button" onclick="sendMainChatMessage()">发送</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="api-debug">
                        <div class="api-request" id="chat-request" style="display: none;"></div>
                        <div class="api-response" id="chat-response" style="display: none;"></div>
                    </div>
                </div>

                <!-- 商品浏览页面 -->
                <div id="products-section" class="content-section">
                    <h2 class="section-title">🛍️ 商品浏览</h2>
                    <div class="loading" id="products-loading">加载中...</div>
                    <div class="error" id="products-error"></div>
                    
                    <div class="form-group">
                        <label class="form-label">搜索商品:</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" class="form-input" id="product-search" placeholder="输入商品名称...">
                            <button class="btn" onclick="searchProducts()">🔍 搜索</button>
                            <button class="btn btn-info" onclick="loadProducts()">🔄 刷新</button>
                        </div>
                    </div>
                    
                    <div id="products-grid" class="card-grid">
                        <p>正在加载商品...</p>
                    </div>
                    
                    <div class="api-debug">
                        <div class="api-request" id="products-request" style="display: none;"></div>
                        <div class="api-response" id="products-response" style="display: none;"></div>
                    </div>
                </div>

                <!-- 购物车页面 -->
                <div id="cart-section" class="content-section">
                    <h2 class="section-title">🛒 我的购物车</h2>
                    <div class="loading" id="cart-loading">加载中...</div>
                    <div class="error" id="cart-error"></div>
                    
                    <div id="cart-items">
                        <p>购物车为空</p>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <button class="btn btn-success" onclick="checkout()">💰 结算</button>
                        <button class="btn btn-warning" onclick="clearCart()">🗑️ 清空购物车</button>
                        <button class="btn btn-info" onclick="loadCart()">🔄 刷新</button>
                    </div>
                    
                    <div class="api-debug">
                        <div class="api-request" id="cart-request" style="display: none;"></div>
                        <div class="api-response" id="cart-response" style="display: none;"></div>
                    </div>
                </div>

                <!-- 收货地址页面 -->
                <div id="addresses-section" class="content-section">
                    <h2 class="section-title">📍 收货地址管理</h2>
                    <div class="loading" id="addresses-loading">加载中...</div>
                    <div class="error" id="addresses-error"></div>
                    
                    <button class="btn btn-success" onclick="showAddAddressForm()">📍 添加新地址</button>
                    
                    <!-- 添加地址表单 -->
                    <div id="add-address-form" style="display: none; margin-top: 20px;">
                        <h3>添加收货地址</h3>
                        <div class="form-group">
                            <label class="form-label">收货人姓名:</label>
                            <input type="text" class="form-input" id="address-name" value="张三">
                        </div>
                        <div class="form-group">
                            <label class="form-label">联系电话:</label>
                            <input type="text" class="form-input" id="address-phone" value="13800138000">
                        </div>
                        <div class="form-group">
                            <label class="form-label">省份:</label>
                            <select class="form-select" id="address-province">
                                <option value="北京市">北京市</option>
                                <option value="上海市">上海市</option>
                                <option value="广东省">广东省</option>
                                <option value="浙江省">浙江省</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">城市:</label>
                            <select class="form-select" id="address-city">
                                <option value="北京市">北京市</option>
                                <option value="上海市">上海市</option>
                                <option value="广州市">广州市</option>
                                <option value="杭州市">杭州市</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">区县:</label>
                            <select class="form-select" id="address-district">
                                <option value="朝阳区">朝阳区</option>
                                <option value="海淀区">海淀区</option>
                                <option value="西城区">西城区</option>
                                <option value="东城区">东城区</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">详细地址:</label>
                            <textarea class="form-textarea" id="address-detail">某某街道某某小区123号楼456室</textarea>
                        </div>
                        <button class="btn btn-success" onclick="addAddress()">保存地址</button>
                        <button class="btn btn-outline" onclick="hideAddAddressForm()">取消</button>
                    </div>
                    
                    <div id="addresses-list" style="margin-top: 20px;">
                        <p>暂无收货地址</p>
                    </div>
                    
                    <div class="api-debug">
                        <div class="api-request" id="addresses-request" style="display: none;"></div>
                        <div class="api-response" id="addresses-response" style="display: none;"></div>
                    </div>
                </div>

                <!-- 订单页面 -->
                <div id="orders-section" class="content-section">
                    <h2 class="section-title">📦 我的订单</h2>
                    <div class="loading" id="orders-loading">加载中...</div>
                    <div class="error" id="orders-error"></div>
                    
                    <div id="orders-list">
                        <p>暂无订单</p>
                    </div>
                    
                    <div class="api-debug">
                        <div class="api-request" id="orders-request" style="display: none;"></div>
                        <div class="api-response" id="orders-response" style="display: none;"></div>
                    </div>
                </div>

                <!-- API调试页面 -->
                <div id="api-debug-section" class="content-section">
                    <h2 class="section-title">🔧 API调试工具</h2>
                    
                    <div class="tabs">
                        <div class="tab active" onclick="showDebugTab('auth')">🔐 认证</div>
                        <div class="tab" onclick="showDebugTab('reports')">📋 报告</div>
                        <div class="tab" onclick="showDebugTab('chat')">💬 聊天</div>
                        <div class="tab" onclick="showDebugTab('goods')">🛍️ 商品</div>
                        <div class="tab" onclick="showDebugTab('orders')">📦 订单</div>
                    </div>
                    
                    <div id="auth-debug" class="tab-content active">
                        <h3>认证API测试</h3>
                        <button class="btn" onclick="testRegisterAPI()">测试注册</button>
                        <button class="btn" onclick="testLoginAPI()">测试登录</button>
                        <button class="btn" onclick="testUserInfoAPI()">测试用户信息</button>
                        <div class="api-debug">
                            <div class="api-request" id="auth-debug-request"></div>
                            <div class="api-response" id="auth-debug-response"></div>
                        </div>
                    </div>
                    
                    <div id="reports-debug" class="tab-content">
                        <h3>报告API测试</h3>
                        <button class="btn" onclick="testConstitutionListAPI()">体质报告列表</button>
                        <button class="btn" onclick="testComplaintListAPI()">主诉报告列表</button>
                        <button class="btn" onclick="testCreateConstitutionAPI()">生成体质报告</button>
                        <div class="api-debug">
                            <div class="api-request" id="reports-debug-request"></div>
                            <div class="api-response" id="reports-debug-response"></div>
                        </div>
                    </div>
                    
                    <div id="chat-debug" class="tab-content">
                        <h3>聊天API测试</h3>
                        <button class="btn" onclick="testConversationsAPI()">对话列表</button>
                        <button class="btn" onclick="testCreateConversationAPI()">创建对话</button>
                        <button class="btn" onclick="testSendMessageAPI()">发送消息</button>
                        <div class="api-debug">
                            <div class="api-request" id="chat-debug-request"></div>
                            <div class="api-response" id="chat-debug-response"></div>
                        </div>
                    </div>
                    
                    <div id="goods-debug" class="tab-content">
                        <h3>商品API测试</h3>
                        <button class="btn" onclick="testGoodsListAPI()">商品列表</button>
                        <button class="btn" onclick="testCartAPI()">购物车</button>
                        <button class="btn" onclick="testAddToCartAPI()">添加到购物车</button>
                        <div class="api-debug">
                            <div class="api-request" id="goods-debug-request"></div>
                            <div class="api-response" id="goods-debug-response"></div>
                        </div>
                    </div>
                    
                    <div id="orders-debug" class="tab-content">
                        <h3>订单API测试</h3>
                        <button class="btn" onclick="testOrdersListAPI()">订单列表</button>
                        <button class="btn" onclick="testAddressesAPI()">地址列表</button>
                        <div class="api-debug">
                            <div class="api-request" id="orders-debug-request"></div>
                            <div class="api-response" id="orders-debug-response"></div>
                        </div>
                    </div>
                </div>

                <!-- 个人信息页面 -->
                <div id="profile-section" class="content-section">
                    <h2 class="section-title">👤 个人信息</h2>
                    <div class="loading" id="profile-loading">加载中...</div>
                    <div class="error" id="profile-error"></div>
                    
                    <div class="form-group">
                        <label class="form-label">用户名:</label>
                        <input type="text" class="form-input" id="profile-username" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">邮箱:</label>
                        <input type="email" class="form-input" id="profile-email" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">手机号:</label>
                        <input type="text" class="form-input" id="profile-mobile" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">性别:</label>
                        <select class="form-select" id="profile-gender">
                            <option value="">请选择</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">年龄:</label>
                        <input type="number" class="form-input" id="profile-age">
                    </div>
                    
                    <button class="btn btn-success" onclick="updateProfile()">保存修改</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/dashboard_enhanced.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多对话聊天室</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 0;
            display: flex;
            height: 100vh;
            overflow: hidden; /* 防止整个页面滚动 */
        }
        #sidebar {
            width: 250px;
            border-right: 1px solid #ccc;
            padding: 15px;
            display: flex;
            flex-direction: column;
            background-color: #f8f9fa;
        }
        #sidebar h2 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        #user-input-area {
            margin-bottom: 15px;
        }
        #user-id-input {
            width: calc(100% - 22px); /* 减去 padding 和 border */
            padding: 8px;
            margin-bottom: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        #load-conversations-button, #new-conversation-button {
            width: 100%;
            padding: 8px;
            margin-bottom: 5px;
            cursor: pointer;
            border-radius: 3px;
        }
         #load-conversations-button {
             background-color: #007bff;
             color: white;
             border: none;
         }
         #new-conversation-button {
             background-color: #28a745;
             color: white;
             border: none;
         }
         #new-conversation-button:disabled, #load-conversations-button:disabled {
             background-color: #ccc;
             cursor: not-allowed;
         }

        #conversation-list {
            list-style: none;
            padding: 0;
            margin: 0;
            overflow-y: auto;
            flex-grow: 1; /* 占据剩余空间 */
        }
        #conversation-list li {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        #conversation-list li:hover {
            background-color: #e9ecef;
        }
        #conversation-list li.active {
            background-color: #d4edda;
            font-weight: bold;
        }
        #conversation-list .conv-title {
            display: block;
        }
        #conversation-list .conv-time {
            display: block;
            font-size: 0.8em;
            color: #6c757d;
        }

        #main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        #chatbox {
            flex-grow: 1;
            border: 1px solid #ccc;
            padding: 10px;
            overflow-y: auto;
            margin-bottom: 10px;
            display: flex;
            flex-direction: column-reverse; /* 新消息在底部 */
            background-color: #fff;
        }
        .message {
            margin-bottom: 8px;
            padding: 8px 12px;
            border-radius: 15px;
            max-width: 70%;
            word-wrap: break-word;
        }
        .user-message {
            background-color: #dcf8c6;
            align-self: flex-end;
            border-bottom-right-radius: 5px;
        }
        .ai-message {
            background-color: #f1f0f0;
            align-self: flex-start;
            border-bottom-left-radius: 5px;
        }
         .message .timestamp {
            font-size: 0.7em;
            color: #888;
            display: block;
            text-align: right;
            margin-top: 4px;
        }
        #input-area {
            display: flex;
        }
        #message-input {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        #send-button {
            padding: 10px 15px;
            margin-left: 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        #send-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
         #loading, #error-message {
            text-align: center;
            padding: 10px;
            color: #888;
            font-style: italic;
         }
         #loading { display: none; } /* 初始隐藏 */
         #error-message { color: red; display: none; }
         #chat-placeholder {
             display: flex;
             justify-content: center;
             align-items: center;
             height: 100%;
             color: #aaa;
             font-size: 1.2em;
         }
    </style>
</head>
<body>

    <div id="sidebar">
        <h2>对话列表</h2>
        <div id="user-input-area">
            <label for="user-id-input">用户 ID:</label>
            <input type="text" id="user-id-input" placeholder="输入测试用户 ID">
            <button id="load-conversations-button">加载/刷新对话</button>
            <button id="new-conversation-button" disabled>新建对话</button>
        </div>
        <div id="loading" style="text-align:left;">加载中...</div>
        <ul id="conversation-list">
            <!-- 对话列表将显示在这里 -->
        </ul>
    </div>

    <div id="main-content">
        <div id="chatbox">
            <div id="chat-placeholder">请在左侧选择或新建一个对话</div>
            <!-- 聊天消息将显示在这里 -->
        </div>
        <div id="error-message"></div>
        <div id="input-area">
            <input type="text" id="message-input" placeholder="选择对话后输入消息..." disabled>
            <button id="send-button" disabled>发送</button>
        </div>
    </div>

    <script>
        const chatbox = document.getElementById('chatbox');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const userIdInput = document.getElementById('user-id-input');
        const loadConvButton = document.getElementById('load-conversations-button');
        const newConvButton = document.getElementById('new-conversation-button');
        const conversationList = document.getElementById('conversation-list');
        const loadingDiv = document.getElementById('loading');
        const errorDiv = document.getElementById('error-message');
        const chatPlaceholder = document.getElementById('chat-placeholder');

        let currentUserId = '';
        let currentConversationId = null;
        let isAiResponding = false;

        // 获取认证token的函数
        function getAuthToken() {
            return localStorage.getItem('admin_token') || localStorage.getItem('token');
        }

        function displayChatMessage(messageData) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message');
            messageDiv.classList.add(messageData.is_user ? 'user-message' : 'ai-message');

            // 设置数据属性，用于后续查找和更新
            if (messageData.id) { // 真实ID from server
                messageDiv.dataset.id = messageData.id;
            } else if (messageData.temp_id) { // 临时ID for local preview
                messageDiv.dataset.tempId = messageData.temp_id;
            }

            const contentP = document.createElement('p');
            contentP.textContent = messageData.message;
            contentP.style.margin = '0';
            messageDiv.appendChild(contentP);

            if (messageData.timestamp) {
                 const timeSpan = document.createElement('span');
                 timeSpan.classList.add('timestamp');
                 timeSpan.textContent = new Date(messageData.timestamp).toLocaleString();
                 messageDiv.appendChild(timeSpan);
            }

            if (chatPlaceholder) chatPlaceholder.style.display = 'none';
            chatbox.insertBefore(messageDiv, chatbox.firstChild);
            chatbox.scrollTop = chatbox.scrollHeight;
            return messageDiv; // 返回创建的元素，以便 sendMessage 中引用
        }

        function showLoading(isLoading, area = 'sidebar') {
             const targetLoadingDiv = area === 'chat' ? document.getElementById('chat-loading') : loadingDiv; // 未来可以为聊天区加独立loading
             if(targetLoadingDiv) targetLoadingDiv.style.display = isLoading ? 'block' : 'none';
        }

        function showError(message, area = 'chat') {
            const targetErrorDiv = area === 'sidebar' ? document.getElementById('sidebar-error') : errorDiv; // 未来可以为侧边栏加独立error
             if(targetErrorDiv) {
                 targetErrorDiv.textContent = message;
                 targetErrorDiv.style.display = message ? 'block' : 'none';
             }
        }

        function renderConversationList(conversations) {
            conversationList.innerHTML = ''; // 清空列表
            if (conversations.length === 0) {
                const li = document.createElement('li');
                li.textContent = '没有找到对话';
                li.style.fontStyle = 'italic';
                li.style.color = '#888';
                conversationList.appendChild(li);
            } else {
                conversations.forEach(conv => {
                    const li = document.createElement('li');
                    li.dataset.conversationId = conv.id;
                    li.classList.toggle('active', conv.id === currentConversationId);

                    const titleSpan = document.createElement('span');
                    titleSpan.classList.add('conv-title');
                    titleSpan.textContent = conv.title || '无标题对话';
                    li.appendChild(titleSpan);

                    const timeSpan = document.createElement('span');
                    timeSpan.classList.add('conv-time');
                    timeSpan.textContent = `更新于: ${new Date(conv.updated_at).toLocaleString()}`;
                    li.appendChild(timeSpan);

                    li.addEventListener('click', () => loadChatHistory(conv.id));
                    conversationList.appendChild(li);
                });
            }
        }

        async function loadConversations() {
            currentUserId = userIdInput.value.trim();
            if (!currentUserId) {
                alert('请输入用户 ID'); // 简单的提示
                return;
            }

            showLoading(true, 'sidebar');
            showError(null, 'sidebar');
            loadConvButton.disabled = true;
            newConvButton.disabled = true;
            conversationList.innerHTML = ''; // 清空旧列表

            try {
                const response = await fetch(`/api/v1/chat/conversations?user_id=${currentUserId}`);
                if (!response.ok) {
                     const errorData = await response.json();
                     throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log("获取对话列表返回数据:", data); // 调试日志
                
                if (data.code === 0) {
                    renderConversationList(data.data);
                    userIdInput.disabled = true; // 加载成功后锁定用户ID
                    newConvButton.disabled = false; // 允许新建对话
                } else {
                    throw new Error(data.message || '加载对话列表失败');
                }
            } catch (error) {
                showError(`加载对话错误: ${error.message}`, 'sidebar');
                userIdInput.disabled = false; // 允许重试
            } finally {
                showLoading(false, 'sidebar');
                loadConvButton.disabled = false; // 允许刷新
            }
        }

        async function createNewConversation() {
            if (!currentUserId) return;

            newConvButton.disabled = true;
            showLoading(true, 'sidebar');

            try {
                // 修改请求方式，使用查询参数传递user_id，而不是请求体
                const response = await fetch(`/api/v1/chat/conversations?user_id=${currentUserId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                if (!response.ok) {
                     const errorData = await response.json();
                     throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log("创建对话返回数据:", data); // 调试日志
                
                // 创建成功后重新加载对话列表
                await loadConversations();
                
                // 获取新创建的对话ID
                const newConvId = data.id;
                if (newConvId) {
                    // 延迟一点确保列表渲染完成再加载历史
                    setTimeout(() => loadChatHistory(newConvId), 100);
                } else {
                    throw new Error('创建对话失败：返回数据格式不正确');
                }
            } catch (error) {
                 showError(`创建对话错误: ${error.message}`, 'sidebar');
                 console.error("创建对话错误:", error); // 调试日志
            } finally {
                 showLoading(false, 'sidebar');
                 newConvButton.disabled = false;
            }
        }

        async function loadChatHistory(conversationId) {
            if (!conversationId) return;
            
            currentConversationId = conversationId;

            // 更新列表中的选中状态
            document.querySelectorAll('#conversation-list li').forEach(li => {
                li.classList.toggle('active', li.dataset.conversationId === conversationId);
            });

            // 清空聊天框并显示加载状态
            chatbox.innerHTML = '';
            const loadingMsg = document.createElement('div');
            loadingMsg.id = 'chat-loading';
            loadingMsg.textContent = '加载历史记录中...';
            loadingMsg.style.textAlign = 'center';
            loadingMsg.style.color = '#888';
            chatbox.appendChild(loadingMsg);
            if (chatPlaceholder) chatPlaceholder.style.display = 'none';
            showError(null, 'chat');
            messageInput.disabled = true;
            sendButton.disabled = true;
            
            try {
                const response = await fetch(`/api/v1/chat/history/${conversationId}?user_id=${currentUserId}`);
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log("获取聊天历史返回数据:", data); // 调试日志
                
                // 移除加载提示
                const existingLoading = document.getElementById('chat-loading');
                if(existingLoading) chatbox.removeChild(existingLoading);
                
                if (data.code === 0) {
                    if (data.data.length === 0) {
                        if (chatPlaceholder) chatPlaceholder.style.display = 'flex';
                        chatPlaceholder.textContent = '开始对话吧！';
                    } else {
                        data.data.forEach(displayChatMessage);
                        if (chatPlaceholder) chatPlaceholder.style.display = 'none';
                    }
                    messageInput.disabled = false;
                    sendButton.disabled = false;
                    messageInput.focus();
                } else {
                    throw new Error(data.message || '加载历史记录失败');
                }
            } catch (error) {
                // 移除加载提示
                const existingLoading = document.getElementById('chat-loading');
                if(existingLoading) chatbox.removeChild(existingLoading);
                showError(`加载历史错误: ${error.message}`, 'chat');
                if (chatPlaceholder) chatPlaceholder.style.display = 'flex';
                chatPlaceholder.textContent = '加载历史失败';
            }
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || !currentConversationId) {
                return;
            }

            messageInput.disabled = true;
            sendButton.disabled = true;
            showError(null, 'chat');
            const currentMessageInputValue = messageInput.value; // 保存一下，因为马上要清空
            messageInput.value = '';

            let aiMessageDiv = null;
            let localUserMessageDiv = null; // 用于存储本地预览的用户消息DOM元素的引用

            // 1. 本地即时显示用户消息 (带临时ID)
            const tempUserMessageId = 'temp-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            const localUserMsgData = {
                message: currentMessageInputValue, // 使用保存的输入框值
                is_user: true,
                timestamp: new Date().toISOString(),
                temp_id: tempUserMessageId 
            };
            localUserMessageDiv = displayChatMessage(localUserMsgData);

            try {
                const response = await fetch('/api/v1/chat/send_stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: currentUserId,
                        conversation_id: currentConversationId,
                        message: currentMessageInputValue, // 使用原始值，后端会处理
                        task_type: null // 对于通用聊天，task_type 可以为 null 或不传
                    })
                });

                if (!response.ok) { 
                    const errorData = await response.json().catch(() => ({ message: `发送失败，状态码: ${response.status}` }));
                    throw new Error(errorData.message || `发送失败，状态码: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let streamingContent = "";
                let buffer = '';

                while (true) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;

                    // Process complete lines
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // Keep incomplete line in buffer

                    for (const line of lines) {
                        if (line.trim() && line.startsWith('data: ')) {
                            try {
                                const dataStr = line.substring(6).trim();
                                if (dataStr === '[DONE]') {
                                    if (aiMessageDiv) {
                                        const timeSpan = aiMessageDiv.querySelector('.timestamp');
                                        if (timeSpan && !timeSpan.textContent) {
                                            timeSpan.textContent = new Date().toLocaleString();
                                        }
                                    }
                                    messageInput.disabled = false;
                                    sendButton.disabled = false;
                                    messageInput.focus();
                                    return;
                                }

                                const data = JSON.parse(dataStr);
                                
                                if (data.type === 'ai_chunk') {
                                    const content = data.data?.content || '';
                                    streamingContent += content;
                                    
                                    if (!aiMessageDiv || !aiMessageDiv.parentNode) {
                                        aiMessageDiv = document.createElement('div');
                                        aiMessageDiv.classList.add('message', 'ai-message');
                                        
                                        const contentP = document.createElement('p');
                                        contentP.style.margin = '0';
                                        contentP.id = 'streaming-content-' + Date.now();
                                        aiMessageDiv.appendChild(contentP);
                                        
                                        const timeSpan = document.createElement('span');
                                        timeSpan.classList.add('timestamp');
                                        aiMessageDiv.appendChild(timeSpan);
                                        
                                        if (chatPlaceholder) chatPlaceholder.style.display = 'none';
                                        chatbox.insertBefore(aiMessageDiv, chatbox.firstChild);
                                    }
                                    
                                    const contentP = aiMessageDiv.querySelector('p');
                                    if (contentP) {
                                        // 实现打字机效果 - 立即更新文本内容
                                        contentP.textContent = streamingContent;
                                        
                                        // 强制重绘以确保字符立即显示
                                        contentP.offsetHeight;
                                        
                                        // 滚动到底部
                                        chatbox.scrollTop = chatbox.scrollHeight;
                                        
                                        // 添加光标效果
                                        contentP.style.borderRight = '2px solid #007bff';
                                        setTimeout(() => {
                                            if (contentP.style.borderRight) {
                                                contentP.style.borderRight = '';
                                            }
                                        }, 100);
                                    }
                                } else if (data.type === 'stream_end') {
                                    if (aiMessageDiv) {
                                        const timeSpan = aiMessageDiv.querySelector('.timestamp');
                                        if (timeSpan && !timeSpan.textContent) {
                                            timeSpan.textContent = new Date().toLocaleString();
                                        }
                                    }
                                    messageInput.disabled = false;
                                    sendButton.disabled = false;
                                    messageInput.focus();
                                    return;
                                } else if (data.type === 'error') {
                                    showError(`AI回复错误: ${data.data?.message || '未知错误'}`, 'chat');
                                    messageInput.disabled = false;
                                    sendButton.disabled = false;
                                    messageInput.focus();
                                    return;
                                }
                            } catch (e) {
                                console.error('解析流数据失败:', e, 'Line:', line);
                            }
                        }
                    }
                }
                

            } catch (error) {
                showError(`发送消息错误: ${error.message}`, 'chat');
                console.error("发送消息错误:", error); // 调试日志
                messageInput.disabled = false;
                sendButton.disabled = false;
                messageInput.value = currentMessageInputValue; // 恢复输入框内容
                messageInput.focus();
            }
        }

        // --- 事件绑定 ---
        loadConvButton.addEventListener('click', loadConversations);
        newConvButton.addEventListener('click', createNewConversation);
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !sendButton.disabled) {
                sendMessage();
            }
        });

        // --- 初始化状态 ---
        messageInput.disabled = true;
        sendButton.disabled = true;
        newConvButton.disabled = true;

        document.addEventListener('DOMContentLoaded', async function() {
            const urlParams = new URLSearchParams(window.location.search);
            currentUserId = urlParams.get('user_id');
            
            // 如果URL中没有user_id，尝试从token中获取
            if (!currentUserId) {
                const token = getAuthToken();
                if (token) {
                    try {
                        const response = await fetch('/api/v1/users/me', {
                            headers: {
                                'Authorization': `Bearer ${token}`
                            }
                        });
                        if (response.ok) {
                            const userData = await response.json();
                            if (userData && userData.data && userData.data.id) {
                                currentUserId = userData.data.id;
                                console.log('已从token中获取用户ID:', currentUserId);
                            }
                        }
                    } catch (error) {
                        console.error('获取用户信息出错:', error);
                    }
                }
                
                // 如果仍然没有用户ID，使用默认值或提示用户登录
                if (!currentUserId) {
                    currentUserId = 'guest_user_' + Math.random().toString(36).substring(2, 15);
                    console.log('使用临时访客ID:', currentUserId);
                    // 可以考虑显示登录提示
                    showError('您当前是访客模式，某些功能可能受限。<a href="/login" style="color:white;text-decoration:underline;">点击登录</a>', 'chat', 10000);
                }
            }
            
            console.log('当前用户ID:', currentUserId);
            
            // 初始化其他代码...
        });

    </script>

</body>
</html> 
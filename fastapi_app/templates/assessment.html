<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>体质评估与AI问诊</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { background-color: #f4f7f6; }
        .container { max-width: 1200px; }
        .report-section, .chat-section, .report-display-section {
            background-color: #fff;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        #chat-box {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #fafafa;
        }
        .user-message, .ai-message {
            padding: 0.5rem 1rem;
            border-radius: 15px;
            margin-bottom: 0.5rem;
            max-width: 80%;
            word-wrap: break-word;
        }
        .user-message { background-color: #e1f5fe; margin-left: auto; }
        .ai-message { background-color: #f1f8e9; }
        .choice-options button { margin-right: 10px; margin-bottom: 10px; }
        #report-display { display: none; }
        #report-chart-container { max-width: 500px; margin: auto; }
        .typing-indicator {
            display: inline-flex;
            align-items: center;
        }
        .typing-indicator span {
            height: 8px;
            width: 8px;
            margin: 0 1px;
            background-color: #888;
            border-radius: 50%;
            display: inline-block;
            animation: wave 1.5s infinite ease-in-out;
        }
        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }
        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }
        @keyframes wave {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-4px); }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">智能中医体质评估</h1>

        <!-- Section 1: User Info & Report List -->
        <div class="report-section" id="initial-view">
            <h2>第一步: 确认用户信息</h2>
            <div class="mb-3">
                <label for="user-id-input" class="form-label">用户ID (用于测试)</label>
                <input type="text" class="form-control" id="user-id-input" placeholder="请输入您的用户ID">
            </div>
            <button class="btn btn-primary" id="load-reports-btn">加载历史报告</button>
            <hr>
            <div id="report-list-container" style="display: none;">
                <h3>历史基础体质报告</h3>
                <div id="constitution-reports-list" class="list-group mb-3">
                    <!-- Historical reports will be listed here -->
                </div>
                <button class="btn btn-success" id="new-constitution-report-btn">开始新的体质评估</button>
            </div>
             <div id="complaint-section" style="display: none;">
                <h3>发起综合主诉评估</h3>
                <p>请选择一份基础体质报告，以便开始综合主诉评估。</p>
                <button class="btn btn-info" id="start-complaint-report-btn" disabled>基于所选报告进行主诉评估</button>
            </div>
        </div>

        <!-- Section 2: AI Chat Interface -->
        <div class="chat-section" id="chat-view" style="display: none;">
            <h2 id="chat-title">AI评估进行中...</h2>
            <div id="chat-box">
                <!-- Chat messages will appear here -->
            </div>
            <div id="choice-options" class="mb-3">
                <!-- AI provided choices will appear here -->
            </div>
            <div class="input-group">
                <input type="text" class="form-control" id="chat-input" placeholder="请输入您的回答...">
                <button class="btn btn-primary" id="send-chat-btn">发送</button>
            </div>
        </div>

        <!-- Section 3: Report Display -->
        <div class="report-display-section" id="report-display">
            <h2 id="report-title">评估报告</h2>
            <div id="report-chart-container">
                <canvas id="report-radar-chart"></canvas>
            </div>
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">体质分析</div>
                        <div class="card-body" id="report-analysis"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">健康建议</div>
                        <div class="card-body" id="report-recommendations"></div>
                    </div>
                </div>
            </div>
            <pre id="report-json" class="mt-4 bg-light p-3 rounded" style="display: none;"></pre>
            <button class="btn btn-secondary mt-3" id="return-to-start-btn">返回</button>
        </div>
    </div>

<script>
// --- DOM Elements ---
const initialView = document.getElementById('initial-view');
const chatView = document.getElementById('chat-view');
const reportDisplay = document.getElementById('report-display');
const userIdInput = document.getElementById('user-id-input');
const loadReportsBtn = document.getElementById('load-reports-btn');
const reportListContainer = document.getElementById('report-list-container');
const constitutionReportsList = document.getElementById('constitution-reports-list');
const newConstitutionReportBtn = document.getElementById('new-constitution-report-btn');
const complaintSection = document.getElementById('complaint-section');
const startComplaintReportBtn = document.getElementById('start-complaint-report-btn');

// Chat View Elements
const chatTitle = document.getElementById('chat-title');
const chatBox = document.getElementById('chat-box');
const choiceOptions = document.getElementById('choice-options');
const chatInput = document.getElementById('chat-input');
const sendChatBtn = document.getElementById('send-chat-btn');

// Report Display Elements
const reportTitle = document.getElementById('report-title');
const reportJson = document.getElementById('report-json');
const reportAnalysis = document.getElementById('report-analysis');
const reportRecommendations = document.getElementById('report-recommendations');
const returnToStartBtn = document.getElementById('return-to-start-btn');
let reportChart = null;

// --- State ---
let currentUserId = '';
let currentConversationId = '';
let selectedBaseReportId = '';
let currentTaskType = 'constitution';
let isAiResponding = false;
let aiTypingIndicator = null;

// 如果URL中有用户ID参数，自动填入
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const userId = urlParams.get('user_id');
    if (userId) {
        userIdInput.value = userId;
    }
});

// --- 加载Token ---
function getAuthToken() {
    return localStorage.getItem('token') || sessionStorage.getItem('token');
}

// --- API封装 ---
const API = {
    async getReports(userId) {
        const url = `/api/v1/reports/constitution/list?user_id=${userId}`;
        const token = getAuthToken();
        const response = await fetch(url, {
            headers: token ? { 'Authorization': `Bearer ${token}` } : {}
        });
        return await response.json();
    },
    
    async startConstitutionAssessment(userId) {
        const url = `/api/v1/reports/constitution/start`;
        const token = getAuthToken();
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            },
            body: JSON.stringify({ user_id: userId })
        });
        return await response.json();
    },
    
    async startComplaintAssessment(userId, baseReportId) {
        const url = `/api/v1/reports/complaint/start`;
        const token = getAuthToken();
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            },
            body: JSON.stringify({ 
                user_id: userId,
                base_report_id: baseReportId
            })
        });
        return await response.json();
    },
    
    async getReportDetail(reportId, reportType = 'constitution') {
        const url = `/api/v1/reports/${reportType}/${reportId}`;
        const token = getAuthToken();
        const response = await fetch(url, {
            headers: token ? { 'Authorization': `Bearer ${token}` } : {}
        });
        return await response.json();
    }
};

// --- Logic ---

// Load existing constitution reports for a user
async function loadConstitutionReports() {
    currentUserId = userIdInput.value.trim();
    if (!currentUserId) {
        alert('请输入用户ID');
        return;
    }

    try {
        const result = await API.getReports(currentUserId);

        constitutionReportsList.innerHTML = ''; // Clear list
        if (result.code === 0 || result.status === 'success') {
            if (result.data && result.data.length > 0) {
                result.data.forEach(report => {
                    const a = document.createElement('a');
                    a.href = '#';
                    a.className = 'list-group-item list-group-item-action';
                    a.dataset.reportId = report.id;
                    a.textContent = `${report.title} - ${new Date(report.created_at).toLocaleString()}`;
                    a.onclick = () => selectReport(report.id);
                    constitutionReportsList.appendChild(a);
                });
                complaintSection.style.display = 'block';
            } else {
                // No reports found, prompt to start a new one.
                 const p = document.createElement('p');
                 p.textContent = '暂无历史报告，请点击下方按钮开始新的评估。';
                 constitutionReportsList.appendChild(p);
            }
            reportListContainer.style.display = 'block';
        } else {
            throw new Error(result.message || '加载报告失败');
        }
    } catch (error) {
        alert(`错误: ${error.message}`);
        console.error('Error loading reports:', error);
    }
}

// Handle report selection from the list
function selectReport(reportId) {
    selectedBaseReportId = reportId;
    // Highlight selected item
    document.querySelectorAll('#constitution-reports-list a').forEach(el => {
        el.classList.toggle('active', el.dataset.reportId === reportId);
    });
    startComplaintReportBtn.disabled = false;
}

// Start a new assessment (either constitution or complaint)
function startNewAssessment(conversationId, initialMessage, taskType = 'constitution') {
    currentConversationId = conversationId;
    currentTaskType = taskType;
    initialView.style.display = 'none';
    chatView.style.display = 'block';
    reportDisplay.style.display = 'none';
    chatTitle.textContent = taskType === 'constitution' ? '体质评估进行中...' : '综合主诉评估中...';
    chatBox.innerHTML = ''; // Clear chatbox
    
    if(initialMessage) {
        appendMessage(initialMessage, 'ai-message');
    }
}

// Append a message to the chat box
function appendMessage(text, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = type;
    messageDiv.textContent = text;
    chatBox.appendChild(messageDiv);
    chatBox.scrollTop = chatBox.scrollHeight;
}

// Show AI typing indicator
function showTypingIndicator() {
    if (aiTypingIndicator) return;
    
    aiTypingIndicator = document.createElement('div');
    aiTypingIndicator.className = 'ai-message typing-indicator';
    aiTypingIndicator.innerHTML = '<span></span><span></span><span></span>';
    chatBox.appendChild(aiTypingIndicator);
    chatBox.scrollTop = chatBox.scrollHeight;
}

// Hide AI typing indicator
function hideTypingIndicator() {
    if (aiTypingIndicator) {
        aiTypingIndicator.remove();
        aiTypingIndicator = null;
    }
}

// Send chat message to backend with streaming response
async function sendChatMessage() {
    const message = chatInput.value.trim();
    if (!message || isAiResponding) return;
    
    appendMessage(message, 'user-message');
    chatInput.value = '';
    
    isAiResponding = true;
    showTypingIndicator();
    
    // 处理流式响应
    const url = '/api/v1/chat/send_stream';
    const token = getAuthToken();
    
    const body = {
        message: message,
        conversation_id: currentConversationId,
        user_id: currentUserId,
        task_type: currentTaskType === 'constitution' ? 'constitution_assessment' : 'chief_complaint'
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            },
            body: JSON.stringify(body)
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || '发送消息失败');
        }
        
        const reader = response.body.getReader();
        let aiResponse = '';
        let responseDiv = null;
        
        hideTypingIndicator();
        
        while (true) {
            const { value, done } = await reader.read();
            if (done) break;
            
            // 解码接收到的数据块
            const chunk = new TextDecoder().decode(value);
            
            // 检查是否包含特殊标记，表示评估完成
            if (chunk.includes('__ASSESSMENT_COMPLETE__')) {
                // 提取报告ID并显示报告
                const match = chunk.match(/__REPORT_ID:(.+?)__/);
                if (match && match[1]) {
                    const reportId = match[1].trim();
                    loadAndDisplayReport(reportId);
                }
                break;
            }
            
            // 追加到响应文本
            aiResponse += chunk;
            
            // 创建或更新响应元素
            if (!responseDiv) {
                responseDiv = document.createElement('div');
                responseDiv.className = 'ai-message';
                chatBox.appendChild(responseDiv);
            }
            
            responseDiv.textContent = aiResponse;
            chatBox.scrollTop = chatBox.scrollHeight;
        }
    } catch (error) {
        hideTypingIndicator();
        appendMessage(`发送失败: ${error.message}`, 'ai-message');
        console.error('Error sending message:', error);
    } finally {
        isAiResponding = false;
    }
}

// 加载并展示报告
async function loadAndDisplayReport(reportId, reportType = 'constitution') {
    try {
        const result = await API.getReportDetail(reportId, reportType);
        
        if (result.code === 0 || result.status === 'success') {
            const reportData = result.data;
            displayReport(reportData);
        } else {
            throw new Error(result.message || '加载报告失败');
        }
    } catch (error) {
        alert(`加载报告失败: ${error.message}`);
        console.error('Error loading report:', error);
    }
}

// Display the final report
function displayReport(reportData) {
    chatView.style.display = 'none';
    reportDisplay.style.display = 'block';

    reportTitle.textContent = reportData.title || '评估报告';
    
    // 保存原始JSON以便调试
    reportJson.textContent = JSON.stringify(reportData, null, 2);
    
    // 填充分析和建议
    reportAnalysis.innerHTML = '';
    if (reportData.analysis) {
        reportAnalysis.innerHTML = reportData.analysis.replace(/\n/g, '<br>');
    } else if (reportData.constitution_type) {
        reportAnalysis.innerHTML = `<p><strong>主要体质类型:</strong> ${reportData.constitution_type}</p>
                                   <p><strong>得分:</strong> ${reportData.score || 'N/A'}</p>`;
    }
    
    reportRecommendations.innerHTML = '';
    if (reportData.recommendations && Array.isArray(reportData.recommendations)) {
        const ul = document.createElement('ul');
        reportData.recommendations.forEach(rec => {
            const li = document.createElement('li');
            li.textContent = rec;
            ul.appendChild(li);
        });
        reportRecommendations.appendChild(ul);
    } else if (reportData.recommendation) {
        reportRecommendations.innerHTML = reportData.recommendation.replace(/\n/g, '<br>');
    }

    // Render radar chart if data available
    if (reportData.constitution_radar || reportData.radar_data) {
        const radarData = reportData.constitution_radar || reportData.radar_data;
        const ctx = document.getElementById('report-radar-chart').getContext('2d');
        
        if (reportChart) {
            reportChart.destroy();
        }
        
        reportChart = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: Object.keys(radarData),
                datasets: [{
                    label: '体质得分',
                    data: Object.values(radarData),
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                scales: {
                    r: {
                        angleLines: { display: true },
                        suggestedMin: 0,
                        suggestedMax: 100
                    }
                }
            }
        });
    }
}


// --- Event Listeners ---
loadReportsBtn.addEventListener('click', loadConstitutionReports);
sendChatBtn.addEventListener('click', sendChatMessage);
chatInput.addEventListener('keypress', e => e.key === 'Enter' && sendChatMessage());

newConstitutionReportBtn.addEventListener('click', async () => {
    try {
        const result = await API.startConstitutionAssessment(currentUserId);
        
        if (result.code === 0 || result.status === 'success') {
            const { conversation_id, initial_message } = result.data;
            startNewAssessment(conversation_id, initial_message, 'constitution');
        } else {
            throw new Error(result.message || '启动体质评估失败');
        }
    } catch (error) {
        alert(`错误: ${error.message}`);
        console.error('Error starting assessment:', error);
    }
});

startComplaintReportBtn.addEventListener('click', async () => {
    if (!selectedBaseReportId) {
        alert('请先选择一份基础报告');
        return;
    }
    
    try {
        const result = await API.startComplaintAssessment(currentUserId, selectedBaseReportId);
        
        if (result.code === 0 || result.status === 'success') {
            const { conversation_id, initial_message } = result.data;
            startNewAssessment(conversation_id, initial_message, 'complaint');
        } else {
            throw new Error(result.message || '启动主诉评估失败');
        }
    } catch (error) {
        alert(`错误: ${error.message}`);
        console.error('Error starting complaint assessment:', error);
    }
});

returnToStartBtn.addEventListener('click', () => {
    reportDisplay.style.display = 'none';
    initialView.style.display = 'block';
});
</script>

</body>
</html> 
import uuid
from sqlalchemy import Column, String, Integer, Float, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
from typing import Optional, List, Dict, Any
from fastapi_app.core.database import Base

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class Recipe(Base):
    """茶品配方模型"""
    __tablename__ = 'recipes'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)  # 配方名称
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # 配方描述
    intro: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # 配方介绍
    image: Mapped[Optional[str]] = mapped_column(String(256), nullable=True)  # 配方图片
    grams: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 克数
    
    # 关联的商品ID
    goods_id: Mapped[Optional[str]] = mapped_column(String(32), ForeignKey('goods.id'), nullable=True, index=True)
    
    # 配方状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    
    create_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    update_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联
    goods: Mapped[Optional["Goods"]] = relationship("Goods", back_populates="recipes")
    recipe_ingredients: Mapped[List["RecipeIngredient"]] = relationship("RecipeIngredient", back_populates="recipe", cascade="all, delete-orphan")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'intro': self.intro,
            'image': self.image,
            'grams': self.grams,
            'goods_id': self.goods_id,
            'is_active': self.is_active,
            'ingredients': [ingredient.to_dict() for ingredient in self.recipe_ingredients] if self.recipe_ingredients else [],
            'created_at': self.create_time.isoformat() if self.create_time else None,
            'updated_at': self.update_time.isoformat() if self.update_time else None
        }

class HerbIngredient(Base):
    """草药成分模型"""
    __tablename__ = 'herb_ingredients'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)  # 草药名称
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # 草药描述
    intro: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # 草药介绍
    image: Mapped[Optional[str]] = mapped_column(String(256), nullable=True)  # 草药图片
    flavor: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # 味道
    nature: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # 性质
    
    # 草药状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    
    create_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    update_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联
    recipe_ingredients: Mapped[List["RecipeIngredient"]] = relationship("RecipeIngredient", back_populates="herb_ingredient", cascade="all, delete-orphan")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'intro': self.intro,
            'image': self.image,
            'flavor': self.flavor,
            'nature': self.nature,
            'is_active': self.is_active,
            'created_at': self.create_time.isoformat() if self.create_time else None,
            'updated_at': self.update_time.isoformat() if self.update_time else None
        }

class RecipeIngredient(Base):
    """配方-草药关联模型"""
    __tablename__ = 'recipe_ingredients'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    recipe_id: Mapped[str] = mapped_column(String(32), ForeignKey('recipes.id'), nullable=False, index=True)
    herb_ingredient_id: Mapped[str] = mapped_column(String(32), ForeignKey('herb_ingredients.id'), nullable=False, index=True)
    
    # 用量信息
    amount: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # 用量
    unit: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)  # 单位
    
    create_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    update_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联
    recipe: Mapped["Recipe"] = relationship("Recipe", back_populates="recipe_ingredients")
    herb_ingredient: Mapped["HerbIngredient"] = relationship("HerbIngredient", back_populates="recipe_ingredients")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'recipe_id': self.recipe_id,
            'herb_ingredient_id': self.herb_ingredient_id,
            'amount': self.amount,
            'unit': self.unit,
            'herb_ingredient': self.herb_ingredient.to_dict() if self.herb_ingredient else None,
            'created_at': self.create_time.isoformat() if self.create_time else None,
            'updated_at': self.update_time.isoformat() if self.update_time else None
        }

import uuid
from datetime import datetime
from sqlalchemy import String, DateT<PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from fastapi_app.core.database import Base
from typing import List
# This import is tricky because of circular dependencies.
# We will resolve it later if needed by moving relationships
# to a separate file or using string type hints.
# from .user import User 

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class Address(Base):
    __tablename__ = 'addresses'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    user_id: Mapped[str] = mapped_column(String(32), ForeignKey('users.id', ondelete='CASCADE'), index=True)
    name: Mapped[str] = mapped_column(String(64), nullable=False)
    mobile: Mapped[str] = mapped_column(String(20), nullable=False)
    province: Mapped[str] = mapped_column(String(64), nullable=False)
    city: Mapped[str] = mapped_column(String(64), nullable=False)
    district: Mapped[str] = mapped_column(String(64), nullable=False)
    detail: Mapped[str] = mapped_column(String(256), nullable=False)
    is_default: Mapped[bool] = mapped_column(Boolean, default=False)
    create_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    update_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, onupdate=datetime.now)

    user: Mapped["User"] = relationship(back_populates="addresses") 
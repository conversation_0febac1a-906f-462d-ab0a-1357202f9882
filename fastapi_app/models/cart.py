import uuid
from datetime import datetime
from sqlalchemy import String, DateTime, Boolean, Float, Integer, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from fastapi_app.core.database import Base
from typing import Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from .user import User

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class CartItem(Base):
    __tablename__ = 'cart_items'
    
    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    user_id: Mapped[str] = mapped_column(String(32), ForeignKey('users.id', ondelete='CASCADE'), index=True)
    goods_id: Mapped[str] = mapped_column(String(32), index=True)
    sku_id: Mapped[Optional[str]] = mapped_column(String(32), nullable=True)
    name: Mapped[str] = mapped_column(String(128))
    price: Mapped[float] = mapped_column(Float)
    count: Mapped[int] = mapped_column(Integer, default=1)
    quantity: Mapped[int] = mapped_column(Integer, default=1)  # 兼容k_api的quantity字段
    image: Mapped[Optional[str]] = mapped_column(String(256), nullable=True)
    checked: Mapped[bool] = mapped_column(Boolean, default=True)
    selected: Mapped[bool] = mapped_column(Boolean, default=True)  # 兼容k_api的selected字段
    product_type: Mapped[int] = mapped_column(Integer, default=0)  # 商品类型：0=普通茶品，1=定制茶品

    # VIP定制和包装信息
    packaging: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # 包装信息
    is_vip: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否VIP定制
    customization_note: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)  # 定制备注

    create_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    update_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, onupdate=datetime.now)

    user: Mapped["User"] = relationship(back_populates="cart_items")

    def __setattr__(self, name, value):
        """同步 count 和 quantity 字段"""
        super().__setattr__(name, value)
        if name == 'count':
            super().__setattr__('quantity', value)
        elif name == 'quantity':
            super().__setattr__('count', value)
import uuid
from datetime import datetime
from sqlalchemy import String, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from fastapi_app.core.database import Base
from typing import List, TYPE_CHECKING, Dict, Any

if TYPE_CHECKING:
    from .user import User
    from .chat import ChatMessage

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

class Conversation(Base):
    __tablename__ = 'conversations'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    user_id: Mapped[str] = mapped_column(String(32), ForeignKey('users.id', ondelete='CASCADE'), index=True, nullable=False)
    title: Mapped[str] = mapped_column(String(128), default="新对话")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user: Mapped["User"] = relationship(back_populates="conversations")
    chat_messages: Mapped[List["ChatMessage"]] = relationship(back_populates="conversation", cascade="all, delete-orphan")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'created_at': int(self.created_at.timestamp() * 1000) if self.created_at else None,
            'updated_at': int(self.updated_at.timestamp() * 1000) if self.updated_at else None
        } 
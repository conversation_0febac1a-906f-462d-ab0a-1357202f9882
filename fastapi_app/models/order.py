import uuid
import random
from datetime import datetime
from sqlalchemy import String, DateTime, Float, Integer, Text, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from fastapi_app.core.database import Base
from typing import List
from .user import User
from .address import Address

def generate_uuid():
    return str(uuid.uuid4()).replace('-', '')[:32]

def generate_order_no():
    now = datetime.now()
    return now.strftime('%Y%m%d%H%M%S') + str(random.randint(100000, 999999))

class Order(Base):
    __tablename__ = 'orders'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    order_no: Mapped[str] = mapped_column(String(32), unique=True, default=generate_order_no)
    user_id: Mapped[str] = mapped_column(String(32), ForeignKey('users.id', ondelete='CASCADE'), index=True)
    address_id: Mapped[str] = mapped_column(String(32), <PERSON><PERSON>ey('addresses.id', ondelete='SET NULL'), nullable=True, index=True)
    status: Mapped[int] = mapped_column(Integer, default=0)
    total_price: Mapped[float] = mapped_column(Float, nullable=False)
    pay_price: Mapped[float] = mapped_column(Float, nullable=True, default=0)
    address_snapshot: Mapped[str] = mapped_column(Text, nullable=True)
    remark: Mapped[str] = mapped_column(String(256), nullable=True)
    create_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    pay_time: Mapped[datetime] = mapped_column(DateTime, nullable=True)
    ship_time: Mapped[datetime] = mapped_column(DateTime, nullable=True)
    complete_time: Mapped[datetime] = mapped_column(DateTime, nullable=True)
    cancel_time: Mapped[datetime] = mapped_column(DateTime, nullable=True)

    user: Mapped["User"] = relationship()
    address: Mapped["Address"] = relationship()
    order_goods: Mapped[List["OrderGoods"]] = relationship(back_populates="order", cascade="all, delete-orphan")

class OrderGoods(Base):
    __tablename__ = 'order_goods'

    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=generate_uuid)
    order_id: Mapped[str] = mapped_column(String(32), ForeignKey('orders.id', ondelete='CASCADE'), index=True)
    goods_id: Mapped[str] = mapped_column(String(32), index=True)
    sku_id: Mapped[str] = mapped_column(String(32), nullable=True)
    name: Mapped[str] = mapped_column(String(128), nullable=False)
    price: Mapped[float] = mapped_column(Float, nullable=False)
    count: Mapped[int] = mapped_column(Integer, nullable=False)
    image: Mapped[str] = mapped_column(String(256), nullable=True)
    sku_name: Mapped[str] = mapped_column(String(128), nullable=True)
    create_time: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)

    order: Mapped["Order"] = relationship(back_populates="order_goods") 
from pydantic import BaseModel, <PERSON>son, Field
from typing import Optional, Any, List, Union, Dict
from datetime import datetime

# --- Constitution Report Schemas ---

class ConstitutionReportBase(BaseModel):
    title: Optional[str] = None
    report_data: Any # Using Any to allow any valid JSON

class ConstitutionReportCreate(ConstitutionReportBase):
    pass

class ConstitutionReport(ConstitutionReportBase):
    id: str
    user_id: str
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True

    def model_post_init(self, __context):
        if self.created_at:
            self.created_at = int(self.created_at.timestamp() * 1000)
        if self.updated_at:
            self.updated_at = int(self.updated_at.timestamp() * 1000)

# --- Complaint Report Schemas ---

class ComplaintReportBase(BaseModel):
    title: str = "综合主诉评估报告"
    base_constitution_report_id: Optional[str] = None
    report_data: Any # Using Any to allow any valid JSON

class ComplaintReportCreate(ComplaintReportBase):
    pass

class ComplaintReport(ComplaintReportBase):
    id: str
    user_id: str
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True
        
    def model_post_init(self, __context):
        if self.created_at:
            self.created_at = int(self.created_at.timestamp() * 1000)
        if self.updated_at:
            self.updated_at = int(self.updated_at.timestamp() * 1000)

# --- General Report Schema for listing ---

class Report(BaseModel):
    id: str
    title: str
    type: str # 'constitution' or 'complaint'
    created_at: int 

class ReportBase(BaseModel):
    title: str
    type: str

class ReportInfo(BaseModel):
    id: str
    title: str
    created_at: datetime

class NewAssessmentData(BaseModel):
    conversation_id: str
    initial_ai_message: str

class ConstitutionReportListResponse(BaseModel):
    code: int
    message: str
    data: Union[List[ReportInfo], NewAssessmentData]

class ComplaintReportListResponse(BaseModel):
    code: int
    message: str
    data: List[ReportInfo]

class ConstitutionReportData(BaseModel):
    constitution: str
    score: int
    recommendations: List[str] 

# --- 新增评估相关响应模型 ---

class StartAssessmentData(BaseModel):
    conversation_id: str
    initial_message: str

class StartAssessmentResponse(BaseModel):
    code: int
    message: str
    data: StartAssessmentData

class ReportDetailData(BaseModel):
    id: str
    title: str
    created_at: int
    user_id: str
    base_constitution_report_id: Optional[str] = None
    constitution_type: Optional[str] = None
    score: Optional[int] = None
    constitution_radar: Optional[Dict[str, float]] = None
    radar_data: Optional[Dict[str, float]] = None
    analysis: Optional[str] = None
    recommendation: Optional[str] = None
    recommendations: Optional[List[str]] = None
    
    class Config:
        extra = "allow"  # 允许额外的字段

class ReportDetailResponse(BaseModel):
    code: int
    message: str
    data: ReportDetailData 
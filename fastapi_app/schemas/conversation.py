from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class ConversationBase(BaseModel):
    title: str

class ConversationCreate(ConversationBase):
    pass

class ConversationUpdate(BaseModel):
    title: Optional[str] = None

class Conversation(ConversationBase):
    id: str
    user_id: str
    created_at: int
    updated_at: int

    class Config:
        from_attributes = True
        
    def model_post_init(self, __context):
        # 确保created_at和updated_at是整数时间戳
        if hasattr(self, 'created_at') and self.created_at is not None:
            if isinstance(self.created_at, datetime):
                self.created_at = int(self.created_at.timestamp() * 1000)
            elif not isinstance(self.created_at, int):
                # 尝试转换为整数
                try:
                    self.created_at = int(self.created_at)
                except (ValueError, TypeError):
                    self.created_at = int(datetime.now().timestamp() * 1000)
                
        if hasattr(self, 'updated_at') and self.updated_at is not None:
            if isinstance(self.updated_at, datetime):
                self.updated_at = int(self.updated_at.timestamp() * 1000)
            elif not isinstance(self.updated_at, int):
                # 尝试转换为整数
                try:
                    self.updated_at = int(self.updated_at)
                except (ValueError, TypeError):
                    self.updated_at = int(datetime.now().timestamp() * 1000) 
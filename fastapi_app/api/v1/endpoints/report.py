from typing import List, Any, Union, Optional, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from fastapi_app.core.database import get_db
from fastapi_app.core.security import get_current_user, get_user_by_id_or_token
from fastapi_app.models.user import User as UserModel
from fastapi_app.models.report import ConstitutionReport
from fastapi_app.crud import report as crud_report, chat as crud_chat
from fastapi_app.schemas import report as schemas_report
from fastapi_app.services.ai_chat_service import load_prompt
import json
import logging
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/constitution/list", response_model=schemas_report.ConstitutionReportListResponse)
def get_user_constitution_reports(
    user_id: str,
    db: Session = Depends(get_db),
    # current_user: UserModel = Depends(get_current_user) # Temporarily disable auth for easy testing
):
    """
    Get a user's constitution reports.
    """
    # 验证用户是否存在，如果不存在则创建
    user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user:
        # 自动创建用户，与Flask版本保持一致
        from fastapi_app.models.user import User
        try:
            username = f"user_{user_id[-8:]}"
            email = f"{user_id}@example.com"
            user = User(id=user_id, username=username, email=email, password_hash="none")
            db.add(user)
            db.commit()
            logger.info(f"成功创建用户: {user_id}")
        except Exception as e:
            db.rollback()
            logger.error(f"创建用户失败: {e}")
            raise HTTPException(status_code=500, detail="创建用户失败")

    reports = crud_report.get_constitution_reports_by_user(db, user_id=user_id)

    if reports:
        # 转换为列表格式
        return {"code": 0, "message": "Success", "data": [report.to_list_dict() for report in reports]}
    else:
        # 没有报告，返回空列表，让前端引导用户使用问卷
        return {
            "code": 1, 
            "message": "暂无历史体质报告，请通过问卷评估创建新的报告。", 
            "data": []
        }

@router.get("/complaint/list", response_model=schemas_report.ComplaintReportListResponse)
def get_user_complaint_reports(
    user_id: str,
    db: Session = Depends(get_db),
    # current_user: UserModel = Depends(get_current_user) # Temporarily disable auth for easy testing
):
    """
    Get a user's complaint reports.
    """
    # 验证用户是否存在
    user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # 获取主诉报告列表
    constitution_reports, complaint_reports = crud_report.get_all_reports_by_user(db, user_id=user_id)
    
    # 返回主诉报告列表，转换为列表格式
    if complaint_reports:
        return {"code": 0, "message": "Success", "data": [report.to_list_dict() for report in complaint_reports]}
    else:
        return {"code": 1, "message": "暂无历史主诉评估报告。", "data": []}

@router.get("/constitution/{report_id}")
def get_constitution_report_detail(
    report_id: str,
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(lambda: None)  # 暂时禁用认证
):
    """
    获取体质报告详情 - 按照前端示例格式返回
    """
    try:
        # 获取报告
        report = crud_report.get_report_by_id(db, report_id=report_id, report_type="constitution")
        if not report:
            raise HTTPException(status_code=404, detail="体质报告不存在")

        # 解析报告数据
        try:
            report_data = json.loads(report.report_data_json)
        except json.JSONDecodeError:
            raise HTTPException(status_code=500, detail="报告数据格式错误")

        # 获取用户信息
        user = db.query(UserModel).filter(UserModel.id == report.user_id).first()
        username = user.username if user else "Unknown"

        # 构建符合前端格式的响应
        response_data = {
            "id": report.id,  # 使用原始UUID，保持与列表接口一致
            "userName": username,
            "date": report.created_at.strftime('%Y-%m-%d') if report.created_at else "",
            "score": report_data.get("score", 76),
            "overall": {
                "text": report_data.get("overall", {}).get("text", "您的体质评估已完成，以下是详细的体质解读和建议。"),
                "BCType": report_data.get("overall", {}).get("BCType", "平和质"),
                "mainSymptoms": report_data.get("overall", {}).get("mainSymptoms", "暂无明显症状")
            },
            "analysis": {
                "BCIndex": {
                    "qiXu": report_data.get("analysis", {}).get("BCIndex", {}).get("qiXu", 25),
                    "yangXu": report_data.get("analysis", {}).get("BCIndex", {}).get("yangXu", 30),
                    "yinXu": report_data.get("analysis", {}).get("BCIndex", {}).get("yinXu", 35),
                    "tanShi": report_data.get("analysis", {}).get("BCIndex", {}).get("tanShi", 40),
                    "shiRe": report_data.get("analysis", {}).get("BCIndex", {}).get("shiRe", 45),
                    "xueYu": report_data.get("analysis", {}).get("BCIndex", {}).get("xueYu", 30),
                    "qiYu": report_data.get("analysis", {}).get("BCIndex", {}).get("qiYu", 35),
                    "teBing": report_data.get("analysis", {}).get("BCIndex", {}).get("teBing", 20)
                },
                "mainBC": report_data.get("analysis", {}).get("mainBC", "平和质"),
                "secondaryBC": report_data.get("analysis", {}).get("secondaryBC", [])
            },
            "definition": {
                "definition": report_data.get("definition", {}).get("definition", "体质平和，身心健康。"),
                "bodyShape": report_data.get("definition", {}).get("bodyShape", "体型匀称。"),
                "commonSymptoms": report_data.get("definition", {}).get("commonSymptoms", "精力充沛，睡眠良好。"),
                "psychology": report_data.get("definition", {}).get("psychology", "性格开朗，情绪稳定。"),
                "diseases": report_data.get("definition", {}).get("diseases", "不易患病。"),
                "environment": report_data.get("definition", {}).get("environment", "对自然环境和社会环境适应能力较强。")
            },
            "dailyTips": {
                "term": report_data.get("dailyTips", {}).get("term", "立夏"),
                "intro": report_data.get("dailyTips", {}).get("intro", "根据您的体质特点，以下是为您定制的健康建议："),
                "food": report_data.get("dailyTips", {}).get("food", "饮食宜清淡，营养均衡。"),
                "sleep": report_data.get("dailyTips", {}).get("sleep", "保持规律作息，充足睡眠。"),
                "exercise": report_data.get("dailyTips", {}).get("exercise", "适量运动，增强体质。"),
                "mood": report_data.get("dailyTips", {}).get("mood", "保持心情愉悦，情绪稳定。")
            }
        }

        return {
            "code": 0,
            "message": "获取体质报告成功",
            "data": response_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取体质报告详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取体质报告详情失败")

@router.get("/constitution-list")
def get_all_constitution_reports(
    skip: int = 0,
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """
    获取所有体质报告列表 - 按照前端示例格式返回
    """
    try:
        # 获取所有体质报告（不限制用户）
        reports = db.query(ConstitutionReport).offset(skip).limit(limit).all()

        reports_data = []
        for report in reports:
            try:
                report_data = json.loads(report.report_data_json)
            except json.JSONDecodeError:
                continue

            # 获取用户信息
            user = db.query(UserModel).filter(UserModel.id == report.user_id).first()
            username = user.username if user else "Unknown"

            # 构建列表项格式
            report_item = {
                "id": report.id,  # 使用原始UUID，保持与详情接口一致
                "userName": username,
                "date": report.created_at.strftime('%Y-%m-%d') if report.created_at else "",
                "score": report_data.get("score", 76),
                "overall": {
                    "text": report_data.get("overall", {}).get("text", "体质评估已完成"),
                    "BCType": report_data.get("overall", {}).get("BCType", "平和质"),
                    "mainSymptoms": report_data.get("overall", {}).get("mainSymptoms", "暂无明显症状")
                }
            }
            reports_data.append(report_item)

        return {
            "code": 0,
            "message": "获取体质报告列表成功",
            "data": reports_data
        }

    except Exception as e:
        import traceback
        logger.error(f"获取体质报告列表失败: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"获取体质报告列表失败: {str(e)}")

# 该端点已废弃，体质评估现在使用问卷方式而非聊天方式

@router.post("/complaint/start")
async def start_complaint_assessment(
    *,
    user_id: str = Body(...),
    base_constitution_report_id: str = Body(...),
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(lambda: None)  # 暂时禁用认证，使用可选认证
):
    """
    Start a new complaint assessment conversation based on a constitution report.
    """
    try:
        # 验证用户是否存在，如果不存在则创建
        target_user_id = current_user.id if current_user else user_id
        user = db.query(UserModel).filter(UserModel.id == target_user_id).first()
        if not user:
            # 自动创建用户，与constitution list逻辑保持一致
            from fastapi_app.models.user import User
            try:
                username = f"user_{target_user_id[-8:]}"
                email = f"{target_user_id}@example.com"
                user = User(id=target_user_id, username=username, email=email, password_hash="none")
                db.add(user)
                db.commit()
                logger.info(f"成功创建用户: {target_user_id}")
            except Exception as e:
                db.rollback()
                logger.error(f"创建用户失败: {e}")
                raise HTTPException(status_code=500, detail="创建用户失败")
        
        # 验证基础报告是否存在
        base_report = crud_report.get_report_by_id(db, report_id=base_constitution_report_id, report_type="constitution")
        if not base_report:
            raise HTTPException(status_code=404, detail="Base constitution report not found")
        
        # 解析基础报告数据
        try:
            base_report_data = json.loads(base_report.report_data_json)
            logger.info(f"成功加载基础体质报告: {base_constitution_report_id}")
        except json.JSONDecodeError:
            base_report_data = {"error": "Invalid report data format"}
            logger.error(f"解析基础体质报告失败: {base_constitution_report_id}")
        
        # 创建新对话
        try:
            new_conversation = crud_chat.create_conversation(db, user_id=target_user_id, title="综合主诉评估")
            
            # 设置对话元数据
            metadata = {
                "base_constitution_report_id": base_constitution_report_id,
                "task_type": "chief_complaint"
            }
            new_conversation.conversation_metadata = json.dumps(metadata)
            db.commit()
            logger.info(f"成功创建主诉评估对话: {new_conversation.id}")
        except Exception as e:
            db.rollback()
            logger.error(f"创建对话失败: {e}")
            raise HTTPException(status_code=500, detail=f"创建对话失败: {str(e)}")
        
        # 加载主诉评估提示词
        try:
            initial_ai_message = load_prompt("综合主诉报告AI任务")
        except Exception as e:
            logger.error(f"Error loading prompt: {e}")
            initial_ai_message = "您好！根据您之前的体质评估结果，我将为您进行综合主诉评估。请描述您目前的主要不适或症状？"
        
        # 添加基础体质报告信息到初始消息中
        base_report_summary = "基于您之前的体质评估，"
        
        # 添加主要体质信息
        if base_report_data.get("dominant_constitution"):
            dom_const = base_report_data["dominant_constitution"]
            base_report_summary += f"您的主要体质是{dom_const.get('name', '未知')}（得分：{dom_const.get('score', 'N/A')}）。"
        
        # 添加体质分数信息
        if base_report_data.get("constitution_scores"):
            scores = base_report_data["constitution_scores"]
            base_report_summary += "各项体质得分为：" + ", ".join([f"{k}:{v}" for k, v in scores.items()]) + "。"
        
        # 添加用户选择的信息
        if base_report_data.get("user_selections"):
            user_sel = base_report_data["user_selections"]
            if user_sel.get("gender"):
                base_report_summary += f"您的性别是{user_sel.get('gender')}，"
            if user_sel.get("age_group"):
                base_report_summary += f"年龄段为{user_sel.get('age_group')}，"
            if user_sel.get("q12_priority_conditioning"):
                base_report_summary += f"您希望优先调理的方向是{user_sel.get('q12_priority_conditioning')}。"
        
        # 将基础报告摘要添加到AI初始消息中
        enhanced_ai_message = initial_ai_message + "\n\n" + base_report_summary + "\n\n请描述您目前的主要不适或症状？"
        
        # 将开场白存入数据库
        try:
            crud_chat.create_chat_message(db, user_id=target_user_id, conversation_id=new_conversation.id, message=enhanced_ai_message, is_user=False)
            logger.info(f"成功保存初始AI消息到对话: {new_conversation.id}")
        except Exception as e:
            logger.error(f"保存初始AI消息失败: {e}")
            # 不影响主流程，继续返回

        return {
            "code": 0, 
            "message": "已开启新的主诉评测会话", 
            "data": {
                "conversation_id": new_conversation.id,
                "initial_message": enhanced_ai_message
            }
        }
    except Exception as e:
        logger.error(f"主诉评估开始失败: {e}")
        raise HTTPException(status_code=500, detail=f"主诉评估开始失败: {str(e)}")

@router.get("/constitution/{report_id}")
def get_constitution_report_detail(
    report_id: str,
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(get_current_user)
):
    """
    Get details of a specific constitution report.
    """
    report = crud_report.get_report_by_id(db, report_id=report_id, report_type="constitution")
    if not report:
        raise HTTPException(status_code=404, detail="Report not found")
    
    # 如果有用户登录，检查权限
    if current_user and report.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this report")
    
    # 解析JSON数据
    try:
        report_data = json.loads(report.report_data_json)
    except json.JSONDecodeError:
        report_data = {"error": "Invalid report data format"}
    
    # 直接返回报告数据，与前端期望的格式匹配
    result = {
        "id": report.id,
        "title": report.title,
        "created_at": report.created_at.isoformat() if hasattr(report.created_at, 'isoformat') else report.created_at,
        "user_id": report.user_id,
        **report_data
    }
    
    return result

@router.get("/complaint/{report_id}")
def get_complaint_report_detail(
    report_id: str,
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(get_current_user)
):
    """
    Get details of a specific complaint report.
    """
    report = crud_report.get_report_by_id(db, report_id=report_id, report_type="complaint")
    if not report:
        raise HTTPException(status_code=404, detail="Report not found")
    
    # 如果有用户登录，检查权限
    if current_user and report.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this report")
    
    # 解析JSON数据
    try:
        report_data = json.loads(report.report_data_json)
    except json.JSONDecodeError:
        report_data = {"error": "Invalid report data format"}
    
    # 直接返回报告数据，与前端期望的格式匹配
    result = {
        "id": report.id,
        "title": report.title,
        "created_at": report.created_at.isoformat() if hasattr(report.created_at, 'isoformat') else report.created_at,
        "user_id": report.user_id,
        "base_constitution_report_id": getattr(report, "base_constitution_report_id", None),
        **report_data
    }
    
    return result

@router.get("", response_model=List[schemas_report.Report])
def get_all_reports(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Get all reports for the current user, sorted by creation date.
    """
    const_reports, comp_reports = crud_report.get_all_reports_by_user(db, user_id=current_user.id)
    
    all_reports = []
    for r in const_reports:
        all_reports.append(schemas_report.Report(id=r.id, title=r.title, type='constitution', created_at=int(r.created_at.timestamp() * 1000)))
        
    for r in comp_reports:
        all_reports.append(schemas_report.Report(id=r.id, title=r.title, type='complaint', created_at=int(r.created_at.timestamp() * 1000)))

    all_reports.sort(key=lambda x: x.created_at, reverse=True)
    return all_reports

# 该端点已被 /constitution/calculate 替代

@router.post("/constitution/calculate")
def calculate_constitution_report(
    *,
    report_in: Dict = Body(...),
    db: Session = Depends(get_db),
    current_user: Optional[UserModel] = Depends(get_current_user)
):
    """
    计算体质报告，基于问卷答案使用规则引擎计算体质分数
    """
    # 获取用户ID和答案
    user_id = report_in.get('user_id')
    
    # 从report_data中提取answers，兼容不同格式
    answers = report_in.get('answers')
    if not answers and 'report_data' in report_in:
        answers = report_in['report_data'].get('user_selections', {})
    
    if not user_id:
        if current_user:
            user_id = current_user.id
        else:
            raise HTTPException(status_code=400, detail="缺少用户 ID")
    
    if not answers:
        raise HTTPException(status_code=400, detail="缺少问卷答案")

    # 验证用户是否存在，如果不存在则创建
    user = db.query(UserModel).filter(UserModel.id == user_id).first()
    if not user:
        # 自动创建用户，与constitution list逻辑保持一致
        from fastapi_app.models.user import User
        try:
            username = f"user_{user_id[-8:]}"
            email = f"{user_id}@example.com"
            user = User(id=user_id, username=username, email=email, password_hash="none")
            db.add(user)
            db.commit()
            logger.info(f"成功创建用户: {user_id}")
        except Exception as e:
            db.rollback()
            logger.error(f"创建用户失败: {e}")
            raise HTTPException(status_code=500, detail="创建用户失败")

    # 初始化体质分数
    scores = {
        "qi_deficiency": 0, "yang_deficiency": 0, "yin_deficiency": 0,
        "phlegm_dampness": 0, "damp_heat": 0, "blood_stasis": 0,
        "qi_stagnation": 0, "special_constitution": 0, "peaceful_constitution": 0
    }

    # 提取基础信息
    gender = answers.get('gender')
    age_group = answers.get('age_group')

    # 计分逻辑 - 与Flask版本完全一致
    # 问题3: 核心症状筛选 (单选)
    q3_symptom = answers.get('q3_core_symptom')
    q3_map = {
        "疲劳乏力，容易感冒": "qi_deficiency", "怕冷，手脚冰凉": "yang_deficiency",
        "口干咽燥，手足心热": "yin_deficiency", "身体困重，面部油腻": "phlegm_dampness",
        "口苦口臭，皮肤长痘": "damp_heat", "面色暗沉，局部刺痛": "blood_stasis",
        "胸闷胁痛，情绪抑郁": "qi_stagnation", "过敏频发，皮肤划痕": "special_constitution",
        "无明显不适": "peaceful_constitution"
    }
    if q3_symptom and q3_map.get(q3_symptom):
        scores[q3_map[q3_symptom]] += 2

    # 问题4: 是否容易出汗 (多选)
    q4_sweating = answers.get('q4_sweating', [])
    for item in q4_sweating:
        if item == "白天动则大汗": scores["qi_deficiency"] += 1
        elif item == "夜间盗汗明显": scores["yin_deficiency"] += 1
        elif item == "头部/胸口多汗": scores["damp_heat"] += 1
        elif item == "几乎不出汗":
            scores["phlegm_dampness"] += 1
            scores["yang_deficiency"] += 1
        elif item == "无明显异常": scores["peaceful_constitution"] += 1
            
    # 问题5: 大便状态 (单选)
    q5_stool = answers.get('q5_stool')
    if q5_stool == "黏滞不爽，冲不干净":
        scores["phlegm_dampness"] += 1
        scores["damp_heat"] += 1
    elif q5_stool == "干燥硬结，如羊粪球":
        scores["yin_deficiency"] += 1
        scores["blood_stasis"] += 1
    elif q5_stool == "稀溏不成形":
        scores["qi_deficiency"] += 1
        scores["yang_deficiency"] += 1
    elif q5_stool == "先干后溏，时好时坏": scores["qi_deficiency"] += 1
    elif q5_stool == "规律正常": scores["peaceful_constitution"] += 1

    # 问题6: 睡眠状态 (单选)
    q6_sleep = answers.get('q6_sleep')
    if q6_sleep == "入睡困难、多梦易醒":
        scores["qi_stagnation"] += 1
        scores["blood_stasis"] += 1
    elif q6_sleep == "失眠潮热、盗汗": scores["yin_deficiency"] += 1
    elif q6_sleep == "嗜睡乏力，睡不醒":
        scores["phlegm_dampness"] += 1
        scores["qi_deficiency"] += 1
    elif q6_sleep == "夜尿频繁，起夜≥2次": scores["yang_deficiency"] += 1
    elif q6_sleep == "睡眠正常": scores["peaceful_constitution"] += 1

    # 问题7: 对季节变化的反应 (多选)
    q7_season = answers.get('q7_season_response', [])
    for item in q7_season:
        if item == "冬季怕冷，穿再多也冷": scores["yang_deficiency"] += 1
        elif item == "夏季上火，烦躁长痘":
            scores["yin_deficiency"] += 1
            scores["damp_heat"] += 1
        elif item == "梅雨季头重如裹": scores["phlegm_dampness"] += 1
        elif item == "春秋季鼻痒打喷嚏": scores["special_constitution"] += 1
        elif item == "四季适应良好": scores["peaceful_constitution"] += 1

    # 问题8: 饮食偏好 (单选)
    q8_diet = answers.get('q8_diet_preference')
    if q8_diet == "油腻辛辣": scores["damp_heat"] += 1
    elif q8_diet == "生冷寒凉": scores["yang_deficiency"] += 1
    elif q8_diet == "甜食糕点": scores["phlegm_dampness"] += 1
    elif q8_diet == "重咸腌制": scores["blood_stasis"] += 1
    elif q8_diet == "规律清淡": scores["peaceful_constitution"] += 1

    # 问题9: 长期习惯 (多选)
    q9_habits = answers.get('q9_long_term_habits', [])
    for item in q9_habits:
        if item == "熬夜（23点后睡）": scores["yin_deficiency"] += 1
        elif item == "久坐（每天≥6小时）": scores["phlegm_dampness"] += 1
        elif item == "高压焦虑（常感紧张）": scores["qi_stagnation"] += 1
        elif item == "过度运动（易耗气）": scores["qi_deficiency"] += 1
        elif item == "作息规律": scores["peaceful_constitution"] += 1
    
    # 问题10: 体型倾向 (单选)
    q10_body = answers.get('q10_body_type')
    if q10_body == "肥胖，肉松软": scores["phlegm_dampness"] += 1
    elif q10_body == "消瘦，肌肉薄弱":
        scores["yin_deficiency"] += 1
        scores["qi_deficiency"] += 1
    elif q10_body == "正常但局部胖": scores["phlegm_dampness"] += 1
    elif q10_body == "肌肉发达，体脂低": scores["peaceful_constitution"] += 1
    elif q10_body == "体重波动大":
        scores["qi_stagnation"] += 1
        scores["phlegm_dampness"] += 1

    # 问题11: 皮肤状态 (单选)
    q11_skin = answers.get('q11_skin_status')
    if q11_skin == "油腻长痘，毛孔粗大": scores["damp_heat"] += 1
    elif q11_skin == "干燥脱屑，易生皱纹": scores["yin_deficiency"] += 1
    elif q11_skin == "暗沉色斑，黑眼圈": scores["blood_stasis"] += 1
    elif q11_skin == "敏感红痒，易起疹": scores["special_constitution"] += 1
    elif q11_skin == "光滑红润": scores["peaceful_constitution"] += 1

    # 问题12: 优先调理方向 (单选)
    q12_priority = answers.get('q12_priority_conditioning')

    # 确定主要体质和次要体质
    dominant_constitution = {"name": "未知", "score": -1}
    secondary_constitutions = []
    
    if scores:
        sorted_scores = sorted(scores.items(), key=lambda item: item[1], reverse=True)
        
        if sorted_scores:
            dominant_constitution["name"] = sorted_scores[0][0]
            dominant_constitution["score"] = sorted_scores[0][1]

            for name, score_val in sorted_scores[1:]:
                if score_val > 0:
                    if name == "peaceful_constitution" and dominant_constitution["name"] != "peaceful_constitution" and dominant_constitution["score"] > score_val:
                        continue
                    secondary_constitutions.append({"name": name, "score": score_val})
                if len(secondary_constitutions) >= 2:
                    break
        
        if dominant_constitution["name"] == "peaceful_constitution":
            secondary_constitutions = []

    # 构建报告数据
    report_data = {
        "report_type": "constitution_assessment_rules_v1",
        "user_info": {"gender": gender, "age_group": age_group},
        "user_selections": answers,
        "constitution_scores": scores,
        "dominant_constitution": dominant_constitution,
        "secondary_constitutions": secondary_constitutions,
        "priority_conditioning_selection": q12_priority,
        "assessment_date": datetime.utcnow().isoformat() + "Z",
        "version": "1.0.0"
    }
    
    try:
        report_title = f"{datetime.utcnow().strftime('%Y-%m-%d')} 按规则生成的体质报告"
        new_report = crud_report.create_constitution_report(
            db=db, 
            user_id=user_id, 
            report_data={"title": report_title, **report_data}
        )
        
        return {
            "id": new_report.id,
            "message": "基础体质报告计算并保存成功",
            **report_data
        }
    except Exception as e:
        logger.error(f"Error saving calculated constitution report: {e}")
        raise HTTPException(status_code=500, detail=f"保存报告失败: {str(e)}")

@router.post("/complaint", response_model=schemas_report.ComplaintReport)
def create_complaint_report(
    *,
    report_in: schemas_report.ComplaintReportCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Create a new complaint report.
    """
    return crud_report.create_complaint_report(db=db, user_id=current_user.id, report=report_in) 
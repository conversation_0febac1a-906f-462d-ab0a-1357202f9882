from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import fastapi_app.schemas.user as schemas
from fastapi_app.core.database import get_db
from fastapi_app.core.security import get_current_user
from fastapi_app.models.user import User as UserModel
from fastapi_app.crud import user as crud_user
from typing import Optional

router = APIRouter()

@router.get("/me")
def read_users_me(current_user: Optional[UserModel] = Depends(get_current_user), db: Session = Depends(get_db)):
    """
    获取当前用户信息 - 按照前端示例格式返回
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")

    # 获取购物车信息
    from fastapi_app.crud import cart as crud_cart
    try:
        cart_items = crud_cart.get_cart_items(db, current_user.id)
    except:
        cart_items = []

    # 转换购物车格式以匹配前端
    cart_data = []
    for item in cart_items:
        cart_item = {
            "id": item.id,
            "name": item.name,
            "price": item.price,
            "image": item.image or "",
            "quantity": item.count,
            "packaging": item.packaging or "10包/袋",
            "isVip": item.is_vip if hasattr(item, 'is_vip') else False
        }
        cart_data.append(cart_item)

    # 构建用户信息响应 - 匹配前端UserInfo接口
    user_data = {
        "id": current_user.id,
        "username": current_user.username,
        "realName": current_user.real_name or current_user.username,
        "phone": current_user.mobile or "",
        "address": current_user.address or "",
        "zipCode": current_user.zip_code or "",
        "avatarUrl": current_user.avatar_url or "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/user-avatar-default.png",
        "isMember": current_user.is_member,
        "cart": cart_data,
        # 保留原有字段以兼容现有接口
        "email": current_user.email,
        "mobile": current_user.mobile,
        "gender": current_user.gender,
        "age": current_user.age,
        "height": current_user.height,
        "weight": current_user.weight,
        "is_pregnant": current_user.is_pregnant,
        "is_preparing_pregnancy": current_user.is_preparing_pregnancy
    }

    # 添加会员期限信息
    if current_user.is_member and current_user.membership_start_date and current_user.membership_end_date:
        user_data["membershipPeriod"] = {
            "startDate": current_user.membership_start_date.strftime('%Y/%m/%d'),
            "endDate": current_user.membership_end_date.strftime('%Y/%m/%d')
        }

    return {
        "code": 0,
        "message": "获取用户信息成功",
        "data": user_data
    }

@router.put("/me", response_model=schemas.User)
def update_user_me(
    *,
    db: Session = Depends(get_db),
    user_in: schemas.UserUpdate,
    current_user: UserModel = Depends(get_current_user)
):
    """
    更新当前用户信息
    """
    user = crud_user.update_user(db=db, db_user=current_user, user_in=user_in)
    return user 
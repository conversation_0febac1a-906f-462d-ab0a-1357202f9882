from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from fastapi_app.core.database import get_db
from fastapi_app.core.security import get_current_user
from fastapi_app.models.user import User as UserModel
from fastapi_app.models import cart as models_cart
from fastapi_app.crud import cart as crud_cart
from fastapi_app.schemas import cart as schemas_cart

router = APIRouter()

@router.get("/")
def get_cart(
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Get current user's cart - 按照前端格式返回
    """
    if not current_user:
        raise HTTPException(status_code=401, detail="未登录")

    cart_items = crud_cart.get_cart_items(db, user_id=current_user.id)

    # 转换为前端格式
    cart_data = []
    for item in cart_items:
        cart_item = {
            "id": int(item.id) if item.id.isdigit() else hash(item.id) % 100000000,
            "name": item.name,
            "price": int(item.price),
            "image": item.image or "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-default.png",
            "quantity": item.count,
            "packaging": item.packaging or "10包/袋",
            "isVip": item.is_vip if hasattr(item, 'is_vip') else False
        }
        cart_data.append(cart_item)

    return {
        "code": 0,
        "message": "获取购物车成功",
        "data": cart_data
    }

@router.post("/", response_model=schemas_cart.CartItem)
def add_to_cart(
    item: schemas_cart.CartItemCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Add an item to the cart.
    """
    cart_item = crud_cart.add_item_to_cart(db, user_id=current_user.id, item=item)
    if not cart_item:
        raise HTTPException(status_code=404, detail="Goods or SKU not found")
    return cart_item

# Keep the old /add endpoint for backward compatibility
@router.post("/add", response_model=schemas_cart.CartItem)
def add_to_cart_legacy(
    item: schemas_cart.CartItemCreate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Add an item to the cart (legacy endpoint for backward compatibility).
    """
    return add_to_cart(item, db, current_user)

@router.put("/{item_id}", response_model=schemas_cart.CartItem)
def update_cart(
    item_id: str,
    item_update: schemas_cart.CartItemUpdate,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Update a cart item.
    """
    # First, ensure the item belongs to the current user.
    db_item = db.query(models_cart.CartItem).filter(
        models_cart.CartItem.id == item_id,
        models_cart.CartItem.user_id == current_user.id
    ).first()

    if not db_item:
        raise HTTPException(status_code=404, detail="Cart item not found")

    return crud_cart.update_cart_item(db, cart_item_id=item_id, item_update=item_update)

@router.delete("/{item_id}")
def remove_from_cart(
    item_id: str,
    db: Session = Depends(get_db),
    current_user: UserModel = Depends(get_current_user)
):
    """
    Remove an item from the cart.
    """
    # Ensure the item belongs to the current user before deleting.
    db_item = db.query(models_cart.CartItem).filter(
        models_cart.CartItem.id == item_id,
        models_cart.CartItem.user_id == current_user.id
    ).first()

    if not db_item:
        raise HTTPException(status_code=404, detail="Cart item not found")

    crud_cart.remove_item_from_cart(db, cart_item_id=item_id)
    return {"ok": True} 
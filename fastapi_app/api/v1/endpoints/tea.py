from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from fastapi_app.core.database import get_db
from fastapi_app.crud import recipe as crud_recipe
from fastapi_app.crud import goods as crud_goods
from fastapi_app.schemas.recipe import (
    TeaProductResponse, TeaListQuery, SelfSelectTea,
    RecommendTeaData, BasicRecommendTea, CustomizationTea,
    HerbIngredientResponse, RecipeResponse
)
from fastapi_app.models.user import User
from fastapi_app.core.security import get_current_user
import json
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/products")
def get_tea_products(
    category: Optional[str] = Query(None, description="茶品分类"),
    tea_type: Optional[str] = Query(None, description="茶品类型: basicRecommend, customization"),
    seasonal_recommend: Optional[bool] = Query(None, description="是否时令推荐"),
    self_select_category: Optional[str] = Query(None, description="自选分类"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    获取茶品列表
    """
    try:
        # 构建查询
        from fastapi_app.models.goods import Goods
        query = db.query(Goods).filter(Goods.status == 0)

        # 应用筛选条件
        if tea_type:
            query = query.filter(Goods.tea_type == tea_type)
        if seasonal_recommend is not None:
            query = query.filter(Goods.seasonal_recommend == seasonal_recommend)
        if category:
            query = query.filter(Goods.category_name == category)

        # 获取商品列表
        goods_list = query.offset(skip).limit(limit).all()
        
        # 如果有自选分类筛选
        if self_select_category and self_select_category != "全部":
            filtered_goods = []
            for goods in goods_list:
                if goods.self_select_categories:
                    try:
                        categories = json.loads(goods.self_select_categories)
                        if self_select_category in categories:
                            filtered_goods.append(goods)
                    except:
                        continue
            goods_list = filtered_goods
        
        # 转换为前端格式
        tea_products = []
        for goods in goods_list:
            # 解析自选分类
            self_select_categories = []
            if goods.self_select_categories:
                try:
                    self_select_categories = json.loads(goods.self_select_categories)
                except:
                    self_select_categories = []

            tea_product = {
                "id": goods.id,  # 使用原始ID，保持一致性
                "name": goods.name,
                "image": goods.image or "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-default.png",
                "intro": goods.intro or goods.description or "",
                "price": int(goods.price),
                "effect": goods.effect,
                "type": goods.tea_type or "basicRecommend",
                "category": goods.category_name,
                "seasonalRecommend": goods.seasonal_recommend,
                "selfSelect": {
                    "isSelected": goods.self_select_enabled,
                    "subCategories": self_select_categories
                }
            }
            tea_products.append(tea_product)

        return {
            "code": 0,
            "message": "获取茶品列表成功",
            "data": tea_products
        }
        
    except Exception as e:
        logger.error(f"获取茶品列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取茶品列表失败")

@router.get("/products/{product_id}")
def get_tea_product_detail(
    product_id: str,
    db: Session = Depends(get_db)
):
    """
    获取茶品详情
    """
    try:
        goods = crud_goods.get_goods_detail(db, product_id)
        if not goods:
            raise HTTPException(status_code=404, detail="茶品不存在")
        
        # 获取配方信息
        recipes = crud_recipe.get_recipes_by_goods_id(db, product_id)
        recipe_responses = []
        for recipe in recipes:
            ingredients = crud_recipe.get_recipe_ingredients(db, recipe.id)
            recipe_response = RecipeResponse(
                id=recipe.id,
                name=recipe.name,
                description=recipe.description,
                intro=recipe.intro,
                image=recipe.image,
                grams=recipe.grams,
                goods_id=recipe.goods_id,
                is_active=recipe.is_active,
                ingredients=[],  # 可以根据需要加载成分信息
                created_at=recipe.create_time.isoformat() if recipe.create_time else None,
                updated_at=recipe.update_time.isoformat() if recipe.update_time else None
            )
            recipe_responses.append(recipe_response)
        
        # 解析自选分类
        self_select_categories = []
        if goods.self_select_categories:
            try:
                self_select_categories = json.loads(goods.self_select_categories)
            except:
                self_select_categories = []

        tea_product = {
            "id": goods.id,
            "name": goods.name,
            "image": goods.image or "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-default.png",
            "intro": goods.intro or goods.description or "",
            "price": int(goods.price),
            "effect": goods.effect,
            "type": goods.tea_type or "basicRecommend",
            "category": goods.category_name,
            "seasonalRecommend": goods.seasonal_recommend,
            "selfSelect": {
                "isSelected": goods.self_select_enabled,
                "subCategories": self_select_categories
            },
            "recipes": recipe_responses,
            "sales": goods.sales,
            "status": goods.status,
            "created_at": goods.create_time.isoformat() if goods.create_time else None,
            "updated_at": goods.update_time.isoformat() if goods.update_time else None
        }

        return {
            "code": 0,
            "message": "获取茶品详情成功",
            "data": tea_product
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取茶品详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取茶品详情失败")

@router.get("/seasonal-recommend")
def get_seasonal_recommended_teas(
    limit: int = Query(10, ge=1, le=50),
    db: Session = Depends(get_db)
):
    """
    获取时令推荐茶品
    """
    try:
        goods_list = crud_recipe.get_seasonal_recommended_teas(db, limit=limit)
        
        tea_list = []
        for goods in goods_list:
            tea = SelfSelectTea.from_goods(goods)
            tea_list.append(tea.dict())

        return {
            "code": 0,
            "message": "获取时令推荐茶品成功",
            "data": tea_list
        }
        
    except Exception as e:
        logger.error(f"获取时令推荐茶品失败: {e}")
        raise HTTPException(status_code=500, detail="获取时令推荐茶品失败")

@router.get("/self-select")
def get_self_select_teas(
    category: Optional[str] = Query(None, description="自选分类: 全部, 助眠, 提神, 身材管理, 皮肤, 养脾胃"),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    获取自选茶包列表
    """
    try:
        goods_list = crud_recipe.get_self_select_teas(db, category=category, limit=limit)
        
        tea_list = []
        for goods in goods_list:
            tea = SelfSelectTea.from_goods(goods)
            tea_list.append(tea.dict())

        return {
            "code": 0,
            "message": "获取自选茶包成功",
            "data": tea_list
        }
        
    except Exception as e:
        logger.error(f"获取自选茶包失败: {e}")
        raise HTTPException(status_code=500, detail="获取自选茶包失败")

@router.get("/recommend/{report_id}")
def get_recommended_teas_by_report(
    report_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    根据体质报告获取推荐茶品
    """
    try:
        # 检查用户认证
        if not current_user:
            raise HTTPException(status_code=401, detail="需要登录才能获取推荐茶品")
        # 这里应该根据实际的体质报告来推荐茶品
        # 暂时返回示例数据
        
        # 获取基础推荐茶品
        basic_recommend_goods = crud_recipe.get_recommended_teas_by_constitution(
            db, constitution_type="default", limit=5
        )

        basic_recommend = []
        for goods in basic_recommend_goods:
            # 获取配方信息
            recipes = crud_recipe.get_recipes_by_goods_id(db, goods.id)
            recipe_list = []

            for recipe in recipes:
                # 获取配方成分
                ingredients = crud_recipe.get_recipe_ingredients(db, recipe.id)

                # 如果配方有成分，使用成分信息；否则使用配方本身
                if ingredients:
                    for ingredient in ingredients:
                        herb = ingredient.herb_ingredient
                        if herb:
                            recipe_item = {
                                "id": int(herb.id) if herb.id.isdigit() else hash(herb.id) % 100000000,
                                "name": herb.name,
                                "image": herb.image or "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/herb-default.png",
                                "intro": herb.intro or herb.description or "",
                                "grams": int(ingredient.amount) if ingredient.amount else 10
                            }
                            recipe_list.append(recipe_item)
                else:
                    # 如果没有成分，使用配方本身的信息
                    recipe_item = {
                        "id": int(recipe.id) if recipe.id.isdigit() else hash(recipe.id) % 100000000,
                        "name": recipe.name,
                        "image": recipe.image or "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/herb-default.png",
                        "intro": recipe.intro or recipe.description or "",
                        "grams": recipe.grams or 10
                    }
                    recipe_list.append(recipe_item)

            basic_tea = {
                "id": int(goods.id) if goods.id.isdigit() else hash(goods.id) % 100000000,
                "name": goods.name,
                "image": goods.image or "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/tea-default.png",
                "intro": goods.intro or goods.description or "",
                "price": int(goods.price),
                "effect": goods.effect or "",
                "recipes": recipe_list
            }
            basic_recommend.append(basic_tea)
        
        # 获取定制茶品（暂时使用第一个定制类型的茶品）
        from fastapi_app.models.goods import Goods
        customization_goods = db.query(Goods).filter(
            Goods.tea_type == "customization"
        ).first()

        customization = None
        if customization_goods:
            # 获取配方信息
            recipes = crud_recipe.get_recipes_by_goods_id(db, customization_goods.id)
            recipe_list = []

            for recipe in recipes:
                # 获取配方成分
                ingredients = crud_recipe.get_recipe_ingredients(db, recipe.id)

                # 如果配方有成分，使用成分信息；否则使用配方本身
                if ingredients:
                    for ingredient in ingredients:
                        herb = ingredient.herb_ingredient
                        if herb:
                            recipe_item = {
                                "id": int(herb.id) if herb.id.isdigit() else hash(herb.id) % 100000000,
                                "name": herb.name,
                                "image": herb.image or "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/herb-default.png",
                                "intro": herb.intro or herb.description or "",
                                "grams": int(ingredient.amount) if ingredient.amount else 10
                            }
                            recipe_list.append(recipe_item)
                else:
                    # 如果没有成分，使用配方本身的信息
                    recipe_item = {
                        "id": int(recipe.id) if recipe.id.isdigit() else hash(recipe.id) % 100000000,
                        "name": recipe.name,
                        "image": recipe.image or "https://cloud-1317345444.cos.ap-shanghai.myqcloud.com/herb-default.png",
                        "intro": recipe.intro or recipe.description or "",
                        "grams": recipe.grams or 10
                    }
                    recipe_list.append(recipe_item)

            customization = {
                "id": int(customization_goods.id) if customization_goods.id.isdigit() else hash(customization_goods.id) % 100000000,
                "name": customization_goods.name,
                "price": int(customization_goods.price),
                "recipes": recipe_list
            }
        
        recommend_data = {
            "BCReportId": report_id,
            "userName": current_user.username,
            "date": "2025-06-30",  # 当前日期
            "basicRecommend": basic_recommend,
            "customization": customization
        }

        return {
            "code": 0,
            "message": "获取推荐茶品成功",
            "data": recommend_data
        }

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取推荐茶品失败: {e}")
        raise HTTPException(status_code=500, detail="获取推荐茶品失败")

@router.get("/herbs")
def get_herb_ingredients(
    name: Optional[str] = Query(None, description="草药名称"),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    获取草药成分列表
    """
    try:
        herbs = crud_recipe.get_herb_ingredients(
            db, skip=skip, limit=limit, name=name, is_active=True
        )
        
        herb_responses = []
        for herb in herbs:
            herb_response = {
                "id": herb.id,
                "name": herb.name,
                "description": herb.description,
                "intro": herb.intro,
                "image": herb.image,
                "flavor": herb.flavor,
                "nature": herb.nature,
                "is_active": herb.is_active,
                "created_at": herb.create_time.isoformat() if herb.create_time else None,
                "updated_at": herb.update_time.isoformat() if herb.update_time else None
            }
            herb_responses.append(herb_response)

        return {
            "code": 0,
            "message": "获取草药成分列表成功",
            "data": herb_responses
        }
        
    except Exception as e:
        logger.error(f"获取草药成分列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取草药成分列表失败")

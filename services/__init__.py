"""
服务模块入口文件
导出所有服务功能
"""

import sys
import os
from pathlib import Path

# 将项目根目录添加到Python路径
current_path = Path(__file__).parent.absolute()
root_path = current_path.parent
if str(root_path) not in sys.path:
    sys.path.append(str(root_path))

# 导入用户服务模块的功能
from .user_service import (
    get_user_by_wechat_openid,
    get_user_by_wechat_unionid,
    create_wechat_user,
    update_user_wechat_info,
    find_wechat_users_by_nickname_prefix
)

# 导入微信服务模块
from .wechat_service import wechat_mini_program_api

# 导入茶产品服务模块
from .tea_product_service import TeaProductService

# 导入体质报告服务模块
from .body_report_service import (
    get_report_overview,
    get_report_detail,
    get_tea_recommend
)

# 导入购物车服务模块
from .cart_service import (
    get_cart,
    add_cart_item,
    update_cart_item,
    remove_cart_items,
    clear_cart,
    update_cart_item_selection,
    get_or_create_cart
)

# 创建wechat_service命名空间，兼容原来的导入方式
class WechatServiceNamespace:
    def __init__(self):
        self.wechat_mini_program_api = wechat_mini_program_api

# 实例化命名空间
wechat_service = WechatServiceNamespace() 
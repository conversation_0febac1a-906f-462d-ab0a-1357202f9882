"""
茶产品服务模块
处理茶产品的查询、分页和分类功能
"""

import sys
import os
from pathlib import Path

# 将项目根目录添加到Python路径
current_path = Path(__file__).parent.absolute()
root_path = current_path.parent
if str(root_path) not in sys.path:
    sys.path.append(str(root_path))

from typing import Dict, Any, List, Optional, Tuple
import logging
from sqlalchemy import select, func, or_, and_
from sqlalchemy.orm import Session
from datetime import datetime
import json
from fastapi import HTTPException

from sql.db_model import TeaProducts, TeaProductCategories, WechatUsers

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TeaProductService:
    """
    茶产品服务类
    处理茶产品的查询和分类功能
    """

    @staticmethod
    def get_all_products(
        db: Session,
        page: int = 1,
        page_size: int = 20,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取所有茶产品列表，支持分页

        Args:
            db: 数据库会话
            page: 页码，默认为1
            page_size: 每页数量，默认为20
            user_id: 用户ID，如果提供则获取个性化推荐

        Returns:
            包含产品列表、分类和分页信息的字典
        """
        try:
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 获取产品总数
            total_count = db.execute(
                select(func.count(TeaProducts.id)).where(
                    TeaProducts.deleted_at.is_(None),
                    TeaProducts.status == 1
                )
            )
            total = total_count.scalar() or 0
            
            # 查询产品，按展示顺序排序
            product_query = select(TeaProducts).where(
                TeaProducts.deleted_at.is_(None),
                TeaProducts.status == 1
            ).order_by(
                TeaProducts.is_seasonal.desc(),
                TeaProducts.display_order.asc(),
                TeaProducts.created_at.desc()
            ).offset(offset).limit(page_size)
            
            # 执行查询
            products_result = db.execute(product_query)
            products = products_result.scalars().all()
            
            # 查询分类信息
            category_query = select(TeaProductCategories).where(
                TeaProductCategories.deleted_at.is_(None),
                TeaProductCategories.status == 1
            ).order_by(TeaProductCategories.display_order.asc())
            
            categories_result = db.execute(category_query)
            categories = categories_result.scalars().all()
            
            # 判断用户是否已授权
            user_is_authorized = False
            has_body_data = False
            
            if user_id:
                # 查询用户信息
                user_query = select(WechatUsers).where(
                    WechatUsers.id == user_id,
                    WechatUsers.deleted_at.is_(None)
                )
                user_result = db.execute(user_query)
                user = user_result.scalar_one_or_none()
                
                if user:
                    user_is_authorized = user.user_info_authorized
                    # 这里假设有一个字段表示用户是否填写了体质数据
                    # 实际情况可能需要查询其他表或者添加该字段到用户表
                    has_body_data = False  # 暂时默认为False

            # 格式化产品数据
            product_list = []
            for product in products:
                product_data = {
                    "id": product.id,
                    "name": product.name,
                    "series": product.series,  # 添加系列字段
                    "intro": product.intro,
                    "price": product.price,
                    "image": product.image,
                    "effect": product.effect,
                    "stock": product.stock,
                    "is_seasonal": product.is_seasonal,
                    "is_help_sleep": product.is_help_sleep,
                    "is_energizing": product.is_energizing,
                    "is_weight_management": product.is_weight_management,
                    "is_skin_care": product.is_skin_care,
                    "is_digestive": product.is_digestive
                }
                product_list.append(product_data)
            
            # 格式化分类数据
            category_list = [{
                "id": "all",
                "name": "全部",
                "property": "all",
                "order": 1
            }]
            
            for category in categories:
                category_data = {
                    "id": category.id,
                    "name": category.name,
                    "property": category.property_name,
                    "order": category.display_order + 1  # 全部分类占据第一位
                }
                category_list.append(category_data)

            # 构造返回结果
            result = {
                "products": product_list,
                "categories": category_list,
                "pagination": {
                    "total": total,
                    "page": page,
                    "page_size": page_size
                },
                "user_status": {
                    "is_authorized": user_is_authorized,
                    "has_body_data": has_body_data
                }
            }
            
            logger.info(f"成功获取产品列表，共{len(product_list)}条记录，第{page}页，每页{page_size}条")
            return result
            
        except Exception as e:
            logger.error(f"获取产品列表时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取产品列表失败: {str(e)}")

    @staticmethod
    def get_product_by_id(
        db: Session,
        product_id: str,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        根据ID获取产品详情

        Args:
            db: 数据库会话
            product_id: 产品ID
            user_id: 用户ID，可选

        Returns:
            产品详情字典
        """
        try:
            # 查询产品
            product_query = select(TeaProducts).where(
                TeaProducts.id == product_id,
                TeaProducts.deleted_at.is_(None),
                TeaProducts.status == 1
            )
            
            product_result = db.execute(product_query)
            product = product_result.scalar_one_or_none()
            
            if not product:
                raise HTTPException(status_code=404, detail=f"产品不存在: {product_id}")
            
            # 判断用户是否已授权
            user_is_authorized = False
            if user_id:
                user_query = select(WechatUsers).where(
                    WechatUsers.id == user_id,
                    WechatUsers.deleted_at.is_(None)
                )
                user_result = db.execute(user_query)
                user = user_result.scalar_one_or_none()
                if user:
                    user_is_authorized = user.user_info_authorized
            
            # 格式化产品数据
            product_data = {
                "id": product.id,
                "name": product.name,
                "series": product.series,  # 添加系列字段
                "intro": product.intro,
                "price": product.price,
                "image": product.image,
                "effect": product.effect,
                "stock": product.stock,
                "is_seasonal": product.is_seasonal,
                "is_help_sleep": product.is_help_sleep,
                "is_energizing": product.is_energizing,
                "is_weight_management": product.is_weight_management,
                "is_skin_care": product.is_skin_care,
                "is_digestive": product.is_digestive
            }
            
            logger.info(f"成功获取产品详情: {product_id}")
            return product_data
            
        except HTTPException:
            # 直接向上抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"获取产品详情时发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取产品详情失败: {str(e)}") 
# -*- coding: utf-8 -*-
"""
用户服务模块
处理用户创建、查询、更新等操作
支持微信小程序用户的处理
"""

from sqlalchemy.orm import Session
from sql.db_model import WechatUsers
from typing import Dict, Optional, List, Any
import logging
from datetime import datetime, timedelta
import uuid
import json

# 配置日志
logger = logging.getLogger(__name__)

def get_user_by_wechat_openid(db: Session, openid: str) -> Optional[WechatUsers]:
    """
    通过微信openid查询用户
    
    Args:
        db: 数据库会话
        openid: 微信openid
        
    Returns:
        查询到的用户或None
    """
    logger.info(f"通过微信openid查询用户: {openid[:5]}***")
    return db.query(WechatUsers).filter(
        WechatUsers.openid == openid,
        WechatUsers.deleted_at.is_(None)  # 确保用户未被删除
    ).first()

def get_user_by_wechat_unionid(db: Session, unionid: str) -> Optional[WechatUsers]:
    """
    通过微信unionid查询用户
    
    Args:
        db: 数据库会话
        unionid: 微信unionid
        
    Returns:
        查询到的用户或None
    """
    if not unionid:
        return None
        
    logger.info(f"通过微信unionid查询用户: {unionid[:5]}***")
    return db.query(WechatUsers).filter(
        WechatUsers.unionid == unionid,
        WechatUsers.deleted_at.is_(None)  # 确保用户未被删除
    ).first()

def create_wechat_user(db: Session, user_info: Dict[str, Any]) -> WechatUsers:
    """
    创建微信用户
    
    Args:
        db: 数据库会话
        user_info: 用户信息字典
        
    Returns:
        创建的用户对象
    """
    logger.info(f"创建新的微信用户: {json.dumps(user_info, ensure_ascii=False)[:100]}...")
    
    # 获取必要字段
    openid = user_info.get('openid')
    
    if not openid:
        raise ValueError("缺少必要的openid字段")
    
    # 准备用户数据
    now = datetime.utcnow()
    
    # 创建新用户
    db_user = WechatUsers(
        # 基础信息
        openid=openid,
        unionid=user_info.get('unionid'),
        session_key=user_info.get('session_key'),
        session_key_expires_at=now + timedelta(days=30),  # 默认30天
        
        # 授权状态
        user_info_authorized=user_info.get('is_authorized', False),
        auth_time=now if user_info.get('is_authorized') else None,
        
        # 用户信息
        nickname=user_info.get('nickname', f"微信用户_{uuid.uuid4().hex[:6]}"),
        sex=user_info.get('gender', 0),
        language=user_info.get('language', ''),
        city=user_info.get('city', ''),
        province=user_info.get('province', ''),
        country=user_info.get('country', ''),
        headimgurl=user_info.get('avatar_url', ''),
        
        # 更新时间
        nickname_updated_at=now if user_info.get('nickname') else None,
        avatar_updated_at=now if user_info.get('avatar_url') else None,
        
        # 状态信息
        is_wechat_miniprogram=True,
        status=0,  # 正常状态
        
        # 登录信息
        last_login_at=now,
        login_count=1
    )
    
    # 保存到数据库
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    logger.info(f"微信用户创建成功: id={db_user.id}, nickname={db_user.nickname}")
    return db_user

def update_user_wechat_info(db: Session, user: WechatUsers, user_info: Dict[str, Any]) -> WechatUsers:
    """
    更新微信用户信息
    
    Args:
        db: 数据库会话
        user: 用户对象
        user_info: 新的用户信息
        
    Returns:
        更新后的用户对象
    """
    logger.info(f"更新用户 {user.id} 的微信信息")
    
    now = datetime.utcnow()
    has_updates = False
    
    # 更新会话密钥（如果有）
    if user_info.get('session_key'):
        user.session_key = user_info.get('session_key')
        user.session_key_expires_at = now + timedelta(days=30)  # 默认30天
        has_updates = True
    
    # 更新unionid（如果提供且原来没有）
    if user_info.get('unionid') and not user.unionid:
        user.unionid = user_info.get('unionid')
        has_updates = True
    
    # 更新授权状态
    if user_info.get('is_authorized', False):
        user.user_info_authorized = True
        user.auth_time = now
        has_updates = True
    
    # 更新用户基本信息（只有当有授权时）
    if user_info.get('is_authorized', False):
        # 更新昵称
        if user_info.get('nickname') and user_info.get('nickname') != user.nickname:
            user.nickname = user_info.get('nickname')
            user.nickname_updated_at = now
            has_updates = True
            
        # 更新头像
        if user_info.get('avatar_url') and user_info.get('avatar_url') != user.headimgurl:
            user.headimgurl = user_info.get('avatar_url')
            user.avatar_updated_at = now
            has_updates = True
            
        # 更新其他信息
        if user_info.get('gender') is not None and user_info.get('gender') != user.sex:
            user.sex = user_info.get('gender')
            has_updates = True
            
        if user_info.get('language') and user_info.get('language') != user.language:
            user.language = user_info.get('language')
            has_updates = True
            
        if user_info.get('country') and user_info.get('country') != user.country:
            user.country = user_info.get('country')
            has_updates = True
            
        if user_info.get('province') and user_info.get('province') != user.province:
            user.province = user_info.get('province')
            has_updates = True
            
        if user_info.get('city') and user_info.get('city') != user.city:
            user.city = user_info.get('city')
            has_updates = True
    
    # 更新登录信息
    user.last_login_at = now
    user.login_count = user.login_count + 1 if user.login_count else 1
    
    # 保存更新
    if has_updates:
        user.updated_at = now
        logger.info(f"用户信息已更新: id={user.id}")
    else:
        logger.info(f"用户信息无变化: id={user.id}")
        
    db.commit()
    db.refresh(user)
    
    return user

def find_wechat_users_by_nickname_prefix(db: Session, prefix: str, limit: int = 10) -> List[WechatUsers]:
    """
    通过昵称前缀查找微信用户
    
    Args:
        db: 数据库会话
        prefix: 昵称前缀
        limit: 返回结果数量限制
        
    Returns:
        用户列表
    """
    logger.info(f"通过昵称前缀查找微信用户: prefix={prefix}")
    
    return db.query(WechatUsers).filter(
        WechatUsers.nickname.like(f"{prefix}%"),
        WechatUsers.deleted_at.is_(None)  # 确保用户未被删除
    ).limit(limit).all() 
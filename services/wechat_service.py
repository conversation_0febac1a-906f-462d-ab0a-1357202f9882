# -*- coding: utf-8 -*-
"""
微信服务模块
处理微信小程序登录认证和用户信息获取
基于2025年最新的微信小程序API规范
"""

import sys
import os
from pathlib import Path

# 将项目根目录添加到Python路径
current_path = Path(__file__).parent.absolute()
root_path = current_path.parent
if str(root_path) not in sys.path:
    sys.path.append(str(root_path))

import requests
from typing import Dict, Any, Optional
import json
import base64
from Crypto.Cipher import AES
import logging
import time
from datetime import datetime
from fastapi import HTTPException
from dotenv import load_dotenv
from os import getenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 微信小程序配置
WECHAT_MINI_APPID = getenv("WECHAT_APP_ID")
WECHAT_MINI_SECRET = getenv("WECHAT_APP_SECRET")

# API端点
WECHAT_CODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session"


class WeChatMiniProgramAPI:
    """
    微信小程序API服务类
    处理微信小程序的登录认证和用户信息解密
    """

    @staticmethod
    async def get_access_token(code: str) -> Dict[str, Any]:
        """
        通过授权码获取微信小程序用户的session_key和openid

        Args:
            code: 微信小程序前端获取的临时授权码

        Returns:
            包含session_key和openid的字典

        Raises:
            HTTPException: 当请求微信API失败时
        """
        logger.info(f"调用微信小程序登录API，授权码: {code[:4]}***")

        try:
            # 构建请求参数
            params = {
                "appid": WECHAT_MINI_APPID,
                "secret": WECHAT_MINI_SECRET,
                "js_code": code,
                "grant_type": "authorization_code",
            }

            # 请求微信API
            response = requests.get(WECHAT_CODE2SESSION_URL, params=params)
            response_data = response.json()

            # 检查响应
            if "errcode" in response_data and response_data["errcode"] != 0:
                error_msg = (
                    f"微信登录API错误: {response_data.get('errmsg', '未知错误')}"
                )
                logger.error(f"{error_msg}, 错误代码: {response_data.get('errcode')}")
                raise HTTPException(status_code=400, detail=error_msg)

            # 检查必要字段
            if "openid" not in response_data or "session_key" not in response_data:
                logger.error("微信API返回数据缺少必要字段 openid 或 session_key")
                raise HTTPException(status_code=400, detail="微信登录数据无效")

            # 构建返回结果
            result = {
                "openid": response_data["openid"],
                "access_token": response_data[
                    "session_key"
                ],  # 重命名为access_token以兼容接口
                "session_key": response_data["session_key"],
            }

            # 如果返回了unionid，也加入结果
            if "unionid" in response_data:
                result["unionid"] = response_data["unionid"]
                logger.info(f"成功获取unionid: {response_data['unionid'][:5]}***")
            else:
                logger.info("未返回unionid，可能需要满足特定条件才能获取")

            logger.info(
                f"微信小程序登录API调用成功，已获取openid: {result['openid'][:5]}***"
            )
            return result

        except requests.RequestException as e:
            logger.error(f"请求微信API时发生异常: {str(e)}")
            raise HTTPException(status_code=500, detail="连接微信服务器失败")

        except json.JSONDecodeError:
            logger.error("解析微信API响应JSON数据失败")
            raise HTTPException(status_code=500, detail="解析微信返回数据失败")

        except Exception as e:
            logger.error(f"获取微信access_token过程中发生未知错误: {str(e)}")
            raise HTTPException(status_code=500, detail="处理微信登录请求失败")

    @staticmethod
    def decrypt_user_info(
        session_key: str, encrypted_data: str, iv: str
    ) -> Dict[str, Any]:
        """
        解密微信小程序用户信息

        Args:
            session_key: 微信API返回的会话密钥
            encrypted_data: 加密的用户数据
            iv: 加密算法的初始向量

        Returns:
            解密后的用户信息字典

        Raises:
            Exception: 解密失败时抛出异常
        """
        if not all([session_key, encrypted_data, iv]):
            logger.warning("解密参数不完整，无法解密用户信息")
            return {}

        logger.info("开始解密微信用户数据...")

        try:
            # 确保正确的padding
            def fix_padding(data):
                """修复base64解码时的padding问题"""
                missing_padding = len(data) % 4
                if missing_padding:
                    data += "=" * (4 - missing_padding)
                return data

            # 解码参数
            session_key = base64.b64decode(fix_padding(session_key))
            encrypted_data = base64.b64decode(fix_padding(encrypted_data))
            iv = base64.b64decode(fix_padding(iv))

            # 使用AES-CBC模式解密
            cipher = AES.new(session_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)

            # 处理PKCS#7填充
            padding_len = decrypted[-1]
            if padding_len < 1 or padding_len > 32:
                # 无效的padding
                raise ValueError("无效的padding")

            decrypted = decrypted[:-padding_len]

            # 解析JSON
            decrypted_str = decrypted.decode("utf-8")
            user_info = json.loads(decrypted_str)

            # 验证数据有效性
            if user_info.get("watermark", {}).get("appid") != WECHAT_MINI_APPID:
                logger.warning("数据水印中的appid与配置不匹配")
                raise ValueError("数据水印验证失败")

            logger.info(
                f"用户数据解密成功: {json.dumps(user_info, ensure_ascii=False)[:100]}..."
            )
            return user_info

        except ValueError as e:
            logger.error(f"解密数据格式错误: {str(e)}")
            raise Exception(f"解密数据验证失败: {str(e)}")

        except Exception as e:
            logger.error(f"解密过程中发生未知错误: {str(e)}")
            raise Exception(f"解密用户信息失败: {str(e)}")

    async def get_user_info(
        self,
        session_key: str,
        openid: str,
        encrypted_data: Optional[str] = None,
        iv: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取用户信息，优先使用解密数据，如无则使用基本信息

        Args:
            session_key: 微信会话密钥
            openid: 用户openid
            encrypted_data: 加密的用户数据（可选）
            iv: 加密初始向量（可选）

        Returns:
            用户信息字典
        """
        # 初始化基本用户信息
        user_info = {
            "openid": openid,
            # 默认用户昵称使用"微信用户+随机数"
            "nickname": f"微信用户_{str(int(time.time()))[-6:]}",
            "is_authorized": False,
        }

        # 如果提供了加密数据，尝试解密获取完整信息
        if encrypted_data and iv:
            try:
                # 解密用户信息
                decrypted_data = self.decrypt_user_info(session_key, encrypted_data, iv)

                # 保存原始数据用于调试和记录
                user_info["raw_data"] = decrypted_data

                # 获取可用的用户信息字段
                if decrypted_data:
                    user_info["is_authorized"] = True
                    user_info["nickname"] = decrypted_data.get(
                        "nickName", user_info["nickname"]
                    )
                    user_info["avatar_url"] = decrypted_data.get("avatarUrl", "")
                    user_info["gender"] = decrypted_data.get("gender", 0)
                    user_info["country"] = decrypted_data.get("country", "")
                    user_info["province"] = decrypted_data.get("province", "")
                    user_info["city"] = decrypted_data.get("city", "")
                    user_info["language"] = decrypted_data.get("language", "")

                    # 更新时间
                    current_time = datetime.now().isoformat()
                    user_info["nickname_updated_at"] = current_time
                    user_info["avatar_updated_at"] = current_time
                    user_info["auth_time"] = current_time

                    # 如果有unionid，也添加到结果中
                    if "unionId" in decrypted_data:
                        user_info["unionid"] = decrypted_data["unionId"]

                logger.info(f"成功解密并获取用户信息: {user_info['nickname']}")

            except Exception as e:
                logger.error(f"解密用户信息失败，将使用基本信息: {str(e)}")
        else:
            logger.info(f"未提供加密数据，使用基本用户信息: openid={openid}")

        return user_info


# 初始化微信小程序API服务
wechat_mini_program_api = WeChatMiniProgramAPI()

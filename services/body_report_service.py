"""
体质报告服务模块
提供用户体质报告相关的业务逻辑实现
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple

from sqlalchemy import desc, func
from sqlalchemy.orm import Session
from sqlalchemy.sql import text

from sql.db_model import (
    UserBodyReports, 
    UserBodyReportScores,
    BodyConstitutionTypes,
    UserCustomTea,
    CustomTeaRecipes,
    Herbs,
    TeaProducts,
    TeaProductRecipes
)

# 时间格式化字符串
DATE_FORMAT = "%Y-%m-%d"

async def get_report_overview(db: Session, user_id: str, limit: int = 5) -> Dict[str, Any]:
    """
    获取用户体质报告总览数据
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        limit: 历史报告返回数量
        
    Returns:
        包含报告总览数据的字典
    """
    # 查询用户最近半年的报告
    half_year_ago = datetime.now() - timedelta(days=180)
    
    # 获取用户的所有报告，按日期降序排序
    reports = db.query(UserBodyReports).filter(
        UserBodyReports.user_id == user_id,
        UserBodyReports.deleted_at.is_(None),
        UserBodyReports.date >= half_year_ago
    ).order_by(desc(UserBodyReports.date)).all()
    
    # 如果没有报告，返回空数据
    if not reports:
        return {
            "half_year_avg_score": None,
            "latest_constitution_type": None,
            "chart_data": [],
            "date_range": "",
            "report_history": [],
            "has_completed_test": False
        }
    
    # 计算半年平均分数
    avg_score = sum(report.score for report in reports) / len(reports) if reports else 0
    
    # 获取最近一次报告的体质类型（主体质）
    latest_report = reports[0]
    latest_report_scores = db.query(UserBodyReportScores).filter(
        UserBodyReportScores.report_id == latest_report.id
    ).all()
    
    # 获取主体质类型
    main_constitution_type = _get_main_constitution_type(db, latest_report_scores)
    
    # 构建图表数据
    chart_data = []
    for report in reports:
        chart_data.append({
            "date": report.date.strftime(DATE_FORMAT),
            "score": report.score
        })
    
    # 设置日期范围
    date_range = ""
    if reports:
        oldest_date = reports[-1].date.strftime(DATE_FORMAT)
        latest_date = reports[0].date.strftime(DATE_FORMAT)
        date_range = f"{oldest_date} ~ {latest_date}"
    
    # 构建历史报告列表
    report_history = []
    for i, report in enumerate(reports[:limit]):
        # 获取报告对应的主体质类型
        report_scores = db.query(UserBodyReportScores).filter(
            UserBodyReportScores.report_id == report.id
        ).all()
        
        bc_type = _get_main_constitution_type(db, report_scores)
        
        report_history.append({
            "id": report.id,
            "date": report.date.strftime(DATE_FORMAT),
            "score": report.score,
            "overall": {
                "BCType": bc_type,
                "mainSymptoms": report.main_symptoms
            }
        })
    
    return {
        "half_year_avg_score": round(avg_score),
        "latest_constitution_type": main_constitution_type,
        "chart_data": chart_data,
        "date_range": date_range,
        "report_history": report_history,
        "has_completed_test": True
    }

async def get_report_detail(db: Session, report_id: str, user_id: str) -> Dict[str, Any]:
    """
    获取用户体质报告详情数据
    
    Args:
        db: 数据库会话
        report_id: 报告ID
        user_id: 用户ID，用于验证权限
        
    Returns:
        包含报告详情数据的字典
    """
    # 查询指定报告
    report = db.query(UserBodyReports).filter(
        UserBodyReports.id == report_id,
        UserBodyReports.deleted_at.is_(None)
    ).first()
    
    # 如果报告不存在或已删除
    if not report:
        return None
    
    # 验证报告所属用户
    if report.user_id != user_id:
        return None
    
    # 获取报告相关的体质评分
    report_scores = db.query(
        UserBodyReportScores, 
        BodyConstitutionTypes.name, 
        BodyConstitutionTypes.code
    ).join(
        BodyConstitutionTypes, 
        UserBodyReportScores.constitution_type_id == BodyConstitutionTypes.id
    ).filter(
        UserBodyReportScores.report_id == report_id
    ).all()
    
    # 获取主体质和兼有体质
    main_bc, secondary_bc = _get_constitution_types(db, report_scores)
    
    # 构建体质指数数组（用于雷达图）
    bc_index = []
    for score, bc_name, _ in report_scores:
        bc_index.append({
            "name": bc_name,
            "value": score.score
        })
    
    # 获取主体质的释义
    main_bc_definition = db.query(BodyConstitutionTypes).filter(
        BodyConstitutionTypes.name == main_bc
    ).first()
    
    # 构建报告详情响应
    return {
        "id": report.id,
        "date": report.date.strftime(DATE_FORMAT),
        "user_name": "", # 这里需要关联用户表获取用户名称，暂时留空
        "score": report.score,
        "overall": {
            "BCType": main_bc,
            "mainSymptoms": report.main_symptoms,
            "text": report.overall_text
        },
        "analysis": {
            "mainBC": main_bc,
            "secondaryBC": secondary_bc,
            "BCIndex": bc_index
        },
        "definition": {
            "definition": main_bc_definition.definition,
            "bodyShape": main_bc_definition.body_shape,
            "commonSymptoms": main_bc_definition.common_symptoms,
            "psychology": main_bc_definition.psychology,
            "diseases": main_bc_definition.diseases,
            "environment": main_bc_definition.environment
        },
        "dailyTips": {
            "term": report.term,
            "intro": report.intro,
            "food": report.food,
            "sleep": report.sleep,
            "exercise": report.exercise,
            "mood": report.mood
        }
    }

async def get_tea_recommend(db: Session, report_id: str, user_id: str) -> Dict[str, Any]:
    """
    获取基于体质报告的茶品推荐
    
    Args:
        db: 数据库会话
        report_id: 报告ID
        user_id: 用户ID，用于验证权限
        
    Returns:
        包含茶品推荐数据的字典
    """
    # 查询指定报告
    report = db.query(UserBodyReports).filter(
        UserBodyReports.id == report_id,
        UserBodyReports.deleted_at.is_(None)
    ).first()
    
    # 如果报告不存在或已删除
    if not report:
        return None
    
    # 验证报告所属用户
    if report.user_id != user_id:
        return None
        
    # 获取报告主体质类型
    report_scores = db.query(
        UserBodyReportScores, 
        BodyConstitutionTypes.name, 
        BodyConstitutionTypes.code
    ).join(
        BodyConstitutionTypes, 
        UserBodyReportScores.constitution_type_id == BodyConstitutionTypes.id
    ).filter(
        UserBodyReportScores.report_id == report_id
    ).all()
    
    main_bc, _ = _get_constitution_types(db, report_scores)
    
    # 获取基础推荐茶品
    basic_recommended_products = []
    if report.basic_recommended_products:
        product_ids = report.basic_recommended_products
        
        products = db.query(TeaProducts).filter(
            TeaProducts.id.in_(product_ids),
            TeaProducts.deleted_at.is_(None)
        ).all()
        
        for product in products:
            # 获取产品配方
            recipes = db.query(
                TeaProductRecipes.grams,
                Herbs.name
            ).join(
                Herbs,
                TeaProductRecipes.herb_id == Herbs.id
            ).filter(
                TeaProductRecipes.tea_product_id == product.id
            ).all()
            
            product_recipes = []
            for grams, herb_name in recipes:
                product_recipes.append({
                    "name": herb_name,
                    "grams": grams
                })
                
            # 根据匹配产品顺序计算匹配度分数
            match_score = 95 - (product_ids.index(product.id) * 5)
            if match_score < 80:
                match_score = 80
                
            basic_recommended_products.append({
                "id": product.id,
                "name": product.name,
                "image": product.image,
                "effect": product.effect,
                "price": product.price,
                "match_score": match_score,
                "recipes": product_recipes
            })
    
    # 获取私人定制茶品
    custom_tea = db.query(UserCustomTea).filter(
        UserCustomTea.report_id == report_id,
        UserCustomTea.status >= 0
    ).first()
    
    customization = None
    if custom_tea:
        # 获取定制茶品的配方
        custom_recipes = db.query(
            CustomTeaRecipes,
            Herbs.id.label("herb_id"),
            Herbs.name.label("herb_name"),
            Herbs.image.label("herb_image"),
            Herbs.intro.label("herb_intro")
        ).join(
            Herbs,
            CustomTeaRecipes.herb_id == Herbs.id
        ).filter(
            CustomTeaRecipes.custom_tea_id == custom_tea.id
        ).all()
        
        recipes = []
        for recipe, herb_id, herb_name, herb_image, herb_intro in custom_recipes:
            recipes.append({
                "id": herb_id,
                "name": herb_name,
                "grams": recipe.grams,
                "image": herb_image,
                "intro": herb_intro
            })
            
        customization = {
            "name": custom_tea.name,
            "price": custom_tea.price,
            "match_score": custom_tea.match_score,
            "recipes": recipes
        }
    
    return {
        "BC_report_id": report_id,
        "BC_type": main_bc,
        "main_symptoms": report.main_symptoms,
        "basic_recommend": basic_recommended_products,
        "customization": customization
    }

def _get_main_constitution_type(db: Session, report_scores: List) -> str:
    """
    获取主体质类型名称
    
    Args:
        db: 数据库会话
        report_scores: 报告评分记录
        
    Returns:
        主体质类型名称
    """
    if not report_scores:
        return ""
    
    # 根据评分找出最高分的体质类型
    max_score = 0
    max_score_type_ids = []
    
    for score in report_scores:
        if score.score > max_score:
            max_score = score.score
            max_score_type_ids = [score.constitution_type_id]
        elif score.score == max_score:
            max_score_type_ids.append(score.constitution_type_id)
    
    # 如果有多个最高分，按display_order选取排序最小的
    if len(max_score_type_ids) > 1:
        main_type = db.query(BodyConstitutionTypes).filter(
            BodyConstitutionTypes.id.in_(max_score_type_ids)
        ).order_by(BodyConstitutionTypes.display_order).first()
    else:
        main_type = db.query(BodyConstitutionTypes).filter(
            BodyConstitutionTypes.id == max_score_type_ids[0]
        ).first()
    
    return main_type.name if main_type else ""

def _get_constitution_types(db: Session, report_scores: List) -> Tuple[str, List[str]]:
    """
    获取主体质和兼有体质类型
    
    Args:
        db: 数据库会话
        report_scores: 报告评分记录列表，每项包含评分记录、体质类型名称和代码
        
    Returns:
        主体质名称和兼有体质名称列表的元组
    """
    if not report_scores:
        return "", []
    
    # 找出最高分
    max_score = 0
    max_score_items = []
    secondary_items = []
    
    for score_obj, bc_name, bc_code in report_scores:
        if score_obj.score > max_score:
            max_score = score_obj.score
            max_score_items = [(score_obj, bc_name, bc_code)]
        elif score_obj.score == max_score:
            max_score_items.append((score_obj, bc_name, bc_code))
            
        # 兼有体质：除主体质外评分大于等于60分的体质
        if score_obj.score >= 60:
            secondary_items.append((score_obj, bc_name, bc_code))
    
    # 如果有多个最高分，按display_order排序
    main_bc = ""
    if len(max_score_items) > 1:
        # 获取体质类型IDs
        bc_ids = [item[0].constitution_type_id for item in max_score_items]
        
        # 按display_order排序获取主体质
        main_bc_type = db.query(BodyConstitutionTypes).filter(
            BodyConstitutionTypes.id.in_(bc_ids)
        ).order_by(BodyConstitutionTypes.display_order).first()
        
        main_bc = main_bc_type.name if main_bc_type else ""
    else:
        main_bc = max_score_items[0][1] if max_score_items else ""
    
    # 提取兼有体质名称列表，排除主体质
    secondary_bc = [item[1] for item in secondary_items if item[1] != main_bc]
    
    return main_bc, secondary_bc 
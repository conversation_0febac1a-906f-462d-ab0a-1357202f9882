"""
购物车服务模块

提供购物车相关功能，包括添加商品、更新数量、删除商品、获取购物车列表等
"""

import logging
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional
from sqlalchemy import select, update, delete, func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from sql.db_model import Cart, CartProducts, TeaProducts, UserCustomTea, WechatUsers

logger = logging.getLogger(__name__)

def get_or_create_cart(db: Session, user_id: str) -> Cart:
    """
    获取用户购物车，如果不存在则创建
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        用户购物车对象
    """
    try:
        # 查询用户购物车
        cart = db.query(Cart).filter(Cart.user_id == user_id).first()
        
        # 如果不存在则创建
        if not cart:
            logger.info(f"创建新的购物车: user_id={user_id}")
            cart = Cart(user_id=user_id)
            db.add(cart)
            db.commit()
            db.refresh(cart)
        
        return cart
    except SQLAlchemyError as e:
        logger.error(f"获取或创建购物车失败: {e}")
        db.rollback()
        raise

def add_cart_item(db: Session, user_id: str, product_id: str, quantity: int, product_type: int = 0) -> Tuple[str, int]:
    """
    添加商品到购物车
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        product_id: 商品ID
        quantity: 商品数量
        product_type: 商品类型，0=普通茶品，1=定制茶品
        
    Returns:
        (购物车项ID, 购物车总数量)
        
    Raises:
        ValueError: 如果商品不存在或参数无效
        SQLAlchemyError: 数据库操作失败
    """
    try:
        # 验证参数
        if quantity <= 0:
            raise ValueError("商品数量必须大于0")
        
        # 验证商品是否存在
        if product_type == 0:
            product = db.query(TeaProducts).filter(TeaProducts.id == product_id).first()
            if not product:
                raise ValueError(f"茶产品不存在: {product_id}")
            if product.status != 1:
                raise ValueError(f"茶产品已下架: {product_id}")
            if product.stock < quantity:
                raise ValueError(f"库存不足，当前库存: {product.stock}")
        else:
            product = db.query(UserCustomTea).filter(UserCustomTea.id == product_id).first()
            if not product:
                raise ValueError(f"定制茶品不存在: {product_id}")
        
        # 获取或创建购物车
        cart = get_or_create_cart(db, user_id)
        
        # 检查购物车是否已有该商品
        cart_item = db.query(CartProducts).filter(
            CartProducts.cart_id == cart.id,
            CartProducts.product_id == product_id,
            CartProducts.product_type == product_type
        ).first()
        
        if cart_item:
            # 如果已存在，更新数量
            new_quantity = cart_item.quantity + quantity
            
            # 检查库存（仅对普通茶品）
            if product_type == 0 and new_quantity > product.stock:
                new_quantity = product.stock
                logger.warning(f"请求数量超过库存，已调整为最大库存: {product.stock}")
                
            cart_item.quantity = new_quantity
            cart_item.updated_at = datetime.now()
            cart_item_id = cart_item.id
        else:
            # 创建新的购物车项
            cart_item = CartProducts(
                cart_id=cart.id,
                product_id=product_id,
                product_type=product_type,
                quantity=quantity,
                selected=True
            )
            db.add(cart_item)
            db.flush()
            cart_item_id = cart_item.id
            
        # 提交事务
        db.commit()
        
        # 计算购物车总数量
        total_quantity = db.query(CartProducts).filter(CartProducts.cart_id == cart.id).with_entities(
            func.sum(CartProducts.quantity)
        ).scalar() or 0
        
        return cart_item_id, total_quantity
        
    except (ValueError, SQLAlchemyError) as e:
        db.rollback()
        logger.error(f"添加商品到购物车失败: {e}")
        raise

def update_cart_item(db: Session, user_id: str, cart_item_id: str, quantity: int) -> Tuple[Dict[str, Any], float, int]:
    """
    更新购物车商品数量
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        cart_item_id: 购物车项ID
        quantity: 新数量
        
    Returns:
        (更新后的购物车项信息, 购物车总价, 购物车总数量)
        
    Raises:
        ValueError: 如果参数无效
        SQLAlchemyError: 数据库操作失败
    """
    try:
        # 验证参数
        if quantity <= 0:
            raise ValueError("商品数量必须大于0")
            
        # 获取购物车
        cart = get_or_create_cart(db, user_id)
        
        # 查询购物车项
        cart_item = db.query(CartProducts).filter(
            CartProducts.id == cart_item_id,
            CartProducts.cart_id == cart.id
        ).first()
        
        if not cart_item:
            raise ValueError(f"购物车项不存在: {cart_item_id}")
            
        # 获取商品信息
        if cart_item.product_type == 0:
            product = db.query(TeaProducts).filter(TeaProducts.id == cart_item.product_id).first()
            if not product:
                raise ValueError(f"茶产品不存在: {cart_item.product_id}")
                
            # 检查库存
            if quantity > product.stock:
                quantity = product.stock
                logger.warning(f"请求数量超过库存，已调整为最大库存: {product.stock}")
        else:
            product = db.query(UserCustomTea).filter(UserCustomTea.id == cart_item.product_id).first()
            if not product:
                raise ValueError(f"定制茶品不存在: {cart_item.product_id}")
                
        # 更新数量
        cart_item.quantity = quantity
        cart_item.updated_at = datetime.now()
        
        # 提交事务
        db.commit()
        
        # 获取购物车总价和总数量
        total_price = 0
        total_quantity = 0
        
        cart_items = db.query(CartProducts).filter(CartProducts.cart_id == cart.id).all()
        
        for item in cart_items:
            if item.product_type == 0:
                p = db.query(TeaProducts).filter(TeaProducts.id == item.product_id).first()
                if p and item.selected:
                    total_price += p.price * item.quantity
            else:
                p = db.query(UserCustomTea).filter(UserCustomTea.id == item.product_id).first()
                if p and item.selected:
                    total_price += p.price * item.quantity
                    
            total_quantity += item.quantity
            
        # 获取更新后的商品信息
        item_info = {
            "id": cart_item.id,
            "quantity": cart_item.quantity
        }
        
        if cart_item.product_type == 0:
            item_info["price"] = product.price
        else:
            item_info["price"] = product.price
            
        return item_info, total_price, total_quantity
            
    except (ValueError, SQLAlchemyError) as e:
        db.rollback()
        logger.error(f"更新购物车商品数量失败: {e}")
        raise

def remove_cart_items(db: Session, user_id: str, cart_item_ids: List[str]) -> Tuple[float, int]:
    """
    删除购物车中的商品
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        cart_item_ids: 购物车项ID列表
        
    Returns:
        (购物车总价, 购物车总数量)
        
    Raises:
        SQLAlchemyError: 数据库操作失败
    """
    try:
        # 获取购物车
        cart = get_or_create_cart(db, user_id)
        
        # 删除购物车项
        if cart_item_ids:
            db.query(CartProducts).filter(
                CartProducts.cart_id == cart.id,
                CartProducts.id.in_(cart_item_ids)
            ).delete(synchronize_session=False)
            
            # 提交事务
            db.commit()
            
        # 获取购物车总价和总数量
        total_price = 0
        total_quantity = 0
        
        cart_items = db.query(CartProducts).filter(CartProducts.cart_id == cart.id).all()
        
        for item in cart_items:
            if item.product_type == 0:
                product = db.query(TeaProducts).filter(TeaProducts.id == item.product_id).first()
                if product and item.selected:
                    total_price += product.price * item.quantity
            else:
                product = db.query(UserCustomTea).filter(UserCustomTea.id == item.product_id).first()
                if product and item.selected:
                    total_price += product.price * item.quantity
                    
            total_quantity += item.quantity
            
        return total_price, total_quantity
        
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"删除购物车商品失败: {e}")
        raise

def clear_cart(db: Session, user_id: str) -> bool:
    """
    清空购物车
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        操作是否成功
        
    Raises:
        SQLAlchemyError: 数据库操作失败
    """
    try:
        # 获取购物车
        cart = get_or_create_cart(db, user_id)
        
        # 删除所有购物车项
        db.query(CartProducts).filter(CartProducts.cart_id == cart.id).delete(synchronize_session=False)
        
        # 提交事务
        db.commit()
        
        return True
        
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"清空购物车失败: {e}")
        raise

def update_cart_item_selection(db: Session, user_id: str, cart_item_ids: List[str], selected: bool) -> Tuple[List[str], float]:
    """
    更新购物车商品选择状态
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        cart_item_ids: 购物车项ID列表
        selected: 是否选中
        
    Returns:
        (选中的购物车项ID列表, 选中商品的总价)
        
    Raises:
        SQLAlchemyError: 数据库操作失败
    """
    try:
        # 获取购物车
        cart = get_or_create_cart(db, user_id)
        
        # 更新选择状态
        if cart_item_ids:
            db.query(CartProducts).filter(
                CartProducts.cart_id == cart.id,
                CartProducts.id.in_(cart_item_ids)
            ).update({"selected": selected}, synchronize_session=False)
        else:
            # 如果没有指定ID，则更新所有商品
            db.query(CartProducts).filter(
                CartProducts.cart_id == cart.id
            ).update({"selected": selected}, synchronize_session=False)
            
        # 提交事务
        db.commit()
        
        # 获取选中的商品列表和总价
        selected_items = []
        selected_total_price = 0
        
        cart_items = db.query(CartProducts).filter(
            CartProducts.cart_id == cart.id,
            CartProducts.selected == True
        ).all()
        
        for item in cart_items:
            selected_items.append(item.id)
            
            if item.product_type == 0:
                product = db.query(TeaProducts).filter(TeaProducts.id == item.product_id).first()
                if product:
                    selected_total_price += product.price * item.quantity
            else:
                product = db.query(UserCustomTea).filter(UserCustomTea.id == item.product_id).first()
                if product:
                    selected_total_price += product.price * item.quantity
                    
        return selected_items, selected_total_price
        
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"更新购物车商品选择状态失败: {e}")
        raise

def get_cart(db: Session, user_id: str) -> Dict[str, Any]:
    """
    获取购物车列表
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        包含购物车商品列表、总价和总数量的字典
        
    Raises:
        SQLAlchemyError: 数据库操作失败
    """
    try:
        # 获取或创建购物车
        cart = get_or_create_cart(db, user_id)
        
        # 获取购物车项
        cart_items = db.query(CartProducts).filter(CartProducts.cart_id == cart.id).all()
        
        items = []
        total_price = 0
        total_quantity = 0
        
        for cart_item in cart_items:
            item_data = {}
            
            # 根据商品类型获取商品信息
            if cart_item.product_type == 0:
                # 普通茶品
                product = db.query(TeaProducts).filter(TeaProducts.id == cart_item.product_id).first()
                if product:
                    item_price = product.price
                    item_data = {
                        "id": cart_item.id,
                        "productId": product.id,
                        "productName": product.name,
                        "price": product.price,
                        "quantity": cart_item.quantity,
                        "image": product.image,
                        "isVip": False,  # 假设普通茶品不是会员专享
                        "stock": product.stock,
                        "selected": cart_item.selected,
                        "productType": 0
                    }
                    
                    # 计算总价和总数量
                    if cart_item.selected:
                        total_price += item_price * cart_item.quantity
                    total_quantity += cart_item.quantity
            else:
                # 定制茶品
                product = db.query(UserCustomTea).filter(UserCustomTea.id == cart_item.product_id).first()
                if product:
                    item_price = product.price
                    item_data = {
                        "id": cart_item.id,
                        "productId": product.id,
                        "productName": product.name,
                        "price": product.price,
                        "quantity": cart_item.quantity,
                        "image": product.image if hasattr(product, 'image') else "",
                        "isVip": False,  # 假设定制茶品不是会员专享
                        "stock": 999,  # 假设定制茶品库存充足
                        "selected": cart_item.selected,
                        "productType": 1
                    }
                    
                    # 计算总价和总数量
                    if cart_item.selected:
                        total_price += item_price * cart_item.quantity
                    total_quantity += cart_item.quantity
                    
            if item_data:
                items.append(item_data)
                
        return {
            "items": items,
            "totalPrice": total_price,
            "totalQuantity": total_quantity
        }
        
    except SQLAlchemyError as e:
        logger.error(f"获取购物车列表失败: {e}")
        raise 
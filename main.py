# -*- coding: utf-8 -*-
# Desc: FastAPI 服务端代码

from fastapi import FastAPI, Depends, HTTPException, status, Request
from fastapi.security import  HTT<PERSON><PERSON>earer
from datetime import datetime, timedelta
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional
from jose import jwt, J<PERSON><PERSON><PERSON><PERSON>

from sqlalchemy.orm import Session
from sql import get_db
from sql.db_model import WechatUsers
import services
from core.schemas import WeChatLogin, RefreshToken, CartItemAdd, CartItemUpdate, CartItemRemove, CartItemSelect
from core.security import create_access_token, create_refresh_token, JWT_SECRET_KEY, ALGORITHM
import logging
import json

import sys
from pathlib import Path

app = FastAPI(
    title="茶品体质分析API",
    description="茶品体质分析系统后端API",
    version="1.0.0",
    openapi_url="/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置安全方案
security = HTTPBearer()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 用户身份验证依赖函数
async def get_current_user_id(
    token = Depends(security),
    db: Session = Depends(get_db)
) -> str:
    """
    从请求头的Authorization Token中获取当前用户ID
    
    Args:
        token: JWT令牌，从请求头获取
        db: 数据库会话
        
    Returns:
        用户ID字符串
        
    Raises:
        HTTPException: 如果令牌无效或过期
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # 解析JWT令牌
        jwt_token = token.credentials
        payload = jwt.decode(jwt_token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("sub")
        
        if user_id is None:
            raise credentials_exception
            
        # 验证用户是否存在
        user = db.query(WechatUsers).filter(WechatUsers.id == user_id).first()
        if not user:
            raise credentials_exception
            
        return user_id
    except JWTError:
        raise credentials_exception

@app.post("/wechat/login")
async def wechat_login(request: Request, wechat_data: WeChatLogin, db: Session = Depends(get_db)):
    """
    微信小程序登录接口

    Args:
        request: HTTP请求对象，用于获取客户端IP
        wechat_data: 包含微信授权码的数据
        db: 数据库会话

    Returns:
        登录成功的响应，包含token和用户信息
    """
    # 记录登录尝试和请求信息
    client_ip = request.client.host if request.client else "未知IP"
    logger.info(f"微信小程序登录尝试: code={wechat_data.code[:10]}..., IP={client_ip}")
    
    # 如果没有提供客户端IP，从请求中获取
    if not wechat_data.client_ip:
        wechat_data.client_ip = client_ip

    try:
        # 1. 通过小程序授权码获取用户信息（session_key 和 openid）
        token_data = await services.wechat_service.wechat_mini_program_api.get_access_token(wechat_data.code)
        
        # 记录服务器返回的 session_key 和 openid 信息
        logger.info(f"获取到的openid: {token_data.get('openid')[:5]}***, session_key长度: {len(token_data.get('session_key', ''))}")
        
        # 检查是否提供了加密数据
        if wechat_data.encryptedData and wechat_data.iv:
            logger.info("前端提供了encryptedData和iv，尝试解密获取完整用户信息")
        else:
            logger.info("前端未提供encryptedData和iv，将使用基本用户信息")

        # 2. 获取用户信息
        user_info = await services.wechat_service.wechat_mini_program_api.get_user_info(
            token_data['session_key'],
            token_data['openid'],
            wechat_data.encryptedData,
            wechat_data.iv
        )
        
        # 添加其他可能有用的信息
        if wechat_data.app_version:
            user_info['app_version'] = wechat_data.app_version
        
        user_info['last_ip'] = wechat_data.client_ip or client_ip
        
        # 记录获取到的用户信息
        if user_info.get('raw_data'):
            logger.info(f"获取到的解密用户数据: {json.dumps(user_info.get('raw_data'), ensure_ascii=False)[:200]}...")
            # 保留原始数据用于分析，但移除避免传递过多数据
            raw_data = user_info.pop('raw_data', None)
        else:
            logger.info("未获取到解密用户数据，使用基本用户信息")
            
        logger.info(f"处理后的用户信息: {json.dumps(user_info, ensure_ascii=False)}")

        # 3. 查找或创建用户
        db_user = None

        # 优先通过unionid查找用户（如果有的话）
        if user_info.get('unionid'):
            db_user = services.get_user_by_wechat_unionid(db, user_info['unionid'])
            logger.info(f"通过unionid查找用户: {db_user is not None}")

        # 如果没有找到，通过openid查找
        if not db_user:
            db_user = services.get_user_by_wechat_openid(db, user_info['openid'])
            logger.info(f"通过openid查找用户: {db_user is not None}")

        # 如果用户不存在，创建新用户
        if not db_user:
            logger.info(f"创建新的微信小程序用户: openid={user_info['openid'][:5]}***")
            db_user = services.create_wechat_user(db, user_info)
        else:
            # 更新现有用户的微信信息
            logger.info(f"更新现有用户的微信信息: user_id={db_user.id}")
            db_user = services.update_user_wechat_info(db, db_user, user_info)

        # 4. 生成JWT token和refresh token
        access_token = create_access_token(data={"sub": str(db_user.id)})
        refresh_token = create_refresh_token(data={"sub": str(db_user.id)})

        # 保存刷新令牌到用户记录
        db_user.refresh_token = refresh_token
        db_user.refresh_token_expires_at = datetime.utcnow() + timedelta(days=30)
        db.commit()

        # token有效期（秒）
        expires_in = 7200  # 2小时

        logger.info(f"微信小程序登录成功: user_id={db_user.id}, nickname={db_user.nickname}")

        # 构建API响应
        return {
            "code": 200,
            "success": True,
            "data": {
                "token": access_token,
                "refreshToken": refresh_token,
                "expiresIn": expires_in,
                "userInfo": {
                    "id": db_user.id,
                    "nickname": db_user.nickname,
                    "avatar": db_user.headimgurl or '',
                    "vipLevel": getattr(db_user, 'is_member', False) and 1 or 0,
                    "registerDate": db_user.created_at.isoformat() if db_user.created_at else datetime.now().isoformat(),
                    "isAuthorized": db_user.user_info_authorized
                }
            },
            "message": "登录成功"
        }

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"微信小程序登录过程中发生未知错误: {e}")
        return {
            "code": 500, 
            "success": False,
            "data": None,
            "message": f"微信小程序登录失败，请重试: {str(e)}"
        }

@app.post("/auth/refresh-token")
async def refresh_token(refresh_data: RefreshToken, db: Session = Depends(get_db)):
    """
    刷新令牌接口
    
    Args:
        refresh_data: 包含刷新令牌的数据
        db: 数据库会话
        
    Returns:
        返回新的访问令牌
    """
    try:
        # 验证刷新令牌
        payload = jwt.decode(refresh_data.refreshToken, JWT_SECRET_KEY, algorithms=[ALGORITHM])
        
        # 检查令牌类型
        if payload.get("type") != "refresh":
            return {
                "code": 401,
                "success": False,
                "data": None,
                "message": "无效的刷新令牌类型"
            }
        
        # 获取用户ID
        user_id = payload.get("sub")
        if not user_id:
            return {
                "code": 401,
                "success": False,
                "data": None,
                "message": "无效的刷新令牌"
            }
            
        # 从数据库验证令牌
        # 这一步很重要，确保令牌未被撤销且仍然有效
        user = db.query(WechatUsers).filter(WechatUsers.id == user_id).first()
        if not user or user.refresh_token != refresh_data.refreshToken or user.refresh_token_expires_at < datetime.utcnow():
            return {
                "code": 401,
                "success": False,
                "data": None,
                "message": "刷新令牌已失效，请重新登录"
            }
        
        # 创建新的访问令牌
        new_access_token = create_access_token(data={"sub": user_id})
        
        # 更新最后登录时间
        user.last_login_at = datetime.utcnow()
        db.commit()
        
        # 返回新令牌
        return {
            "code": 200,
            "success": True,
            "data": {
                "token": new_access_token,
                "expiresIn": 7200  # 2小时
            },
            "message": "刷新Token成功"
        }
    except JWTError:
        return {
            "code": 401,
            "success": False,
            "data": None,
            "message": "刷新令牌无效或已过期"
        }
    except Exception as e:
        logger.error(f"刷新Token过程中发生未知错误: {e}")
        return {
            "code": 500,
            "success": False,
            "data": None,
            "message": "刷新Token失败，请重新登录"
        }



@app.get("/dev/token")
async def generate_token(use_real_user: bool = False, custom_id: str = None, db: Session = Depends(get_db)):
    """
    仅用于开发环境：生成JWT令牌
    
    警告：此接口仅应在开发环境中使用，生产环境应禁用
    
    Args:
        use_real_user: 是否使用数据库中的真实用户
        custom_id: 自定义用户ID，当use_real_user=False时生效
        db: 数据库会话
        
    Returns:
        包含JWT令牌的响应
    """
    if use_real_user:
        # 获取数据库中的第一个用户
        user = db.query(WechatUsers).first()
        
        if not user:
            return {
                "code": 404,
                "success": False,
                "message": "数据库中没有用户",
                "data": None
            }
        
        # 创建访问令牌
        access_token = create_access_token(data={"sub": user.id})
        
        return {
            "code": 200,
            "success": True,
            "message": "真实用户令牌生成成功",
            "data": {
                "token": access_token,
                "token_type": "bearer",
                "user_id": user.id,
                "user_nickname": user.nickname
            }
        }
    else:
        # 使用自定义ID或默认测试ID
        user_id = custom_id or "test_user_id"
        
        # 创建访问令牌
        access_token = create_access_token(data={"sub": user_id})
        
        return {
            "code": 200,
            "success": True,
            "message": "测试令牌生成成功",
            "data": {
                "token": access_token,
                "token_type": "bearer",
                "user_id": user_id
            }
        }

@app.get("/report/overview")
async def report_overview(
    limit: int = 30, 
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    体质报告总览接口
    
    获取用户体质报告总览数据，包括最新体质分析结果和历史报告列表
    
    Args:
        limit: 历史报告返回数量，默认为5
        user_id: 当前用户ID，从令牌中获取
        db: 数据库会话
        
    Returns:
        包含报告总览数据的响应
    """
    try:
        report_data = await services.get_report_overview(db, user_id, limit)
        
        if report_data["has_completed_test"]:
            return {
                "code": 200,
                "success": True,
                "message": "获取体质报告总览成功",
                "data": report_data
            }
        else:
            return {
                "code": 200,
                "success": True,
                "message": "尚无体质报告数据",
                "data": report_data
            }
    except Exception as e:
        logger.error(f"获取体质报告总览失败: {e}")
        return {
            "code": 500,
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }

@app.get("/report/detail")
async def report_detail(
    report_id: str,
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    体质报告详情接口
    
    获取用户特定体质报告的详细信息
    
    Args:
        report_id: 报告ID
        user_id: 当前用户ID，从令牌中获取
        db: 数据库会话
        
    Returns:
        包含报告详情数据的响应
    """
    try:
        report_data = await services.get_report_detail(db, report_id, user_id)
        
        if not report_data:
            return {
                "code": 404,
                "success": False,
                "message": "未找到指定报告",
                "data": None
            }
            
        return {
            "code": 200,
            "success": True,
            "message": "获取体质报告详情成功",
            "data": report_data
        }
    except Exception as e:
        logger.error(f"获取体质报告详情失败: {e}")
        return {
            "code": 500,
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }

@app.get("/report/tea-recommend")
async def tea_recommend(
    report_id: str,
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    体质报告推荐茶品接口
    
    获取基于用户体质报告的个性化茶品推荐
    
    Args:
        report_id: 体质报告ID
        user_id: 当前用户ID，从令牌中获取
        db: 数据库会话
        
    Returns:
        包含茶品推荐数据的响应
    """
    try:
        recommend_data = await services.get_tea_recommend(db, report_id, user_id)
        
        if not recommend_data:
            return {
                "code": 404,
                "success": False,
                "message": "未找到指定报告",
                "data": None
            }
            
        return {
            "code": 200,
            "success": True,
            "message": "获取茶品推荐成功",
            "data": recommend_data
        }
    except Exception as e:
        logger.error(f"获取茶品推荐失败: {e}")
        return {
            "code": 500,
            "success": False,
            "message": f"服务器错误: {str(e)}",
            "data": None
        }

@app.get("/product/all")
def get_all_products(
    page: int = 1, 
    page_size: int = 20, 
    user_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    获取所有茶产品列表，支持分页
    
    Args:
        page: 页码，默认为1
        page_size: 每页数量，默认为20
        user_id: 用户ID，如果提供则获取个性化推荐
        db: 数据库会话
        
    Returns:
        包含产品列表、分类和分页信息的响应
    """
    try:
        result = services.TeaProductService.get_all_products(
            db=db,
            page=page,
            page_size=page_size,
            user_id=user_id
        )
        
        return {
            "code": 200,
            "success": True,
            "data": result,
            "message": "获取产品列表成功"
        }
    except HTTPException as e:
        return {
            "code": e.status_code,
            "success": False,
            "data": None,
            "message": e.detail
        }
    except Exception as e:
        logger.error(f"获取产品列表时发生未知错误: {e}")
        return {
            "code": 500,
            "success": False,
            "data": None,
            "message": f"获取产品列表失败: {str(e)}"
        }

# 购物车接口
@app.get("/cart")
async def get_cart_list(
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    获取购物车列表
    
    Args:
        user_id: 当前用户ID
        db: 数据库会话
        
    Returns:
        购物车数据，包含商品列表、总价和总数量
    """
    try:
        # 调用服务获取购物车数据
        cart_data = services.get_cart(db, user_id)
        
        return {
            "code": 0,
            "message": "success",
            "data": cart_data
        }
    except Exception as e:
        logger.error(f"获取购物车列表失败: {e}")
        return {
            "code": 500,
            "message": f"获取购物车列表失败: {str(e)}",
            "data": None
        }

@app.post("/cart/add")
async def add_to_cart(
    cart_item: CartItemAdd,
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    添加商品到购物车
    
    Args:
        cart_item: 添加到购物车的商品信息
        user_id: 当前用户ID
        db: 数据库会话
        
    Returns:
        添加结果，包含购物车项ID和总数量
    """
    try:
        # 调用服务添加商品到购物车
        cart_item_id, total_quantity = services.add_cart_item(
            db, 
            user_id, 
            cart_item.teaProductId, 
            cart_item.quantity,
            cart_item.productType
        )
        
        return {
            "code": 0,
            "message": "添加成功",
            "data": {
                "cartItemId": cart_item_id,
                "totalQuantity": total_quantity
            }
        }
    except ValueError as e:
        logger.warning(f"添加商品到购物车参数错误: {e}")
        return {
            "code": 400,
            "message": str(e),
            "data": None
        }
    except Exception as e:
        logger.error(f"添加商品到购物车失败: {e}")
        return {
            "code": 500,
            "message": f"添加商品到购物车失败: {str(e)}",
            "data": None
        }

@app.put("/cart/update")
async def update_cart(
    cart_item: CartItemUpdate,
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    更新购物车商品数量
    
    Args:
        cart_item: 更新的购物车商品信息
        user_id: 当前用户ID
        db: 数据库会话
        
    Returns:
        更新结果，包含购物车项、总价和总数量
    """
    try:
        # 调用服务更新购物车商品数量
        item_info, total_price, total_quantity = services.update_cart_item(db, user_id, cart_item.cartItemId, cart_item.quantity)
        
        return {
            "code": 0,
            "message": "更新成功",
            "data": {
                "item": item_info,
                "totalPrice": total_price,
                "totalQuantity": total_quantity
            }
        }
    except ValueError as e:
        logger.warning(f"更新购物车商品数量参数错误: {e}")
        return {
            "code": 400,
            "message": str(e),
            "data": None
        }
    except Exception as e:
        logger.error(f"更新购物车商品数量失败: {e}")
        return {
            "code": 500,
            "message": f"更新购物车商品数量失败: {str(e)}",
            "data": None
        }

@app.delete("/cart/remove")
async def remove_from_cart(
    cart_item: CartItemRemove,
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    删除购物车商品
    
    Args:
        cart_item: 要删除的购物车商品ID列表
        user_id: 当前用户ID
        db: 数据库会话
        
    Returns:
        删除结果，包含总价和总数量
    """
    try:
        # 调用服务删除购物车商品
        total_price, total_quantity = services.remove_cart_items(db, user_id, cart_item.cartItemIds)
        
        return {
            "code": 0,
            "message": "删除成功",
            "data": {
                "totalPrice": total_price,
                "totalQuantity": total_quantity
            }
        }
    except ValueError as e:
        logger.warning(f"删除购物车商品参数错误: {e}")
        return {
            "code": 400,
            "message": str(e),
            "data": None
        }
    except Exception as e:
        logger.error(f"删除购物车商品失败: {e}")
        return {
            "code": 500,
            "message": f"删除购物车商品失败: {str(e)}",
            "data": None
        }

@app.delete("/cart/clear")
async def clear_cart(
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    清空购物车
    
    Args:
        user_id: 当前用户ID
        db: 数据库会话
        
    Returns:
        清空结果
    """
    try:
        # 调用服务清空购物车
        services.clear_cart(db, user_id)
        
        return {
            "code": 0,
            "message": "清空成功",
            "data": None
        }
    except Exception as e:
        logger.error(f"清空购物车失败: {e}")
        return {
            "code": 500,
            "message": f"清空购物车失败: {str(e)}",
            "data": None
        }

@app.put("/cart/select")
async def update_cart_selection(
    cart_item: CartItemSelect,
    user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """
    更新购物车商品选择状态
    
    Args:
        cart_item: 要更新选择状态的购物车商品ID列表和选择状态
        user_id: 当前用户ID
        db: 数据库会话
        
    Returns:
        更新结果，包含选中商品ID列表和选中商品总价
    """
    try:
        # 调用服务更新购物车商品选择状态
        selected_items, selected_total_price = services.update_cart_item_selection(db, user_id, cart_item.cartItemIds, cart_item.selected)
        
        return {
            "code": 0,
            "message": "更新成功",
            "data": {
                "selectedItems": selected_items,
                "selectedTotalPrice": selected_total_price
            }
        }
    except ValueError as e:
        logger.warning(f"更新购物车商品选择状态参数错误: {e}")
        return {
            "code": 400,
            "message": str(e),
            "data": None
        }
    except Exception as e:
        logger.error(f"更新购物车商品选择状态失败: {e}")
        return {
            "code": 500,
            "message": f"更新购物车商品选择状态失败: {str(e)}",
            "data": None
        }